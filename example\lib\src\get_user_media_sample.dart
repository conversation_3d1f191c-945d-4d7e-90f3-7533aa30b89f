import 'dart:core';
import 'dart:io';
import 'dart:async';
import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_webrtc_plus/flutter_webrtc_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'package:dio/dio.dart';
import 'dart:async';
import 'package:flutter_webrtc_plus/PixeBeautyDialog.dart';
import 'package:flutter_webrtc_plus/pixelfree_platform_interface.dart';
import 'stream_settings.dart';
import 'package:shared_preferences/shared_preferences.dart';
 // 添加WebView頁面的導入
import 'package:flutter_inappwebview/flutter_inappwebview.dart'; // 添加InAppWebView的導入

/*
 * getUserMedia sample
 */
class GetUserMediaSample extends StatefulWidget {
  static String tag = 'get_usermedia_sample';
  final StreamSettings streamSettings;

  const GetUserMediaSample({
    Key? key,
    required this.streamSettings,
  }) : super(key: key);

  @override
  _GetUserMediaSampleState createState() => _GetUserMediaSampleState();
}

class _GetUserMediaSampleState extends State<GetUserMediaSample> {
  MediaStream? _localStream;
  final _localRenderer = RTCVideoRenderer();
  bool _inCalling = false;
  bool _isTorchOn = false;
  MediaRecorder? _mediaRecorder;
  bool _isStreaming = false;
  RTCPeerConnection? _pc;
  String? _resourceUrl;
  
  // 重新配置 Dio
  late final Dio _dio;
  // 使用固定的推流地址
  late String _whipEndpoint;
  
  // 添加狀態變量
  String _currentResolution = '640x480';
  int _currentFps = 0;
  String _streamingStatus = '未連接';
  bool _isRetrying = false;
  int _retryCount = 0;
  static const int maxRetries = 3;
  // 添加碼流統計變量
  double _currentBitrate = 0.0;
  int _packetsLost = 0;
  int _packetsSent = 0;
  Timer? _statsTimer;
  // 添加上一次統計的字節數
  int _lastBytesSent = 0;
  DateTime? _lastStatsTime;
  // 添加幀率統計變量
  int _lastFramesSent = 0;
  int _lastFramesDropped = 0;

  // 添加編碼參數變量
  late String _selectedResolution;
  late int _selectedFps;
  late int _selectedBitrate;
  late int _selectedKeyframeInterval;
  late bool _enableBFrame;
  late String _selectedProfile;
  late String _selectedEncodingMode;

  // 添加碼率平滑計算相關變量
  static const int _bitrateHistorySize = 5;  // 保存最近5次的碼率數據
  final List<int> _bitrateHistory = [];
  double _lastBitrate = 0.0;  // 改為 double
  static const int _smoothingFactor = 3;  // 平滑因子，值越大平滑效果越明顯

  // 修改網絡質量監控變量
  double _networkQuality = 0.0;  // 0-1 表示網絡質量
  double _rtt = 0.0;  // 往返時間
  double _packetLossRate = 0.0;  // 丟包率
  double _jitter = 0.0;  // 抖動
  String _networkType = '未知';  // 網絡類型
  double _bandwidth = 0.0;  // 帶寬估計
  double _availableBitrate = 0.0;  // 可用碼率
  double _targetBitrate = 0.0;  // 目標碼率
  double _actualBitrate = 0.0;  // 實際碼率
  int _framesDropped = 0;  // 丟幀數
  int _framesSent = 0;  // 發送幀數
  double _frameRate = 0.0;  // 實際幀率
  int _resolutionWidth = 0;  // 實際分辨率寬
  int _resolutionHeight = 0;  // 實際分辨率高
  String _codec = '未知';  // 使用的編解碼器
  int _nackCount = 0;  // NACK 計數
  int _pliCount = 0;  // PLI 計數
  int _firCount = 0;  // FIR 計數
  int _qpSum = 0;  // QP 總和
  int _qpCount = 0;  // QP 計數
  double _avgQP = 0.0;  // 平均 QP 值

  // 添加編碼器狀態監控變量
  String _encoderStatus = '正常';
  int _consecutiveLowBitrateCount = 0;
  static const int _maxLowBitrateCount = 5;  // 連續5次低碼率後觸發調整
  static const double _minBitrateRatio = 0.5;  // 實際碼率低於目標碼率50%時認為過低

  bool get _isRec => _mediaRecorder != null;

  List<MediaDeviceInfo>? _mediaDevicesList;

  bool _isFrontCamera = true;  // 添加相機方向標記
  bool _hasNavigatedToWebView = false;  // 添加跳轉標誌，避免重複跳轉
  bool _showFloatingWebView = false;  // 控制懸浮 WebView 的顯示
  InAppWebViewController? _floatingWebViewController;  // WebView 控制器
  
  // 添加退出狀態管理變量
  bool _isExiting = false;  // 是否正在退出
  double _exitProgress = 0.0;  // 退出進度 (0.0 到 1.0)
  String _exitStatusText = '';  // 退出狀態文字

  @override
  void initState() {
    super.initState();
    // 初始化推流參數
    _selectedResolution = widget.streamSettings.resolution;
    _selectedFps = widget.streamSettings.fps;
    _selectedBitrate = widget.streamSettings.bitrate;
    _selectedKeyframeInterval = widget.streamSettings.keyframeInterval;
    _enableBFrame = widget.streamSettings.enableBFrame;
    _selectedProfile = widget.streamSettings.profile;
    _selectedEncodingMode = widget.streamSettings.encodingMode;
    
    // 使用 StreamSettings 中的推流地址
    _whipEndpoint = widget.streamSettings.streamUrl;
    
    initRenderers();
    _makeCall(); // 自動開啟視頻預覽
    navigator.mediaDevices.ondevicechange = (event) async {
      print('++++++ ondevicechange ++++++');
      _mediaDevicesList = await navigator.mediaDevices.enumerateDevices();
    };
    
    // 初始化 Dio
    _dio = Dio(BaseOptions(
      connectTimeout: Duration(seconds: 10),
      sendTimeout: Duration(seconds: 10),
      receiveTimeout: Duration(seconds: 10),
      headers: {
        'Connection': 'keep-alive',
        'User-Agent': 'Flutter-WebRTC-Client',
      },
    ));
    
    // 添加攔截器
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) {
        debugPrint('Dio 請求: ${options.uri}');
        debugPrint('Dio 請求頭: ${options.headers}');
        return handler.next(options);
      },
      onResponse: (response, handler) {
        debugPrint('Dio 響應: ${response.statusCode}');
        debugPrint('Dio 響應頭: ${response.headers}');
        return handler.next(response);
      },
      onError: (error, handler) {
        debugPrint('Dio 錯誤: ${error.type}');
        debugPrint('Dio 錯誤信息: ${error.message}');
        if (error.response != null) {
          debugPrint('Dio 錯誤響應: ${error.response?.statusCode}');
          debugPrint('Dio 錯誤響應頭: ${error.response?.headers}');
        }
        return handler.next(error);
      },
    ));

    // 啟動統計定時器
    _startStatsTimer();

          // 應用保存的美顏參數
      _applySavedBeautySettings();
  }

  @override
  void deactivate() {
    super.deactivate();
    if (_inCalling) {
      _hangUp();
    }
    if (_localRenderer != null) {
      _localRenderer.srcObject = null;
    }
  }

  void initRenderers() async {
    try {
      await _localRenderer.initialize();
    } catch (e) {
      print('初始化渲染器失敗: $e');
    }
  }

  // Platform messages are asynchronous, so we initialize in an async method.
  Future<void> _makeCall() async {
    if (!mounted) return;

    // 使用選擇的分辨率和幀率
    final resolution = _selectedResolution.split('x');
    final width = int.parse(resolution[0]);
    final height = int.parse(resolution[1]);
    
    final mediaConstraints = <String, dynamic>{
      'audio': true,
      'video': <String, dynamic>{
        'mandatory': <String, dynamic>{
          'minWidth': width,
          'minHeight': height,
        },
        'facingMode': 'user'
      }
    };

    try {
      var stream = await navigator.mediaDevices.getUserMedia(mediaConstraints);
      if (!mounted) {
        stream.dispose();
        return;
      }
      
      _mediaDevicesList = await navigator.mediaDevices.enumerateDevices();
      _localStream = stream;
      
      if (_localRenderer != null && mounted) {
        _localRenderer.srcObject = _localStream;
      }
      
      // 添加幀率監控
      final videoTrack = stream.getVideoTracks().first;
      videoTrack.onEnded = () {
        if (!mounted) return;
        print('Video track ended');
      };
      
      // 打印視頻軌道信息和設置
      final settings = videoTrack.getSettings();
      print('Video track settings: $settings');
      print('Selected frame rate: $_selectedFps');
      print('Actual frame rate: ${settings['frameRate']}');
      
      // 設置視頻軌道約束
      await videoTrack.applyConstraints({
        'mandatory': {
          'minFrameRate': _selectedFps,
          'maxFrameRate': _selectedFps,
        },
        'optional': [
          {'frameRate': _selectedFps},
        ],
      });
      
      if (mounted) {
        setState(() {
          _inCalling = true;
        });
      }
    } catch (e) {
      print(e.toString());
      if (!mounted) return;
      setState(() {
        _inCalling = false;
      });
    }
  }

  void _hangUp() async {
    try {
      if (kIsWeb) {
        _localStream?.getTracks().forEach((track) => track.stop());
      }
      await _localStream?.dispose();
      if (_localRenderer != null && mounted) {
        _localRenderer.srcObject = null;
      }
      if (mounted) {
        setState(() {
          _inCalling = false;
        });
      }
    } catch (e) {
      print(e.toString());
    }
  }

  void _startRecording() async {
    if (_localStream == null) throw Exception('Stream is not initialized');
    if (Platform.isIOS) {
      print('Recording is not available on iOS');
      return;
    }
    // TODO(rostopira): request write storage permission
    final storagePath = await getExternalStorageDirectory();
    if (storagePath == null) throw Exception('Can\'t find storagePath');

    final filePath = storagePath.path + '/webrtc_sample/test.mp4';
    _mediaRecorder = MediaRecorder();
    if (mounted) {
      setState(() {});
    }

    final videoTrack = _localStream!
        .getVideoTracks()
        .firstWhere((track) => track.kind == 'video');
    await _mediaRecorder!.start(
      filePath,
      videoTrack: videoTrack,
    );
  }

  void _stopRecording() async {
    await _mediaRecorder?.stop();
    if (mounted) {
      setState(() {
        _mediaRecorder = null;
      });
    }
  }

  void _toggleTorch() async {
    if (_localStream == null) throw Exception('Stream is not initialized');

    final videoTrack = _localStream!
        .getVideoTracks()
        .firstWhere((track) => track.kind == 'video');
    final has = await videoTrack.hasTorch();
    if (has) {
      print('[TORCH] Current camera supports torch mode');
      if (mounted) {
        setState(() => _isTorchOn = !_isTorchOn);
      }
      await videoTrack.setTorch(_isTorchOn);
      print('[TORCH] Torch state is now ${_isTorchOn ? 'on' : 'off'}');
    } else {
      print('[TORCH] Current camera does not support torch mode');
    }
  }

  void setZoom(double zoomLevel) async {
    if (_localStream == null) throw Exception('Stream is not initialized');
    // await videoTrack.setZoom(zoomLevel); //Use it after published webrtc_interface 1.1.1

    // before the release, use can just call native method directly.
    final videoTrack = _localStream!
        .getVideoTracks()
        .firstWhere((track) => track.kind == 'video');
    await WebRTC.invokeMethod('mediaStreamTrackSetZoom',
        <String, dynamic>{'trackId': videoTrack.id, 'zoomLevel': zoomLevel});
  }

  void _toggleCamera() async {
    if (_localStream == null) throw Exception('Stream is not initialized');

    final videoTrack = _localStream!
        .getVideoTracks()
        .firstWhere((track) => track.kind == 'video');
    await Helper.switchCamera(videoTrack);
    
    // 更新相機方向
    if (mounted) {
      setState(() {
        _isFrontCamera = !_isFrontCamera;
      });
    }
  }

  void _captureFrame() async {
    if (_localStream == null) throw Exception('Stream is not initialized');

    final videoTrack = _localStream!
        .getVideoTracks()
        .firstWhere((track) => track.kind == 'video');
    final frame = await videoTrack.captureFrame();
    await showDialog(
        context: context,
        builder: (context) => AlertDialog(
              content:
                  Image.memory(frame.asUint8List(), height: 720, width: 1280),
              actions: <Widget>[
                TextButton(
                  onPressed: Navigator.of(context, rootNavigator: true).pop,
                  child: Text('OK'),
                )
              ],
            ));
  }

  // 檢查網絡連接
  Future<bool> _checkNetworkConnection() async {
    try {
      final result = await InternetAddress.lookup('google.com');
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } on SocketException catch (_) {
      return false;
    }
  }

  // 修改重試推流方法
  Future<void> _retryStreaming() async {
    if (!mounted) return;
    
    debugPrint('開始重試推流，當前重試次數: $_retryCount');
    
    if (_retryCount >= maxRetries) {
      debugPrint('達到最大重試次數，停止重試');
      if (mounted) {
        setState(() {
          _streamingStatus = '連接失敗，請檢查網絡';
          _isRetrying = false;
        });
      }
      return;
    }

    _retryCount++;
    debugPrint('準備第 $_retryCount 次重試');
    
    if (mounted) {
      setState(() {
        _streamingStatus = '重試連接中...(${_retryCount}/$maxRetries)';
        _isRetrying = true;
      });
    }

    // 等待一段時間後重試
    await Future.delayed(Duration(seconds: 2));
    if (!mounted) return;
    
    debugPrint('開始執行重試連接');
    await _startStreaming();
  }

  // 修改連接狀態監聽設置
  void _setupConnectionStateListener() {
    _pc?.onConnectionState = (RTCPeerConnectionState state) {
      if (!mounted) return;
      
      print('Connection state changed: $state');
      
      try {
        if (mounted) {
          setState(() {
            switch (state) {
              case RTCPeerConnectionState.RTCPeerConnectionStateConnected:
                _streamingStatus = '推流中';
                _isStreaming = true;
                // 當推流狀態變為"推流中"時，顯示懸浮WebView
                _showFloatingWebViewOverlay();
                break;
              case RTCPeerConnectionState.RTCPeerConnectionStateDisconnected:
                _streamingStatus = '連接斷開';
                _isStreaming = false;
                break;
              case RTCPeerConnectionState.RTCPeerConnectionStateFailed:
                _streamingStatus = '連接失敗';
                _isStreaming = false;
                break;
              case RTCPeerConnectionState.RTCPeerConnectionStateClosed:
                _streamingStatus = '已關閉';
                _isStreaming = false;
                break;
              default:
                break;
            }
          });
        }
        
        // 處理重試邏輯（在setState之外）
        if (state == RTCPeerConnectionState.RTCPeerConnectionStateDisconnected ||
            state == RTCPeerConnectionState.RTCPeerConnectionStateFailed) {
          _retryStreaming();
        }
      } catch (e) {
        debugPrint('⚠️ PeerConnection狀態更新失敗: $e');
      }
    };

    _pc?.onIceConnectionState = (RTCIceConnectionState state) {
      if (!mounted) return;
      
      print('ICE connection state changed: $state');
      
      try {
        if (mounted) {
          setState(() {
            switch (state) {
              case RTCIceConnectionState.RTCIceConnectionStateConnected:
                _streamingStatus = '推流中';
                _isStreaming = true;
                // 當推流狀態變為"推流中"時，顯示懸浮WebView
                _showFloatingWebViewOverlay();
                break;
              case RTCIceConnectionState.RTCIceConnectionStateDisconnected:
                _streamingStatus = '連接斷開';
                _isStreaming = false;
                break;
              case RTCIceConnectionState.RTCIceConnectionStateFailed:
                _streamingStatus = '連接失敗';
                _isStreaming = false;
                break;
              case RTCIceConnectionState.RTCIceConnectionStateClosed:
                _streamingStatus = '已關閉';
                _isStreaming = false;
                break;
              default:
                break;
            }
          });
        }
        
        // 處理重試邏輯（在setState之外）
        if (state == RTCIceConnectionState.RTCIceConnectionStateDisconnected ||
            state == RTCIceConnectionState.RTCIceConnectionStateFailed) {
          _retryStreaming();
        }
      } catch (e) {
        debugPrint('⚠️ ICE連接狀態更新失敗: $e');
      }
    };
  }

  // 添加網絡診斷方法
  Future<bool> _diagnoseNetwork() async {
    debugPrint('開始網絡診斷...');
    
    try {
      // 檢查網絡接口
      debugPrint('檢查網絡接口...');
      final interfaces = await NetworkInterface.list(
        includeLinkLocal: false,
        type: InternetAddressType.IPv4,
      );
      
      if (interfaces.isEmpty) {
        debugPrint('未找到可用的網絡接口');
        return false;
      }
      
      debugPrint('可用的網絡接口:');
      for (var interface in interfaces) {
        debugPrint('- ${interface.name}: ${interface.addresses.map((a) => a.address).join(', ')}');
      }

      // 嘗試使用不同的方式連接
      debugPrint('嘗試不同的連接方式...');
      
      // 1. 嘗試使用 HTTPS
      try {
        debugPrint('嘗試 HTTPS 連接...');
        final response = await _dio.get(
          'https://www.baidu.com',
          options: Options(
            validateStatus: (status) {
              return status != null && status >= 200 && status < 300;
            },
          ),
        );
        if (response.statusCode == 200) {
          debugPrint('HTTPS 連接成功');
          return true;
        }
      } catch (e) {
        debugPrint('HTTPS 連接失敗: $e');
      }

      // 2. 嘗試使用 HTTP
      try {
        debugPrint('嘗試 HTTP 連接...');
        final response = await _dio.get(
          'http://www.baidu.com',
          options: Options(
            validateStatus: (status) {
              return status != null && status >= 200 && status < 300;
            },
          ),
        );
        if (response.statusCode == 200) {
          debugPrint('HTTP 連接成功');
          return true;
        }
      } catch (e) {
        debugPrint('HTTP 連接失敗: $e');
      }

      // 3. 嘗試使用 DNS 解析
      try {
        debugPrint('嘗試 DNS 解析...');
        final addresses = await InternetAddress.lookup('www.baidu.com');
        debugPrint('DNS 解析結果: ${addresses.map((a) => a.address).join(', ')}');
        return true;
      } catch (e) {
        debugPrint('DNS 解析失敗: $e');
      }

      debugPrint('所有連接方式都失敗');
      return false;
    } catch (e) {
      debugPrint('網絡診斷失敗: $e');
      return false;
    }
  }

  // 修改重置統計變量的方法
  void _resetStats() {
    setState(() {
      _currentBitrate = 0.0;
      _lastBitrate = 0.0;
      _bitrateHistory.clear();
      _lastBytesSent = 0;
      _lastStatsTime = null;
      _actualBitrate = 0.0;
      _targetBitrate = 0.0;
      _framesSent = 0;
      _framesDropped = 0;
      _frameRate = 0.0;
      _resolutionWidth = 0;
      _resolutionHeight = 0;
      _rtt = 0.0;
      _packetsLost = 0;
      _packetsSent = 0;
      _packetLossRate = 0.0;
      _networkQuality = 0.0;
      _codec = '未知';
      _nackCount = 0;
      _pliCount = 0;
      _firCount = 0;
      _qpSum = 0;
      _qpCount = 0;
      _avgQP = 0.0;
      _jitter = 0.0;
      _bandwidth = 0.0;
      _availableBitrate = 0.0;
      _encoderStatus = '正常';
      _consecutiveLowBitrateCount = 0;
    });
  }

  Future<void> _startStreaming() async {
    if (!mounted) return;
    
    debugPrint('開始推流，當前重試狀態: $_isRetrying');
    
    try {
      // 重置統計變量
      _resetStats();
      
      setState(() {
        _streamingStatus = '連接中...';
        _isRetrying = true;
      });

      // 啟動統計定時器
      _startStatsTimer();

      // 檢查網絡連接
      if (!await _diagnoseNetwork()) {
        if (!mounted) return;
        setState(() {
          _streamingStatus = '網絡連接異常，請檢查網絡';
          _isRetrying = false;
        });
        return;
      }

      if (_localStream == null) {
        debugPrint('本地流為空，重新獲取媒體流');
        // 使用選擇的分辨率和幀率
        final resolution = _selectedResolution.split('x');
        final width = int.parse(resolution[0]);
        final height = int.parse(resolution[1]);
        
        final mediaConstraints = <String, dynamic>{
          'audio': true,
          'video': <String, dynamic>{
            'mandatory': <String, dynamic>{
              'minWidth': width,
              'maxWidth': width,
              'minHeight': height,
              'maxHeight': height,
              'minFrameRate': _selectedFps,
              'maxFrameRate': _selectedFps,
            },
            'width': width,
            'height': height,
            'frameRate': _selectedFps,
            'facingMode': 'user'
          }
        };
        
        var stream = await navigator.mediaDevices.getUserMedia(mediaConstraints);
        if (!mounted) return;
        
        _localStream = stream;
        _localRenderer.srcObject = _localStream;
        
        // 添加幀率監控
        final videoTrack = stream.getVideoTracks().first;
        videoTrack.onEnded = () {
          if (!mounted) return;
          print('Video track ended');
        };
        
        // 打印視頻軌道信息
        print('Video track settings: ${videoTrack.getSettings()}');
        
        setState(() {
          _inCalling = true;
        });
      }

      // Create PeerConnection with encoding parameters
      debugPrint('創建 PeerConnection');
      final resolution = _selectedResolution.split('x');
      final width = int.parse(resolution[0]);
      final height = int.parse(resolution[1]);
      final pixels = width * height;
      
      // 根據分辨率動態調整碼率
      int targetBitrate = _selectedBitrate;
      
      if (pixels <= 640 * 480) {  // 480p
        targetBitrate = _selectedBitrate > 1000 ? _selectedBitrate : 1000;
      } else if (pixels <= 1280 * 720) {  // 720p
        targetBitrate = _selectedBitrate > 2500 ? _selectedBitrate : 2500;
      } else if (pixels <= 1920 * 1080) {  // 1080p
        targetBitrate = _selectedBitrate > 4000 ? _selectedBitrate : 4000;
      } else {  // 更高分辨率
        targetBitrate = _selectedBitrate > 6000 ? _selectedBitrate : 6000;
      }
      
      // 限制最大碼率
      targetBitrate = targetBitrate.clamp(500, 8000);
      
      // 根據選擇的編碼等級和模式設置 profile-level-id
      String profileLevelId;
      switch (_selectedProfile) {
        case 'baseline':
          profileLevelId = '42e01f';  // Baseline Profile Level 3.1
          break;
        case 'main':
          profileLevelId = '4d001f';  // Main Profile Level 3.1
          break;
        case 'high':
          profileLevelId = '64001f';  // High Profile Level 3.1
          break;
        default:
          profileLevelId = '42e01f';  // 默認使用 Baseline
      }
      
      // 優化編碼參數
      final Map<String, dynamic> codecParams = {
        'profile-level-id': profileLevelId,
        'level-asymmetry-allowed': '1',
        'packetization-mode': '1',
        'max-fr': _selectedFps.toString(),
        'max-fs': pixels.toString(),
        'max-mbps': (pixels * _selectedFps).toString(),
        'max-cpb': (targetBitrate * 1000).toString(),
        'max-dpb': (targetBitrate * 1000).toString(),
        'max-cmv': (targetBitrate * 1000).toString(),
        'max-br': (targetBitrate * 1000).toString(),
        'keyframe-interval': (_selectedKeyframeInterval * _selectedFps).toString(),
        'use-b-frames': _enableBFrame ? '1' : '0',
        'encoding-mode': _selectedEncodingMode.toLowerCase(),
      };
      
      debugPrint('編碼參數配置:');
      debugPrint('- 分辨率: ${width}x$height');
      debugPrint('- 目標碼率: ${targetBitrate}kbps');
      debugPrint('- 幀率: $_selectedFps');
      debugPrint('- 關鍵幀間隔: ${_selectedKeyframeInterval}秒');
      debugPrint('- 編碼模式: $_selectedEncodingMode');
      
      _pc = await createPeerConnection({
        'iceServers': [
          {'urls': 'stun:stun.l.google.com:19302'}
        ],
        'iceTransportPolicy': 'all',
        'sdpSemantics': 'unified-plan',
        'bundlePolicy': 'max-bundle',
        'rtcpMuxPolicy': 'require',
        'codecPreferences': [
          {
            'mimeType': 'video/H264',
            'clockRate': 90000,
            // 'parameters': codecParams
          }
        ]
      });

      // 設置連接狀態監聽
      _setupConnectionStateListener();

      // Add tracks to connection
      debugPrint('添加媒體軌道到連接');
      for (var track in _localStream!.getTracks()) {
        await _pc!.addTrack(track, _localStream!);
      }

      // 設置視頻軌道參數
      final videoTrack = _localStream!.getVideoTracks().first;
      try {
        // 使用 SDP 參數設置視頻軌道參數
        final Map<String, dynamic> videoConstraints = {
          'mandatory': {
            'minWidth': width,
            'maxWidth': width,
            'minHeight': height,
            'maxHeight': height,
            'minFrameRate': _selectedFps,
            'maxFrameRate': _selectedFps,
          },
          'optional': [
            {'frameRate': _selectedFps},
            {'width': width},
            {'height': height},
          ],
        };

        debugPrint('視頻軌道參數設置成功:');
        debugPrint('- 分辨率: ${width}x$height');
        debugPrint('- 幀率: $_selectedFps');
        debugPrint('- 目標碼率: ${_targetBitrate}kbps');
        debugPrint('- 編碼參數: $videoConstraints');
      } catch (e) {
        debugPrint('設置視頻軌道參數時出錯: $e');
        // 繼續執行，不中斷推流
      }

      // Create offer and wait for ICE gathering
      debugPrint('創建 offer 並等待 ICE 收集');
      final offer = await _pc!.createOffer({
        'offerToReceiveAudio': false,
        'offerToReceiveVideo': false
      });
      await _pc!.setLocalDescription(offer);
      await _waitIceGatheringComplete(_pc!);

      // Get final LocalDescription and POST SDP to WHIP
      final localDesc = await _pc!.getLocalDescription();
      debugPrint('準備發送 SDP 到伺服器');
      debugPrint('SDP 內容:');
      debugPrint('----------------------------------------');
      debugPrint(localDesc?.sdp ?? 'SDP is null');
      debugPrint('----------------------------------------');
      
      try {
        debugPrint('發送 SDP 到伺服器: $_whipEndpoint');
        final response = await _dio.post(
          _whipEndpoint,
          data: localDesc?.sdp,
          options: Options(
            contentType: 'application/sdp',
            sendTimeout: Duration(seconds: 15),  // 增加超時時間
            receiveTimeout: Duration(seconds: 15),
            validateStatus: (status) {
              // 接受更多的狀態碼
              return status != null && status >= 200 && status < 500;
            },
            headers: {
              'Connection': 'keep-alive',
              'User-Agent': 'Flutter-WebRTC-Client',
              'Accept': 'application/sdp',
              'Cache-Control': 'no-cache',
              'Pragma': 'no-cache',
            },
          ),
        );

        if (!mounted) return;

        // 處理不同的響應狀態碼
        if (response.statusCode == 502) {
          debugPrint('伺服器返回 502 錯誤，可能是伺服器暫時不可用');
          throw DioException(
            requestOptions: response.requestOptions,
            response: response,
            error: '伺服器暫時不可用，請稍後重試',
          );
        } else if (response.statusCode != 200 && response.statusCode != 201) {
          throw DioException(
            requestOptions: response.requestOptions,
            response: response,
            error: '伺服器返回狀態碼 ${response.statusCode}',
          );
        }

        _resourceUrl = response.headers.map['location']?.first;
        final answerSdp = response.data as String;
        debugPrint('收到伺服器響應，設置遠程描述');
        debugPrint('Answer SDP: $answerSdp');

        // Set remote SDP
        await _pc!.setRemoteDescription(
            RTCSessionDescription(answerSdp, 'answer'));
        debugPrint('Remote description set');

        if (!mounted) return;
        setState(() {
          _isStreaming = true;
          _streamingStatus = '推流中';
          _isRetrying = false;
          _retryCount = 0;
          _currentResolution = _selectedResolution;
          _currentFps = _selectedFps;
        });
        debugPrint('推流成功建立');
        // 當推流狀態變為"推流中"時，顯示懸浮WebView
        _showFloatingWebViewOverlay();
      } catch (e) {
        if (!mounted) return;
        debugPrint('推流過程中發生錯誤: $e');
        if (e is DioException) {
          debugPrint('Dio 錯誤類型: ${e.type}');
          debugPrint('Dio 錯誤信息: ${e.message}');
          
          String errorMessage = '連接失敗';
          switch (e.type) {
            case DioExceptionType.connectionTimeout:
              errorMessage = '連接超時，請檢查網絡';
              break;
            case DioExceptionType.sendTimeout:
              errorMessage = '發送超時，請檢查網絡';
              break;
            case DioExceptionType.receiveTimeout:
              errorMessage = '接收超時，請檢查網絡';
              break;
            case DioExceptionType.connectionError:
              errorMessage = '無法連接到伺服器，請檢查網絡';
              break;
            case DioExceptionType.badResponse:
              if (e.response?.statusCode == 502) {
                errorMessage = '伺服器暫時不可用，請稍後重試';
                // 增加重試等待時間
                await Future.delayed(Duration(seconds: 5));
              } else {
                errorMessage = '伺服器響應錯誤: ${e.response?.statusCode}';
              }
              break;
            default:
              errorMessage = '未知錯誤: ${e.message}';
          }
          
          setState(() {
            _streamingStatus = errorMessage;
            _isStreaming = false;
          });
        } else {
          setState(() {
            _streamingStatus = '連接失敗: ${e.toString()}';
            _isStreaming = false;
          });
        }
        debugPrint('準備重試推流');
        await _retryStreaming();
      }
    } catch (e, stackTrace) {
      if (!mounted) return;
      debugPrint('推流初始化過程中發生錯誤: $e');
      debugPrint('錯誤堆棧: $stackTrace');
      setState(() {
        _streamingStatus = '連接失敗: ${e.toString()}';
        _isStreaming = false;
      });
      debugPrint('準備重試推流');
      await _retryStreaming();
    }
  }

  Future<void> _waitIceGatheringComplete(RTCPeerConnection pc) async {
    if (pc.iceGatheringState ==
        RTCIceGatheringState.RTCIceGatheringStateComplete) {
      return;
    }
    final completer = Completer<void>();
    pc.onIceGatheringState = (state) {
      if (state == RTCIceGatheringState.RTCIceGatheringStateComplete &&
          !completer.isCompleted) {
        completer.complete();
      }
    };
    await completer.future
        .timeout(const Duration(seconds: 10), onTimeout: () => null);
  }

  @override
  void dispose() {
    debugPrint('🔄 開始強制清理資源...');
    
    // 1. 立即停止所有定時器和異步操作
    try {
      _statsTimer?.cancel();
      _statsTimer = null;
      debugPrint('✅ 統計定時器已停止');
    } catch (e) {
      debugPrint('⚠️ 停止統計定時器失敗: $e');
    }
    
    // 2. 強制標記為非推流狀態和重置退出狀態
    _isStreaming = false;
    _showFloatingWebView = false;
    _hasNavigatedToWebView = false;
    _isExiting = false;
    _exitProgress = 0.0;
    _exitStatusText = '';

    // 3. 強制清理 WebView 控制器
    try {
      _floatingWebViewController = null;
      debugPrint('✅ WebView控制器已清理');
    } catch (e) {
      debugPrint('⚠️ 清理WebView控制器失敗: $e');
    }

    // 4. 立即清理所有 PeerConnection 事件監聽器
    if (_pc != null) {
      try {
        _pc!.onConnectionState = null;
        _pc!.onIceConnectionState = null;
        _pc!.onIceGatheringState = null;
        _pc!.onSignalingState = null;
        _pc!.onIceCandidate = null;
        _pc!.onDataChannel = null;
        _pc!.onTrack = null;
        _pc!.onRenegotiationNeeded = null;
        debugPrint('✅ PeerConnection 事件監聽器已清理');
      } catch (e) {
        debugPrint('⚠️ 清理 PeerConnection 事件監聽器失敗: $e');
      }
    }

    // 5. 強制停止所有媒體軌道
    if (_localStream != null) {
      try {
        final tracks = _localStream!.getTracks();
        for (final track in tracks) {
          try {
            track.stop();
            debugPrint('✅ 媒體軌道已停止: ${track.kind}');
          } catch (e) {
            debugPrint('⚠️ 停止媒體軌道失敗: $e');
          }
        }
      } catch (e) {
        debugPrint('⚠️ 批量停止媒體軌道失敗: $e');
      }
    }

    // 6. 立即清理渲染器
    try {
      _localRenderer.srcObject = null;
      _localRenderer.dispose();
      debugPrint('✅ 渲染器已清理');
    } catch (e) {
      debugPrint('⚠️ 清理渲染器失敗: $e');
    }

    // 7. 強制關閉 PeerConnection
    if (_pc != null) {
      try {
        _pc!.close();
        _pc = null;
        debugPrint('✅ PeerConnection 已強制關閉');
      } catch (e) {
        debugPrint('⚠️ 強制關閉 PeerConnection 失敗: $e');
        // 即使失敗也要清空引用
        _pc = null;
      }
    }

    // 8. 清理媒體流
    if (_localStream != null) {
      try {
        _localStream!.dispose();
        _localStream = null;
        debugPrint('✅ 媒體流已清理');
      } catch (e) {
        debugPrint('⚠️ 清理媒體流失敗: $e');
        // 即使失敗也要清空引用
        _localStream = null;
      }
    }

    // 9. 清理設備監聽
    try {
      navigator.mediaDevices.ondevicechange = null;
      debugPrint('✅ 設備監聽已清理');
    } catch (e) {
      debugPrint('⚠️ 清理設備監聽失敗: $e');
    }

    // 10. 清理 Dio 實例
    try {
      _dio.close(force: true);
      debugPrint('✅ Dio 實例已強制清理');
    } catch (e) {
      debugPrint('⚠️ 強制清理 Dio 實例失敗: $e');
    }

    // 11. 清理資源URL
    _resourceUrl = null;

    // 12. 強制垃圾回收提示（僅在debug模式）
    if (kDebugMode) {
      debugPrint('🗑️ 建議進行垃圾回收');
    }

    // 13. 最後調用父類dispose
    try {
      super.dispose();
      debugPrint('✅ 資源強制清理完成');
    } catch (e) {
      debugPrint('⚠️ 父類dispose失敗: $e');
    }
  }

  // 同步版本的停止推流方法
  void _stopStreamingSync() {
    debugPrint('🔄 開始同步停止推流...');
    
    // 先停止所有可能触发状态更新的操作
    _statsTimer?.cancel();
    _statsTimer = null;

    // 清理 PeerConnection 的事件监听器
    if (_pc != null) {
      try {
        _pc!.onConnectionState = null;
        _pc!.onIceConnectionState = null;
        _pc!.onIceGatheringState = null;
        debugPrint('✅ PeerConnection 事件監聽器已清理（同步）');
      } catch (e) {
        debugPrint('⚠️ 清理 PeerConnection 事件監聽器失敗（同步）: $e');
      }
    }

    // 嘗試删除資源（如果存在的話）
    try {
      if (_resourceUrl != null && _resourceUrl!.isNotEmpty) {
        // 檢查 _resourceUrl 是否為完整 URL
        String deleteUrl;
        if (_resourceUrl!.startsWith('http')) {
          deleteUrl = _resourceUrl!;
        } else {
          // 如果不是完整URL，需要構建完整的URL
          final uri = Uri.parse(_whipEndpoint);
          deleteUrl = '${uri.scheme}://${uri.host}${uri.port != 80 && uri.port != 443 ? ':${uri.port}' : ''}$_resourceUrl';
        }
        
        debugPrint('🔗 嘗試删除資源: $deleteUrl');
        // 同步删除資源（使用 .then() 避免 await）
        _dio.delete(deleteUrl).then((_) {
          debugPrint('✅ 資源删除成功');
        }).catchError((e) {
          debugPrint('⚠️ 删除資源失敗: $e');
        });
      } else {
        debugPrint('ℹ️ 沒有資源URL需要删除');
      }
    } catch (e) {
      debugPrint('⚠️ 停止推流錯誤: $e');
    }

    // 關閉 PeerConnection
    if (_pc != null) {
      try {
        _pc!.close();
        _pc = null;
        debugPrint('✅ PeerConnection 已關閉（同步）');
      } catch (e) {
        debugPrint('⚠️ 關閉 PeerConnection 失敗（同步）: $e');
      }
    }
    
    debugPrint('✅ 同步停止推流完成');
  }

  // 改進的停止推流方法 - 添加超時機制防止當機
  Future<void> _stopStreaming() async {
    debugPrint('🛑 開始停止推流（帶超時保護）...');
    
    try {
      // 設置整體超時時間，避免長期卡死
      await _stopStreamingWithTimeout().timeout(
        Duration(seconds: 15),
        onTimeout: () {
          debugPrint('⏰ 停止推流超時，執行強制清理');
          _forceStopCleanup();
          throw TimeoutException('停止推流超時');
        },
      );
    } catch (e) {
      debugPrint('❌ 停止推流過程中發生錯誤: $e');
      _forceStopCleanup();
    }
  }

  // 內部停止推流方法（帶詳細步驟）
  Future<void> _stopStreamingWithTimeout() async {
    debugPrint('🔄 執行詳細停止流程...');
    
    // 1. 立即停止所有定時器
    _statsTimer?.cancel();
    _statsTimer = null;

    // 2. 立即標記為停止狀態
    if (mounted) {
      setState(() {
        _isStreaming = false;
        _streamingStatus = '正在停止...';
        _hasNavigatedToWebView = false;
        _showFloatingWebView = false;
        _floatingWebViewController = null;
      });
    }

    // 清理 PeerConnection 的事件監聽器
    if (_pc != null) {
      try {
        _pc!.onConnectionState = null;
        _pc!.onIceConnectionState = null;
        _pc!.onIceGatheringState = null;
        _pc!.onSignalingState = null;
        _pc!.onIceCandidate = null;
        _pc!.onDataChannel = null;
        _pc!.onTrack = null;
        debugPrint('✅ PeerConnection 事件監聽器已清理');
      } catch (e) {
        debugPrint('⚠️ 清理 PeerConnection 事件監聽器失敗: $e');
      }
    }

    // 重置统计变量
    _resetStats();

    // 嘗試删除遠程資源
    try {
      if (_resourceUrl != null && _resourceUrl!.isNotEmpty) {
        // 檢查 _resourceUrl 是否為完整 URL
        String deleteUrl;
        if (_resourceUrl!.startsWith('http')) {
          deleteUrl = _resourceUrl!;
        } else {
          // 如果不是完整URL，需要構建完整的URL
          final uri = Uri.parse(_whipEndpoint);
          deleteUrl = '${uri.scheme}://${uri.host}${uri.port != 80 && uri.port != 443 ? ':${uri.port}' : ''}$_resourceUrl';
        }
        
        debugPrint('🔗 嘗試删除遠程資源: $deleteUrl');
        
        // 設置較短的超時時間，避免長時間等待
        await _dio.delete(deleteUrl).timeout(
          Duration(seconds: 3),
          onTimeout: () {
            debugPrint('⏰ 删除資源超時，繼續清理流程');
            throw TimeoutException('Delete resource timeout');
          },
        );
        debugPrint('✅ 遠程資源删除成功');
      } else {
        debugPrint('ℹ️ 沒有遠程資源需要删除');
      }
    } catch (e) {
      debugPrint('⚠️ 删除遠程資源失敗: $e，繼續清理流程');
    }

    // 停止和清理媒體軌道
    if (_localStream != null) {
      try {
        final tracks = _localStream!.getTracks();
        for (final track in tracks) {
          debugPrint('🔇 停止媒體軌道: ${track.kind}');
          track.stop();
        }
        debugPrint('✅ 所有媒體軌道已停止');
      } catch (e) {
        debugPrint('⚠️ 停止媒體軌道失敗: $e');
      }
    }

    // 關閉 PeerConnection
    if (_pc != null) {
      try {
        debugPrint('🔌 關閉 PeerConnection...');
        await _pc!.close();
        _pc = null;
        debugPrint('✅ PeerConnection 已關閉');
      } catch (e) {
        debugPrint('⚠️ 關閉 PeerConnection 失敗: $e');
        _pc = null; // 即使失敗也要清空引用
      }
    }

    // 清空資源URL
    _resourceUrl = '';

    // 最后再更新状态
    if (mounted) {
      setState(() {
        _isStreaming = false;
        _streamingStatus = '已停止';
        _hasNavigatedToWebView = false;  // 重置跳轉標誌，允許下次推流時再次跳轉
        _showFloatingWebView = false;  // 隱藏懸浮WebView
        _floatingWebViewController = null;  // 清理WebView控制器引用
      });
      debugPrint('✅ 推流狀態已更新為停止');
    }

    debugPrint('🎯 停止推流流程完成');
  }

  // 強制停止清理方法 - 用於超時或錯誤情況
  void _forceStopCleanup() {
    debugPrint('💥 執行強制停止清理...');
    
    try {
      // 1. 強制停止所有定時器
      _statsTimer?.cancel();
      _statsTimer = null;
      
      // 2. 強制清理所有 PeerConnection 相關資源
      if (_pc != null) {
        try {
          _pc!.onConnectionState = null;
          _pc!.onIceConnectionState = null;
          _pc!.onIceGatheringState = null;
          _pc!.onSignalingState = null;
          _pc!.onIceCandidate = null;
          _pc!.onDataChannel = null;
          _pc!.onTrack = null;
          _pc!.onRenegotiationNeeded = null;
        } catch (e) {
          debugPrint('⚠️ 清理PeerConnection事件監聽器失敗: $e');
        }
        
        try {
          _pc!.close(); // 不等待結果
        } catch (e) {
          debugPrint('⚠️ 關閉PeerConnection失敗: $e');
        }
        
        _pc = null; // 強制清空引用
      }
      
      // 3. 強制停止媒體軌道
      if (_localStream != null) {
        try {
          final tracks = _localStream!.getTracks();
          for (final track in tracks) {
            try {
              track.stop();
            } catch (e) {
              debugPrint('⚠️ 停止媒體軌道失敗: $e');
            }
          }
        } catch (e) {
          debugPrint('⚠️ 批量停止媒體軌道失敗: $e');
        }
        
        try {
          _localStream!.dispose();
        } catch (e) {
          debugPrint('⚠️ 清理媒體流失敗: $e');
        }
        
        _localStream = null; // 強制清空引用
      }
      
      // 4. 清理其他資源
      _resourceUrl = null;
      _floatingWebViewController = null;
      
      // 5. 強制更新UI狀態
      if (mounted) {
        setState(() {
          _isStreaming = false;
          _streamingStatus = '已強制停止';
          _showFloatingWebView = false;
          _hasNavigatedToWebView = false;
        });
      }
      
      // 6. 重置統計變量
      _resetStats();
      
      debugPrint('💥 強制停止清理完成');
    } catch (e) {
      debugPrint('❌ 強制清理過程中發生錯誤: $e');
    }
  }

  // 修改统计定时器启动方法
  void _startStatsTimer() {
    _statsTimer?.cancel();
    _statsTimer = Timer.periodic(Duration(milliseconds: 500), (timer) {
      if (!mounted) {
        timer.cancel();
        return;
      }
      if (_pc != null && _isStreaming && mounted) {
        _updateStats();
      }
    });
  }

  // 修改统计信息更新方法
  Future<void> _updateStats() async {
    if (!mounted) return;
    
    try {
      final stats = await _pc!.getStats();
      if (!mounted) return;
      
      final now = DateTime.now();
      
      stats.forEach((report) {
        if (!mounted) return;
        
        if (report.type == 'outbound-rtp' && report.values['kind'] == 'video') {
          final values = report.values;
          final currentBytesSent = (values['bytesSent'] ?? 0).toInt();
          final currentTargetBitrate = (values['targetBitrate'] ?? 0.0).toDouble();
          final qualityLimitationReason = values['qualityLimitationReason']?.toString() ?? 'none';
          
          // 安全地處理 qualityLimitationDurations
          Map<String, dynamic>? qualityLimitationDurations;
          try {
            final durations = values['qualityLimitationDurations'];
            if (durations != null) {
              qualityLimitationDurations = Map<String, dynamic>.from(durations as Map);
            }
          } catch (e) {
            debugPrint('解析 qualityLimitationDurations 失敗: $e');
          }
          
          if (_lastStatsTime != null) {
            final timeDiff = now.difference(_lastStatsTime!).inMilliseconds;
            if (timeDiff > 0 && mounted) {
              // 計算當前碼率 (kbps)
              final bytesDiff = currentBytesSent - _lastBytesSent;
              final currentBitrate = (bytesDiff * 8 * 1000 / timeDiff) / 1024;
              
              // 使用移動平均平滑碼率
              _bitrateHistory.add(currentBitrate.toInt());
              if (_bitrateHistory.length > _bitrateHistorySize) {
                _bitrateHistory.removeAt(0);
              }
              
              // 計算平均碼率
              final avgBitrate = _bitrateHistory.reduce((a, b) => a + b) / _bitrateHistory.length;
              
              // 使用指數移動平均進行平滑
              _currentBitrate = (_lastBitrate * (_smoothingFactor - 1) + avgBitrate) / _smoothingFactor;
              _lastBitrate = _currentBitrate;

              // 檢查碼率是否過低
              if (_targetBitrate > 0 && _currentBitrate < _targetBitrate * _minBitrateRatio) {
                _consecutiveLowBitrateCount++;
                if (_consecutiveLowBitrateCount >= _maxLowBitrateCount) {
                  debugPrint('警告: 連續 $_consecutiveLowBitrateCount 次碼率過低');
                  debugPrint('當前碼率: ${_currentBitrate.toStringAsFixed(1)} kbps');
                  debugPrint('目標碼率: ${_targetBitrate.toStringAsFixed(1)} kbps');
                  debugPrint('質量限制原因: $qualityLimitationReason');
                  if (qualityLimitationDurations != null) {
                    debugPrint('質量限制持續時間: $qualityLimitationDurations');
                  }
                  
                  // 根據限制原因調整編碼參數
                  if (qualityLimitationReason == 'bandwidth') {
                    _encoderStatus = '帶寬限制';
                    // 可以在这里添加降低碼率的邏輯
                  } else if (qualityLimitationReason == 'cpu') {
                    _encoderStatus = 'CPU限制';
                    // 可以在这里添加降低分辨率的邏輯
                  } else if (qualityLimitationReason == 'other') {
                    _encoderStatus = '其他限制';
                  }
                }
              } else {
                _consecutiveLowBitrateCount = 0;
                _encoderStatus = '正常';
              }
              
              setState(() {
                _actualBitrate = _currentBitrate;
                _targetBitrate = currentTargetBitrate / 1000; // 轉換為 kbps
                _frameRate = (values['framesPerSecond'] ?? 0.0).toDouble();
                _resolutionWidth = (values['frameWidth'] ?? 0).toInt();
                _resolutionHeight = (values['frameHeight'] ?? 0).toInt();
                _framesSent = (values['framesSent'] ?? 0).toInt();
                _framesDropped = (values['framesDropped'] ?? 0).toInt();
                _codec = values['codecId']?.toString() ?? '未知';
                _nackCount = (values['nackCount'] ?? 0).toInt();
                _pliCount = (values['pliCount'] ?? 0).toInt();
                _firCount = (values['firCount'] ?? 0).toInt();
                _qpSum = (values['qpSum'] ?? 0).toInt();
                _qpCount = (values['framesEncoded'] ?? 0).toInt();
                if (_qpCount > 0) {
                  _avgQP = _qpSum / _qpCount;
                }
              });
            }
          }
          
          _lastBytesSent = currentBytesSent;
          _lastStatsTime = now;
        } else if (report.type == 'remote-inbound-rtp' && report.values['kind'] == 'video') {
          if (!mounted) return;
          final values = report.values;
          setState(() {
            _rtt = (values['roundTripTime'] ?? 0.0).toDouble();
            _jitter = (values['jitter'] ?? 0.0).toDouble();
            _packetsLost = (values['packetsLost'] ?? 0).toInt();
            _packetsSent = (values['packetsReceived'] ?? 0).toInt();
            _packetLossRate = _packetsLost / (_packetsSent + 1) * 100;
          });
        } else if (report.type == 'candidate-pair' && report.values['selected'] == true) {
          if (!mounted) return;
          final values = report.values;
          setState(() {
            _availableBitrate = (values['availableOutgoingBitrate'] ?? 0.0).toDouble();
            _bandwidth = (values['bandwidth'] ?? 0.0).toDouble();
          });
        }
      });

      // 計算網絡質量分數 (0-1)
      if (mounted) {
        final qualityScore = _calculateNetworkQuality();
        setState(() {
          _networkQuality = qualityScore;
        });
      }

    } catch (e, stackTrace) {
      debugPrint('獲取統計信息失敗: $e');
      debugPrint('錯誤堆棧: $stackTrace');
    }
  }

  // 修改網絡質量計算方法
  double _calculateNetworkQuality() {
    double totalWeight = 0.0;
    double weightedSum = 0.0;
    
    // RTT 影響 (權重: 0.3)
    if (_rtt > 0) {
      double rttScore = 1.0 - (_rtt / 500.0).clamp(0.0, 1.0);  // 500ms 為最差情況
      weightedSum += rttScore * 0.3;
      totalWeight += 0.3;
      debugPrint('RTT 分數: $rttScore (RTT: $_rtt ms)');
    }
    
    // 丟包率影響 (權重: 0.3)
    if (_packetLossRate > 0) {
      double lossScore = 1.0 - (_packetLossRate / 10.0).clamp(0.0, 1.0);  // 10% 為最差情況
      weightedSum += lossScore * 0.3;
      totalWeight += 0.3;
      debugPrint('丟包率分數: $lossScore (丟包率: $_packetLossRate%)');
    }
    
    // 碼率影響 (權重: 0.2)
    if (_targetBitrate > 0 && _actualBitrate > 0) {
      double bitrateRatio = _actualBitrate / _targetBitrate;
      double bitrateScore = bitrateRatio.clamp(0.0, 1.0);
      weightedSum += bitrateScore * 0.2;
      totalWeight += 0.2;
      debugPrint('碼率分數: $bitrateScore (實際: $_actualBitrate, 目標: $_targetBitrate)');
    }
    
    // 幀率影響 (權重: 0.1)
    if (_selectedFps > 0 && _frameRate > 0) {
      double frameRateRatio = _frameRate / _selectedFps;
      double frameRateScore = frameRateRatio.clamp(0.0, 1.0);
      weightedSum += frameRateScore * 0.1;
      totalWeight += 0.1;
      debugPrint('幀率分數: $frameRateScore (實際: $_frameRate, 目標: $_selectedFps)');
    }
    
    // 帶寬影響 (權重: 0.1)
    if (_availableBitrate > 0 && _targetBitrate > 0) {
      double bandwidthRatio = (_availableBitrate / 1000) / _targetBitrate;
      double bandwidthScore = bandwidthRatio.clamp(0.0, 1.0);
      weightedSum += bandwidthScore * 0.1;
      totalWeight += 0.1;
      debugPrint('帶寬分數: $bandwidthScore (可用: ${_availableBitrate/1000}Mbps, 目標: $_targetBitrate kbps)');
    }
    
    // 計算最終分數
    double finalScore = totalWeight > 0 ? weightedSum / totalWeight : 0.0;
    debugPrint('最終網絡質量分數: $finalScore (總權重: $totalWeight)');
    return finalScore;
  }

  // 顯示懸浮WebView的方法
  void _showFloatingWebViewOverlay() {
    // 只有来自WebView JS数据的推流才显示WebView
    if (!widget.streamSettings.fromWebViewJS) {
      debugPrint('🚫 推流来源不是WebView JS数据，不显示WebView');
      return;
    }
    
    // 避免重複顯示
    if (_hasNavigatedToWebView) return;
    _hasNavigatedToWebView = true;
    
    // 延遲執行顯示，確保當前狀態更新完成
    Future.delayed(const Duration(milliseconds: 500), () {
      if (!mounted) return;
      
      debugPrint('🎯 顯示懸浮 WebView（来自WebView JS数据）');
      
      setState(() {
        _showFloatingWebView = true;
      });
    });
  }

  // 修改開始推流按鈕的處理方法
  void _onStartStreamingPressed() {
    if (!mounted) return;
    
    debugPrint('🎮 按鈕點擊：當前推流狀態 = $_isStreaming, 狀態文本 = $_streamingStatus');
    
    try {
      if (_isStreaming) {
        debugPrint('🛑 用戶點擊停止推流按鈕');
        _stopStreaming();
      } else {
        debugPrint('▶️ 用戶點擊開始推流按鈕');
        _startStreaming();
      }
    } catch (e) {
      debugPrint('⚠️ 切換推流狀態失敗: $e');
      if (mounted) {
        setState(() {
          _streamingStatus = '操作失敗: $e';
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: _onWillPop,
      child: Stack(
        children: [
          // 主要的Scaffold內容
          Scaffold(
            extendBodyBehindAppBar: true,
            appBar: AppBar(
              backgroundColor: Colors.transparent,
              elevation: 0,
              leading: Container(), // 移除預設的返回按鈕
              actions: [
                // 只在沒有顯示懸浮WebView時顯示關閉按鈕
                if (!_showFloatingWebView)
                  IconButton(
                    icon: Icon(
                      _isExiting ? Icons.hourglass_empty : Icons.close, 
                      color: _isExiting ? Colors.grey : Colors.white,
                      size: 28,
                    ),
                    onPressed: _isExiting ? null : () {  // 如果正在退出則禁用按鈕
                      debugPrint('🖱️ 用戶點擊了關閉按鈕 (mounted: $mounted, _isStreaming: $_isStreaming, _isExiting: $_isExiting)');
                      if (_isExiting) {
                        debugPrint('⚠️ 正在退出中，忽略重複點擊');
                        return;
                      }
                      debugPrint('🎯 開始執行關閉流程...');
                      try {
                        if (mounted) {
                          debugPrint('✅ 組件已掛載，調用安全退出');
                          _safeExit();
                        } else {
                          debugPrint('⚠️ 組件未掛載，嘗試直接退出');
                          // 直接嘗試 pop
                          Navigator.of(context).pop();
                        }
                      } catch (e) {
                        debugPrint('❌ 關閉按鈕點擊處理失敗: $e');
                        // 最後手段：強制退出
                        try {
                          Navigator.of(context).pop();
                        } catch (e2) {
                          debugPrint('❌ 強制退出也失敗: $e2');
                        }
                      }
                    },
                    tooltip: _isExiting ? '正在退出...' : '退出',
                  ),
              ],
            ),
            body: Stack(
              children: [
                // 視頻預覽
                Container(
                  width: MediaQuery.of(context).size.width,
                  height: MediaQuery.of(context).size.height,
                  decoration: BoxDecoration(color: Colors.black54),
                  child: _localRenderer.textureId == null
                      ? const Center(child: Text('尚未開啟相機', style: TextStyle(color: Colors.white)))
                      : RTCVideoView(
                          _localRenderer, 
                          mirror: _isFrontCamera,
                          objectFit: RTCVideoViewObjectFit.RTCVideoViewObjectFitCover,  // 填滿容器
                        ),
                ),
                
                // 狀態顯示框
                Positioned(
                  top: 40,
                  left: 20,
                  child: Row(
                    children: [
                      _buildStatusDisplay(),
                      // WebView 快速訪問按鈕（只在推流中且曾經顯示過 WebView 時顯示）
                      if (_isStreaming && _hasNavigatedToWebView && !_showFloatingWebView) ...[
                        SizedBox(width: 8),
                        GestureDetector(
                          onTap: () {
                            setState(() {
                              _showFloatingWebView = true;
                            });
                          },
                          child: Container(
                            width: 40,
                            height: 40,
                            decoration: BoxDecoration(
                              color: Colors.purple.withOpacity(0.8),
                              borderRadius: BorderRadius.circular(20),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.3),
                                  blurRadius: 4,
                                  offset: Offset(0, 2),
                                ),
                              ],
                            ),
                            child: Icon(
                              Icons.web,
                              color: Colors.white,
                              size: 20,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),

                // 底部工具欄
                Positioned(
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: Container(
                    color: Colors.black54,
                    child: SafeArea(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // 相機控制按鈕
                          Padding(
                            padding: const EdgeInsets.symmetric(vertical: 8),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                              children: [
                                IconButton(
                                  icon: Icon(Icons.switch_camera, color: Colors.white),
                                  onPressed: _toggleCamera,
                                  tooltip: '切換攝像頭',
                                ),
                                IconButton(
                                  icon: Icon(_isTorchOn ? Icons.flash_on : Icons.flash_off, 
                                           color: Colors.white),
                                  onPressed: _toggleTorch,
                                  tooltip: '閃光燈',
                                ),
                                IconButton(
                                  icon: Icon(Icons.camera_alt, color: Colors.white),
                                  onPressed: _captureFrame,
                                  tooltip: '截圖',
                                ),
                                IconButton(
                                  icon: Icon(Icons.face, color: Colors.white),
                                  onPressed: () async {
                                    bool isBeautyEnabled = await Helper.pixelFreeGetBeautyUseEffect() ?? false;
                                    await Helper.pixelFreeSetBeautyUseEffect(!isBeautyEnabled);
                                  },
                                  tooltip: '美顏開關',
                                ),
                              ],
                            ),
                          ),
                          // 推流控制按鈕
                          Padding(
                            padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                            child: Builder(
                              builder: (context) {
                                // 添加按鈕狀態調試日誌
                                debugPrint('🔘 按鈕渲染：_isStreaming = $_isStreaming, 狀態 = $_streamingStatus');
                                return ElevatedButton.icon(
                                  onPressed: _onStartStreamingPressed,
                                  icon: Icon(_isStreaming ? Icons.stop : Icons.play_arrow),
                                  label: Text(_isStreaming ? '停止推流' : '開始推流'),
                                  style: ElevatedButton.styleFrom(
                                    minimumSize: const Size.fromHeight(48),
                                    backgroundColor: _isStreaming ? Colors.red : Colors.green,
                                  ),
                                );
                              },
                            ),
                          ),

                          // 美顏設置按鈕
                          Padding(
                            padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                            child: ElevatedButton.icon(
                              onPressed: () {
                                showModalBottomSheet(
                                  context: context,
                                  isScrollControlled: true,
                                  backgroundColor: Colors.transparent,
                                  barrierColor: Colors.transparent,
                                  builder: (context) => PixeBeautyDialog(),
                                );
                              },
                              icon: Icon(Icons.face),
                              label: Text('美顏設置'),
                              style: ElevatedButton.styleFrom(
                                minimumSize: const Size.fromHeight(48),
                                backgroundColor: Colors.blue,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          // 置於最頂層的滿版 WebView
          if (_showFloatingWebView) _buildTopMostFloatingWebView(),
        ],
      ),
    );
  }

  Widget _buildStatusDisplay() {
    String qualityText;
    Color qualityColor;
    
    if (_networkQuality >= 0.8) {
      qualityText = '優秀';
      qualityColor = Colors.green;
    } else if (_networkQuality >= 0.6) {
      qualityText = '良好';
      qualityColor = Colors.lightGreen;
    } else if (_networkQuality >= 0.4) {
      qualityText = '一般';
      qualityColor = Colors.yellow;
    } else if (_networkQuality >= 0.2) {
      qualityText = '較差';
      qualityColor = Colors.orange;
    } else {
      qualityText = '差';
      qualityColor = Colors.red;
    }

    return Container(
      padding: EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.black54,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '分辨率: ${_resolutionWidth}x$_resolutionHeight',
            style: TextStyle(color: Colors.white),
          ),
          Text(
            '幀率: ${_frameRate.toStringAsFixed(1)} fps',
            style: TextStyle(color: Colors.white),
          ),
          Text(
            '實際碼率: ${_actualBitrate.toStringAsFixed(1)} kbps',
            style: TextStyle(color: Colors.white),
          ),
          Text(
            '目標碼率: ${_targetBitrate.toStringAsFixed(1)} kbps',
            style: TextStyle(color: Colors.white),
          ),
          Text(
            'RTT: ${_rtt.toStringAsFixed(2)} ms',
            style: TextStyle(color: Colors.white),
          ),
          Text(
            '抖動: ${_jitter.toStringAsFixed(2)} ms',
            style: TextStyle(color: Colors.white),
          ),
          Text(
            '丟包率: ${_packetLossRate.toStringAsFixed(1)}%',
            style: TextStyle(color: Colors.white),
          ),
          Text(
            '丟幀: $_framesDropped / $_framesSent',
            style: TextStyle(color: Colors.white),
          ),
          Text(
            '編解碼器: $_codec',
            style: TextStyle(color: Colors.white),
          ),
          Text(
            '狀態: $_streamingStatus',
            style: TextStyle(
              color: _streamingStatus == '推流中' ? Colors.green : 
                     _streamingStatus.contains('連接中') ? Colors.yellow : Colors.red,
            ),
          ),
        ],
      ),
    );
  }

  // 構建滿版WebView（舊版本，保留兼容性）
  Widget _buildFloatingWebView() {
    // 構建WebView URL
    String webViewUrl;
    try {
      final encodedStreamUrl = Uri.encodeQueryComponent(_whipEndpoint);
      webViewUrl = 'http://*************:8080/streamer/returnToLive?streamUrl=$encodedStreamUrl';
    } catch (e) {
      debugPrint('❌ URL編碼失敗: $e');
      final bytes = _whipEndpoint.codeUnits;
      final base64StreamUrl = base64.encode(bytes);
      webViewUrl = 'http://*************:8080/streamer/returnToLive?streamUrl=$base64StreamUrl&encoding=base64';
    }

    return Positioned.fill(
      child: Container(
        color: Colors.white,
        child: Stack(
          children: [
            // WebView 內容區域（帶下拉刷新）
            Positioned.fill(
              child: RefreshIndicator(
                onRefresh: () async {
                  // 重新加載 WebView
                  if (_floatingWebViewController != null) {
                    await _floatingWebViewController!.reload();
                    debugPrint('🔄 WebView 下拉刷新');
                  }
                },
                child: SingleChildScrollView(
                  physics: AlwaysScrollableScrollPhysics(),
                  child: Container(
                    width: MediaQuery.of(context).size.width,
                    height: MediaQuery.of(context).size.height,
                    child: InAppWebView(
                      initialUrlRequest: URLRequest(url: WebUri(webViewUrl)),
                      initialSettings: InAppWebViewSettings(
                        javaScriptEnabled: true,
                        domStorageEnabled: true,
                        allowsInlineMediaPlayback: true,
                        mediaPlaybackRequiresUserGesture: false,
                        mixedContentMode: MixedContentMode.MIXED_CONTENT_ALWAYS_ALLOW,
                        clearCache: false,
                        supportZoom: true,
                        useOnDownloadStart: true,
                        disableVerticalScroll: false,
                        disableHorizontalScroll: false,
                      ),
                      onWebViewCreated: (controller) {
                        _floatingWebViewController = controller;
                        _registerJavaScriptHandlers(controller);
                      },
                      onLoadStart: (controller, url) {
                        debugPrint('🌐 滿版WebView開始載入: $url');
                      },
                      onLoadStop: (controller, url) {
                        debugPrint('✅ 滿版WebView載入完成: $url');
                      },
                      onLoadError: (controller, url, code, message) {
                        debugPrint('❌ 滿版WebView載入錯誤: $message');
                      },
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 構建置於最頂層的滿版WebView（無控制欄）
  Widget _buildTopMostFloatingWebView() {
    // 構建WebView URL
    String webViewUrl;
    try {
      final encodedStreamUrl = Uri.encodeQueryComponent(_whipEndpoint);
      webViewUrl = 'http://*************:8080/streamer/returnToLive?streamUrl=$encodedStreamUrl';
    } catch (e) {
      debugPrint('❌ URL編碼失敗: $e');
      final bytes = _whipEndpoint.codeUnits;
      final base64StreamUrl = base64.encode(bytes);
      webViewUrl = 'http://*************:8080/streamer/returnToLive?streamUrl=$base64StreamUrl&encoding=base64';
    }

    return Positioned.fill(
      child: Material(
        child: Container(
          color: Colors.white,
          child: InAppWebView(
            initialUrlRequest: URLRequest(url: WebUri(webViewUrl)),
            initialSettings: InAppWebViewSettings(
              javaScriptEnabled: true,
              domStorageEnabled: true,
              allowsInlineMediaPlayback: true,
              mediaPlaybackRequiresUserGesture: false,
              mixedContentMode: MixedContentMode.MIXED_CONTENT_ALWAYS_ALLOW,
              clearCache: false,
              supportZoom: true,
              useOnDownloadStart: true,
              disableVerticalScroll: false,
              disableHorizontalScroll: false,
            ),
            onWebViewCreated: (controller) {
              _floatingWebViewController = controller;
              _registerJavaScriptHandlers(controller);
            },
            onLoadStart: (controller, url) {
              debugPrint('🌐 滿版WebView開始載入: $url');
            },
            onLoadStop: (controller, url) {
              debugPrint('✅ 滿版WebView載入完成: $url');
            },
            onLoadError: (controller, url, code, message) {
              debugPrint('❌ 滿版WebView載入錯誤: $message');
            },
          ),
        ),
      ),
    );
  }

  void _selectAudioOutput(String deviceId) {
    _localRenderer.audioOutput(deviceId);
  }

  // 註冊 JavaScript Handlers
  void _registerJavaScriptHandlers(InAppWebViewController controller) {
    try {
      // 註冊停止推流的 handler
      controller.addJavaScriptHandler(
        handlerName: 'stopStreaming',
        callback: (args) {
          debugPrint('📨 收到 JavaScript 停止推流請求: $args');
          if (args.isNotEmpty && args[0] is Map) {
            handleStopStreaming(Map<String, dynamic>.from(args[0]));
          } else {
            debugPrint('❌ 收到無效的停止推流參數: $args');
          }
        },
      );
      debugPrint('✅ JavaScript Handler 註冊成功: stopStreaming');
    } catch (e) {
      debugPrint('❌ JavaScript Handler 註冊失敗: $e');
    }
  }

  // 處理來自 JavaScript 的停止推流指令
  void handleStopStreaming(Map<String, dynamic> params) {
    try {
      debugPrint('🎬 處理停止推流指令，參數: $params');
      
      String? action = params['action']?.toString();
      String? streamer = params['streamer']?.toString();
      int? timestamp = params['timestamp'] is int 
          ? params['timestamp'] 
          : int.tryParse(params['timestamp']?.toString() ?? '');
      
      debugPrint('📋 解析參數: action=$action, streamer=$streamer, timestamp=$timestamp');
      
      if (action == 'stop_streaming') {
        stopStreamingProcess(streamer ?? '未知主播');
      } else {
        debugPrint('⚠️ 未知的動作類型: $action');
      }
    } catch (e) {
      debugPrint('❌ 處理停止推流指令時發生錯誤: $e');
      // 即使解析失敗，也嘗試執行停止推流
      stopStreamingProcess('系統');
    }
  }

  // 執行停止推流的具體邏輯
  Future<void> stopStreamingProcess(String streamerUsername) async {
    try {
      debugPrint('🛑 開始執行停止推流流程，主播: $streamerUsername');
      
      // 1. 檢查當前是否正在推流
      if (!_isStreaming) {
        debugPrint('ℹ️ 當前沒有正在進行的推流');
        _showStreamEndDialog(streamerUsername, '推流已經結束');
        return;
      }

      // 2. 關閉懸浮WebView（如果正在顯示）
      if (_showFloatingWebView) {
        debugPrint('🌐 關閉懸浮WebView');
        setState(() {
          _showFloatingWebView = false;
        });
        // 等待UI更新完成
        await Future.delayed(Duration(milliseconds: 300));
      }

      // 3. 模擬點擊停止直播按鈕，而不是直接調用停止推流邏輯
      debugPrint('🖱️ 模擬點擊停止直播按鈕');
      _onStartStreamingPressed();

      // 4. 等待停止推流操作完成
      await Future.delayed(Duration(seconds: 1));

      // 5. 顯示直播結束的提示
      _showStreamEndDialog(streamerUsername, '直播已結束');

    } catch (e) {
      debugPrint('❌ 停止推流過程中發生錯誤: $e');
      
      // 顯示錯誤提示
      _showStreamEndDialog(streamerUsername, '停止推流時發生錯誤');
    }
  }

  // 內部停止推流邏輯（复用現有的停止推流代碼）
  Future<void> _stopStreamingInternal() async {
    debugPrint('🔄 執行內部停止推流邏輯');
    
    // 停止推流狀態
    setState(() {
      _isStreaming = false;
      _showFloatingWebView = false;  // 隱藏懸浮WebView
      _streamingStatus = '已停止';  // 更新狀態顯示
      _isRetrying = false;  // 重置重試狀態
    });

    try {
      // 停止 peer connection
      if (_pc != null) {
        await _pc!.close();
        _pc = null;
        debugPrint('✅ PeerConnection 已關閉');
      }

      // 停止本地媒體流
      if (_localStream != null) {
        _localStream!.getTracks().forEach((track) {
          track.stop();
        });
        _localStream = null;
        debugPrint('✅ 本地媒體流已停止');
      }

      // 清理渲染器
      if (_localRenderer.srcObject != null) {
        _localRenderer.srcObject = null;
        debugPrint('✅ 本地渲染器已清理');
      }

      // 重置相關狀態
      _hasNavigatedToWebView = false;

      debugPrint('🎯 停止推流完成');
    } catch (e) {
      debugPrint('❌ 停止推流時發生錯誤: $e');
      // 繼續執行，確保狀態被重置
    }
    
    // 輸出最終狀態用於調試
    debugPrint('🔚 停止推流後的狀態：_isStreaming = $_isStreaming, _streamingStatus = $_streamingStatus');
  }

  // 顯示直播結束的對話框
  void _showStreamEndDialog(String streamerUsername, String message) {
    if (!mounted) return;

    try {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          title: Row(
            children: [
              Icon(Icons.tv_off, color: Colors.red, size: 28),
              SizedBox(width: 8),
              Text('直播已結束'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '主播 $streamerUsername 已結束直播',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
              ),
              SizedBox(height: 8),
              Text(
                message,
                style: TextStyle(fontSize: 14, color: Colors.grey[600]),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context); // 關閉對話框
                // 自動跳回首頁 - 回到根頁面
                Navigator.of(context).popUntil((route) => route.isFirst);
              },
              child: Text('確定'),
            ),
          ],
        ),
      );
      debugPrint('✅ 顯示直播結束對話框');
    } catch (e) {
      debugPrint('❌ 顯示直播結束對話框失敗: $e');
    }
  }

  // 測試用方法：手動觸發停止推流（用於調試）
  void _testStopStreamingFromJS() {
    debugPrint('🧪 測試停止推流功能');
    debugPrint('🧪 停止前狀態：_isStreaming = $_isStreaming, _streamingStatus = $_streamingStatus');
    final testParams = {
      "action": "stop_streaming",
      "streamer": "測試主播",
      "timestamp": DateTime.now().millisecondsSinceEpoch
    };
    handleStopStreaming(testParams);
  }

  // 檢查推流狀態的調試方法
  void _debugStreamingState() {
    debugPrint('🔍 當前推流狀態調試：');
    debugPrint('   _isStreaming: $_isStreaming');
    debugPrint('   _streamingStatus: $_streamingStatus');
    debugPrint('   _isRetrying: $_isRetrying');
    debugPrint('   _showFloatingWebView: $_showFloatingWebView');
    debugPrint('   _hasNavigatedToWebView: $_hasNavigatedToWebView');
    debugPrint('   _pc != null: ${_pc != null}');
    debugPrint('   _localStream != null: ${_localStream != null}');
    debugPrint('   mounted: $mounted');
  }

  // 添加應用保存的美顏參數的方法
  Future<void> _applySavedBeautySettings() async {
    final prefs = await SharedPreferences.getInstance();
    bool hasAnySavedSettings = false;
    
    // 定義安全的美顏類型列表，排除可能導致崩潰的類型
    final safeBeautyTypes = [
      PFBeautyFiterType.eyeStrength,
      PFBeautyFiterType.faceThinning,
      PFBeautyFiterType.faceNarrow,
      PFBeautyFiterType.faceChin,
      PFBeautyFiterType.faceV,
      PFBeautyFiterType.faceSmall,
      PFBeautyFiterType.faceNose,
      PFBeautyFiterType.faceForehead,
      PFBeautyFiterType.faceMouth,
      PFBeautyFiterType.facePhiltrum,
      PFBeautyFiterType.faceLongNose,
      PFBeautyFiterType.faceEyeSpace,
      PFBeautyFiterType.faceSmile,
      PFBeautyFiterType.faceEyeRotate,
      PFBeautyFiterType.faceCanthus,
      PFBeautyFiterType.faceBlurStrength,
      PFBeautyFiterType.faceWhitenStrength,
      PFBeautyFiterType.faceRuddyStrength,
      PFBeautyFiterType.faceSharpenStrength,
      PFBeautyFiterType.faceNewWhitenStrength,
      PFBeautyFiterType.faceQualityStrength,
      PFBeautyFiterType.faceEyeBrighten,
      // 跳過以下可能導致崩潰的類型:
      // PFBeautyFiterType.filterName,
      // PFBeautyFiterType.filterStrength,
      // PFBeautyFiterType.lvmu,
      // PFBeautyFiterType.sticker2DFilter,  // 這是索引27，導致崩潰的元兇
      // PFBeautyFiterType.typeOneKey,
      // PFBeautyFiterType.watermark,
      // PFBeautyFiterType.extend,
    ];
    
    // 應用美膚參數 - 只使用安全的類型
    for (var type in safeBeautyTypes) {
      final savedValue = prefs.getDouble(type.toString());
      if (savedValue != null) {
        hasAnySavedSettings = true;
        try {
          await Helper.pixelFreeSetBeautyFilterParam(type, savedValue);
          debugPrint('✅ 成功設置美顏參數: $type = $savedValue');
        } catch (e) {
          debugPrint('❌ 設置美顏參數失敗: $type, 錯誤: $e');
          // 跳過這個參數，繼續處理其他參數
        }
      }
    }
    
    // 加載保存的濾鏡設置
    try {
      final savedFilterName = prefs.getString('current_filter');
      final savedFilterValue = prefs.getDouble('filter_${savedFilterName}');
      
      if (savedFilterName != null && savedFilterValue != null) {
        await Helper.pixelFreeSetFilterParam(savedFilterName, savedFilterValue);
        debugPrint('✅ 成功設置濾鏡: $savedFilterName = $savedFilterValue');
      } else {
        // 如果没有保存的濾鏡設置，使用默認值
        await Helper.pixelFreeSetFilterParam('origin', 0.5);
        // 保存默認值
        await prefs.setString('current_filter', 'origin');
        await prefs.setDouble('filter_origin', 0.5);
        debugPrint('✅ 使用默認濾鏡設置');
      }
    } catch (e) {
      debugPrint('❌ 設置濾鏡失敗: $e');
    }
    
    // 如果没有保存的設置，應用默認值
    if (!hasAnySavedSettings) {
      final defaultSettings = {
        PFBeautyFiterType.faceWhitenStrength: 0.2,
        PFBeautyFiterType.faceRuddyStrength: 0.6,
        PFBeautyFiterType.faceBlurStrength: 0.7,
        PFBeautyFiterType.faceEyeBrighten: 0.0,
        PFBeautyFiterType.faceSharpenStrength: 0.0,
        PFBeautyFiterType.faceQualityStrength: 0.2,
        PFBeautyFiterType.eyeStrength: 0.2,
        PFBeautyFiterType.faceThinning: 0.2,
        PFBeautyFiterType.faceNarrow: 0.2,
        PFBeautyFiterType.faceChin: 0.5,
        PFBeautyFiterType.faceV: 0.2,
        PFBeautyFiterType.faceSmall: 0.2,
        PFBeautyFiterType.faceNose: 0.2,
        PFBeautyFiterType.faceForehead: 0.5,
        PFBeautyFiterType.faceMouth: 0.5,
        PFBeautyFiterType.facePhiltrum: 0.5,
        PFBeautyFiterType.faceLongNose: 0.5,
        PFBeautyFiterType.faceEyeSpace: 0.5,
        PFBeautyFiterType.faceEyeRotate: 0.5,
        PFBeautyFiterType.faceCanthus: 0.0,
      };
      
      // 設置默認美顏參數
      for (var entry in defaultSettings.entries) {
        try {
          await Helper.pixelFreeSetBeautyFilterParam(entry.key, entry.value);
          await prefs.setDouble(entry.key.toString(), entry.value);
          debugPrint('✅ 設置默認美顏參數: ${entry.key} = ${entry.value}');
        } catch (e) {
          debugPrint('❌ 設置默認美顏參數失敗: ${entry.key}, 錯誤: $e');
        }
      }
    }
    
    debugPrint('🎨 美顏參數初始化完成');
  }

  // 處理用戶按返回鍵的邏輯
  Future<bool> _onWillPop() async {
    debugPrint('🔙 用戶按下返回鍵，開始安全退出流程...');
    
    // 如果正在退出中，阻止返回鍵
    if (_isExiting) {
      debugPrint('⚠️ 正在退出中，阻止返回鍵操作');
      return false;
    }
    
    // 開始安全退出流程
    _safeExit();
    return false; // 返回 false，由 _safeExit 控制何時真正退出
  }

  // 顯示退出進度對話框
  void _showExitProgressDialog() {
    if (!mounted) return;
    
    showDialog(
      context: context,
      barrierDismissible: false,
      barrierColor: Colors.black54,
      builder: (context) => WillPopScope(
        onWillPop: () async => false, // 禁止通過返回鍵關閉對話框
        child: Dialog(
          backgroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Container(
            padding: EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 標題
                Row(
                  children: [
                    Icon(Icons.exit_to_app, color: Colors.orange, size: 28),
                    SizedBox(width: 12),
                    Text(
                      '正在退出直播',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 24),
                
                // 進度條
                Container(
                  width: double.infinity,
                  child: Column(
                    children: [
                      // 圓形進度指示器
                      SizedBox(
                        width: 60,
                        height: 60,
                        child: Stack(
                          children: [
                            // 背景圓圈
                            Container(
                              width: 60,
                              height: 60,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: Colors.grey[300]!,
                                  width: 4,
                                ),
                              ),
                            ),
                            // 進度圓圈
                            SizedBox(
                              width: 60,
                              height: 60,
                              child: CircularProgressIndicator(
                                value: _exitProgress,
                                strokeWidth: 4,
                                backgroundColor: Colors.transparent,
                                valueColor: AlwaysStoppedAnimation<Color>(Colors.orange),
                              ),
                            ),
                            // 百分比文字
                            Center(
                              child: Text(
                                '${(_exitProgress * 100).toInt()}%',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.orange,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      SizedBox(height: 16),
                      
                      // 線性進度條
                      ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: LinearProgressIndicator(
                          value: _exitProgress,
                          backgroundColor: Colors.grey[300],
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.orange),
                          minHeight: 8,
                        ),
                      ),
                      SizedBox(height: 16),
                      
                      // 狀態文字
                      Text(
                        _exitStatusText.isEmpty ? '準備退出...' : _exitStatusText,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[700],
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 16),
                
                // 提示文字
                Text(
                  '請稍候，系統正在安全關閉所有連接...',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // 更新退出進度
  void _updateExitProgress(double progress, String statusText) {
    if (!mounted) return;
    
    setState(() {
      _exitProgress = progress.clamp(0.0, 1.0);
      _exitStatusText = statusText;
    });
    
    debugPrint('🔄 退出進度更新: ${(_exitProgress * 100).toInt()}% - $statusText');
  }

  // 改進的安全退出方法 - 防止當機
  Future<void> _safeExit() async {
    debugPrint('🚪 用戶點擊關閉按鈕，開始安全退出流程...');
    debugPrint('🔍 當前狀態：_isStreaming = $_isStreaming, _streamingStatus = $_streamingStatus');
    
    // 防止重複執行
    if (_isExiting) {
      debugPrint('⚠️ 已經在退出流程中，忽略重複調用');
      return;
    }
    
    try {
      // 標記開始退出
      setState(() {
        _isExiting = true;
        _exitProgress = 0.0;
        _exitStatusText = '準備退出...';
      });
      
      // 顯示進度對話框
      _showExitProgressDialog();
      
      // 設置整體退出超時時間
      await _safeExitInternal().timeout(
        Duration(seconds: 15), // 增加超時時間，給進度顯示更多時間
        onTimeout: () {
          debugPrint('⏰ 安全退出超時，執行強制退出');
          _updateExitProgress(1.0, '退出超時，強制退出中...');
          _forceExit();
        },
      );
    } catch (e) {
      debugPrint('❌ 安全退出過程中發生錯誤: $e');
      _updateExitProgress(1.0, '退出過程中出現錯誤...');
      _forceExit();
    }
  }

  // 內部安全退出邏輯
  Future<void> _safeExitInternal() async {
    debugPrint('🔄 開始內部退出流程...');
    
    // 1. 初始化進度 (10%)
    _updateExitProgress(0.1, '檢查推流狀態...');
    await Future.delayed(Duration(milliseconds: 200));
    
    if (!mounted) return;

    // 2. 停止推流（帶超時）
    if (_isStreaming) {
      debugPrint('🛑 正在停止推流...');
      _updateExitProgress(0.3, '正在停止推流...');
      
      try {
        await _stopStreaming().timeout(Duration(seconds: 8));
        debugPrint('✅ 推流停止完成');
        _updateExitProgress(0.6, '推流已停止');
      } catch (e) {
        debugPrint('⚠️ 停止推流超時或失敗: $e');
        _updateExitProgress(0.6, '停止推流失敗，強制清理中...');
        _forceStopCleanup(); // 強制清理
      }
    } else {
      debugPrint('ℹ️ 推流已停止，跳過停止流程');
      _updateExitProgress(0.6, '推流已停止');
    }

    if (!mounted) return;
    await Future.delayed(Duration(milliseconds: 300));

    // 3. 確保所有資源已清理
    debugPrint('🧹 執行最終清理...');
    _updateExitProgress(0.8, '清理系統資源...');
    
    try {
      await Future.delayed(Duration(milliseconds: 200));
      _forceStopCleanup(); // 最後確保清理
      debugPrint('✅ 資源清理完成');
      _updateExitProgress(0.95, '清理完成');
    } catch (e) {
      debugPrint('⚠️ 最終清理失敗: $e');
      _updateExitProgress(0.95, '清理過程中出現錯誤');
    }

    if (!mounted) return;
    await Future.delayed(Duration(milliseconds: 300));

    // 4. 準備退出
    _updateExitProgress(1.0, '退出完成');
    await Future.delayed(Duration(milliseconds: 500)); // 讓用戶看到100%

    // 5. 關閉進度對話框並返回上一頁
    if (mounted) {
      debugPrint('📤 關閉對話框並返回上一頁...');
      try {
        Navigator.of(context, rootNavigator: true).pop(); // 關閉進度對話框
        await Future.delayed(Duration(milliseconds: 100));
        Navigator.of(context).pop(); // 返回上一頁
        debugPrint('✅ 安全退出完成');
      } catch (e) {
        debugPrint('❌ 返回上一頁失敗: $e');
        rethrow; // 拋出異常以觸發強制退出
      }
    } else {
      debugPrint('⚠️ 組件已卸載，無法返回');
    }
  }

  // 強制退出方法
  void _forceExit() {
    debugPrint('💥 執行強制退出...');
    
    try {
      // 強制清理所有資源
      _forceStopCleanup();
      
      // 重置退出狀態
      if (mounted) {
        setState(() {
          _isExiting = false;
          _exitProgress = 0.0;
          _exitStatusText = '';
        });
      }
      
      // 強制關閉所有對話框
      if (mounted) {
        try {
          // 先嘗試關閉當前對話框
          Navigator.of(context, rootNavigator: true).pop();
          Future.delayed(Duration(milliseconds: 100), () {
            if (mounted) {
              Navigator.of(context).pop();
            }
          });
        } catch (e) {
          debugPrint('⚠️ 強制關閉對話框失敗: $e');
          // 嘗試直接返回
          try {
            Navigator.of(context).pop();
          } catch (e2) {
            debugPrint('⚠️ 直接返回也失敗: $e2');
          }
        }
      }
    } catch (e) {
      debugPrint('❌ 強制退出失敗: $e');
      // 最後手段：重置狀態並嘗試退出
      try {
        if (mounted) {
          setState(() {
            _isExiting = false;
            _exitProgress = 0.0;
            _exitStatusText = '';
          });
          Navigator.of(context).pop();
        }
      } catch (e2) {
        debugPrint('❌ 最後退出嘗試也失敗: $e2');
      }
    }
  }
}


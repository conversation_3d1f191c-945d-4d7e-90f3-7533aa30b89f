//
//  RTCPixelfreeBeautyFilter.m
//  flutter_webrtc_plus
//
//  Created by sunmu on 2025/5/7.
//

#import "RTCPixelfreeBeautyFilter.h"
#import <PixelFree/SMPixelFree.h>

@interface RTCPixelfreeBeautyFilter()

@property(nonatomic, strong) SMPixelFree *mPixelFree;
@property(nonatomic, assign) BOOL isUse;
@end


@implementation RTCPixelfreeBeautyFilter

- (instancetype)initWithDelegate:(id<RTCBeautyFilterDelegate>)delegate {
    self = [super init];
    if (self) {
        _isUse = YES;
        _delegate = delegate;
        [self setup];
    }
    return self;
}

- (void)setup {
    // Init video filter
    
    NSString *face_FiltePath = [[NSBundle mainBundle] pathForResource:@"filter_model.bundle"
                                                               ofType:nil];
    NSString *authFile = [[NSBundle mainBundle] pathForResource:@"pixelfreeAuth.lic"
             ofType:nil];

    self.mPixelFree = [[SMPixelFree alloc] initWithProcessContext:nil
                                                    srcFilterPath:face_FiltePath
                                                         authFile:authFile];
}


- (void)processVideoFrame:(CVPixelBufferRef)imageBuffer {
    CVPixelBufferLockBaseAddress(imageBuffer, 0);
 
    
    size_t w = CVPixelBufferGetWidth(imageBuffer);
    size_t h = CVPixelBufferGetHeight(imageBuffer);
    if (_delegate && _isUse) {
        [self.mPixelFree processWithBuffer:imageBuffer rotationMode:PFRotationMode0];
        [_delegate didReceivePixelBuffer:imageBuffer width:w height:h timestamp:0];
    } else {
        [_delegate didReceivePixelBuffer:imageBuffer width:w height:h timestamp:0];
    }
    CVPixelBufferUnlockBaseAddress(imageBuffer, 0);
}

- (void)pixelFreeSetBeautyFiterParam:(int)key value:(void *)value {
    [self.mPixelFree pixelFreeSetBeautyFiterParam:key value:value];
}

- (BOOL)pixelFreeGetBeautyUseEffect{
    return _isUse;
}

- (void)pixelFreeSetBeautyUseEffect:(BOOL)isUse {
    _isUse = isUse;
}

- (void)pixelFreeSetStickerBundle:(NSString *)path {
    if([path isEqualToString:@"origin"]){
        [self.mPixelFree pixelFreeSetBeautyFiterParam:PFBeautyFiterSticker2DFilter value:NULL];
    } else {
        [self.mPixelFree pixelFreeSetBeautyFiterParam:PFBeautyFiterSticker2DFilter value:(void *)[path UTF8String]];
    }
    
}


- (void)releaseInstance {
}

@end

import 'dart:core';

import 'package:flutter/foundation.dart'
    show debugDefaultTargetPlatformOverride;
import 'package:flutter/material.dart';
import 'package:flutter_background/flutter_background.dart';
import 'package:flutter_webrtc_example/src/capture_frame_sample.dart';
import 'package:flutter_webrtc_plus/flutter_webrtc_plus.dart';

import 'src/device_enumeration_sample.dart';
import 'src/get_display_media_sample.dart';
import 'src/get_user_media_sample.dart'
    if (dart.library.html) 'src/get_user_media_sample_web.dart';
import 'src/loopback_data_channel_sample.dart';
import 'src/loopback_sample_unified_tracks.dart';
import 'src/route_item.dart';
import 'src/stream_settings.dart';
import 'src/home_page.dart';

void main() {
  WidgetsFlutterBinding.ensureInitialized();
  if (WebRTC.platformIsDesktop) {
    debugDefaultTargetPlatformOverride = TargetPlatform.fuchsia;
  } else if (WebRTC.platformIsAndroid) {
    //startForegroundService();
  }
  runApp(MyApp());
}

Future<bool> startForegroundService() async {
  final androidConfig = FlutterBackgroundAndroidConfig(
    notificationTitle: 'Title of the notification',
    notificationText: 'Text of the notification',
    notificationImportance: AndroidNotificationImportance.normal,
    notificationIcon: AndroidResource(
        name: 'background_icon',
        defType: 'drawable'), // Default is ic_launcher from folder mipmap
  );
  await FlutterBackground.initialize(androidConfig: androidConfig);
  return FlutterBackground.enableBackgroundExecution();
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      title: 'WebRTC Demo',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        visualDensity: VisualDensity.adaptivePlatformDensity,
      ),
      home: HomePage(),
    );
  }
}

class StreamSettingsPage extends StatefulWidget {
  final String? initialStreamUrl;
  
  const StreamSettingsPage({Key? key, this.initialStreamUrl}) : super(key: key);
  
  @override
  _StreamSettingsPageState createState() => _StreamSettingsPageState();
}

class _StreamSettingsPageState extends State<StreamSettingsPage> {
  // 添加编码模式变量
  String _selectedEncodingMode = 'VBR';  // 默认使用 VBR
  final TextEditingController _streamUrlController = TextEditingController();
  
  // 默认推流地址
      final String _defaultStreamUrl = 'https://live.sotime.app/rtc/v1/whip/?app=live&stream=a5d7c05cbe4340df878353838b3008d2&secret=a7f9642a7adc4c19b3e52385cbecc649';

  // 修改默认推流参数
  late StreamSettings _streamSettings;

  @override
  void initState() {
    super.initState();
    // 如果有接收到的推流地址，使用它，否則使用默認地址
    final streamUrl = widget.initialStreamUrl ?? _defaultStreamUrl;
    _streamUrlController.text = streamUrl;
    
    // 初始化默认设置
    _streamSettings = StreamSettings(
      resolution: '640x480',
      fps: 15,
      bitrate: 1000,
      keyframeInterval: 2,
      enableBFrame: false,
      profile: 'baseline',
      codec: 'h264',
      encodingMode: _selectedEncodingMode,
      streamUrl: streamUrl,
      // 如果有初始推流地址，表示来自WebView JS数据
      fromWebViewJS: widget.initialStreamUrl != null,
    );
  }


  @override
  void dispose() {
    _streamUrlController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('推流設置'),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 推流地址输入
            TextField(
              controller: _streamUrlController,
              decoration: InputDecoration(
                labelText: '推流地址',
                hintText: '請輸入推流地址',
                border: OutlineInputBorder(),
              ),
              onChanged: (value) {
                setState(() {
                  _streamSettings = StreamSettings(
                    resolution: _streamSettings.resolution,
                    fps: _streamSettings.fps,
                    bitrate: _streamSettings.bitrate,
                    keyframeInterval: _streamSettings.keyframeInterval,
                    enableBFrame: _streamSettings.enableBFrame,
                    profile: _streamSettings.profile,
                    codec: _streamSettings.codec,
                    encodingMode: _selectedEncodingMode,
                    streamUrl: value,
                    fromWebViewJS: _streamSettings.fromWebViewJS,
                  );
                });
              },
            ),
            SizedBox(height: 16),
            
            // 编码模式选择
            DropdownButtonFormField<String>(
              value: _selectedEncodingMode,
              decoration: InputDecoration(
                labelText: '編碼模式',
                border: OutlineInputBorder(),
              ),
              items: ['VBR', 'CBV'].map((String value) {
                return DropdownMenuItem<String>(
                  value: value,
                  child: Text(value),
                );
              }).toList(),
              onChanged: (String? newValue) {
                if (newValue != null) {
                  setState(() {
                    _selectedEncodingMode = newValue;
                    _streamSettings = StreamSettings(
                      resolution: _streamSettings.resolution,
                      fps: _streamSettings.fps,
                      bitrate: _streamSettings.bitrate,
                      keyframeInterval: _streamSettings.keyframeInterval,
                      enableBFrame: _streamSettings.enableBFrame,
                      profile: _streamSettings.profile,
                      codec: _streamSettings.codec,
                      encodingMode: newValue,
                      streamUrl: _streamUrlController.text,
                      fromWebViewJS: _streamSettings.fromWebViewJS,
                    );
                  });
                }
              },
            ),
            SizedBox(height: 16),
            
            // 分辨率选择
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '分辨率',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    DropdownButtonFormField<String>(
                      value: _streamSettings.resolution,
                      decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                        contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      ),
                      items: [
                        '480x360',
                        '640x480',
                        '1280x720',
                        '1920x1080',
                      ].map((res) {
                        return DropdownMenuItem(
                          value: res,
                          child: Text(res),
                        );
                      }).toList(),
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            _streamSettings = StreamSettings(
                              resolution: value,
                              fps: _streamSettings.fps,
                              bitrate: _streamSettings.bitrate,
                              keyframeInterval: _streamSettings.keyframeInterval,
                              enableBFrame: _streamSettings.enableBFrame,
                              codec: _streamSettings.codec,
                              profile: _streamSettings.profile,
                              encodingMode: _selectedEncodingMode,
                              streamUrl: _streamUrlController.text,
                              fromWebViewJS: _streamSettings.fromWebViewJS,
                            );
                          });
                        }
                      },
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // 帧率选择
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '幀率 (FPS)',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    DropdownButtonFormField<int>(
                      value: _streamSettings.fps,
                      decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                        contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      ),
                      items: [15, 24, 30, 60].map((fps) {
                        return DropdownMenuItem(
                          value: fps,
                          child: Text('$fps fps'),
                        );
                      }).toList(),
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            _streamSettings = StreamSettings(
                              resolution: _streamSettings.resolution,
                              fps: value,
                              bitrate: _streamSettings.bitrate,
                              keyframeInterval: _streamSettings.keyframeInterval,
                              enableBFrame: _streamSettings.enableBFrame,
                              codec: _streamSettings.codec,
                              profile: _streamSettings.profile,
                              encodingMode: _selectedEncodingMode,
                              streamUrl: _streamUrlController.text,
                              fromWebViewJS: _streamSettings.fromWebViewJS,
                            );
                          });
                        }
                      },
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // 码率选择
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '碼率 (kbps)',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    DropdownButtonFormField<int>(
                      value: _streamSettings.bitrate,
                      decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                        contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      ),
                      items: [500, 1000, 2000, 4000, 6000].map((bitrate) {
                        return DropdownMenuItem(
                          value: bitrate,
                          child: Text('$bitrate kbps'),
                        );
                      }).toList(),
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            _streamSettings = StreamSettings(
                              resolution: _streamSettings.resolution,
                              fps: _streamSettings.fps,
                              bitrate: value,
                              keyframeInterval: _streamSettings.keyframeInterval,
                              enableBFrame: _streamSettings.enableBFrame,
                              codec: _streamSettings.codec,
                              profile: _streamSettings.profile,
                              encodingMode: _selectedEncodingMode,
                              streamUrl: _streamUrlController.text,
                              fromWebViewJS: _streamSettings.fromWebViewJS,
                            );
                          });
                        }
                      },
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // 关键帧间隔
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '關鍵幀間隔 (秒)',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    DropdownButtonFormField<int>(
                      value: _streamSettings.keyframeInterval,
                      decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                        contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      ),
                      items: [1, 2, 3, 4, 5].map((interval) {
                        return DropdownMenuItem(
                          value: interval,
                          child: Text('$interval 秒'),
                        );
                      }).toList(),
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            _streamSettings = StreamSettings(
                              resolution: _streamSettings.resolution,
                              fps: _streamSettings.fps,
                              bitrate: _streamSettings.bitrate,
                              keyframeInterval: value,
                              enableBFrame: _streamSettings.enableBFrame,
                              codec: _streamSettings.codec,
                              profile: _streamSettings.profile,
                              encodingMode: _selectedEncodingMode,
                              streamUrl: _streamUrlController.text,
                              fromWebViewJS: _streamSettings.fromWebViewJS,
                            );
                          });
                        }
                      },
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // B帧开关
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'B幀設置',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    SwitchListTile(
                      title: const Text('啟用 B 幀'),
                      value: _streamSettings.enableBFrame,
                      onChanged: (value) {
                        setState(() {
                          _streamSettings = StreamSettings(
                            resolution: _streamSettings.resolution,
                            fps: _streamSettings.fps,
                            bitrate: _streamSettings.bitrate,
                            keyframeInterval: _streamSettings.keyframeInterval,
                            enableBFrame: value,
                            codec: _streamSettings.codec,
                            profile: _streamSettings.profile,
                            encodingMode: _selectedEncodingMode,
                            streamUrl: _streamUrlController.text,
                            fromWebViewJS: _streamSettings.fromWebViewJS,
                          );
                        });
                      },
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // 编码等级选择
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '編碼等級',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    DropdownButtonFormField<String>(
                      value: _streamSettings.profile,
                      decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                        contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      ),
                      items: [
                        DropdownMenuItem(
                          value: 'baseline',
                          child: Text('Baseline (低延遲)'),
                        ),
                        DropdownMenuItem(
                          value: 'main',
                          child: Text('Main (平衡)'),
                        ),
                        DropdownMenuItem(
                          value: 'high',
                          child: Text('High (高質量)'),
                        ),
                      ],
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            _streamSettings = StreamSettings(
                              resolution: _streamSettings.resolution,
                              fps: _streamSettings.fps,
                              bitrate: _streamSettings.bitrate,
                              keyframeInterval: _streamSettings.keyframeInterval,
                              enableBFrame: _streamSettings.enableBFrame,
                              codec: _streamSettings.codec,
                              profile: value,
                              encodingMode: _selectedEncodingMode,
                              streamUrl: _streamUrlController.text,
                              fromWebViewJS: _streamSettings.fromWebViewJS,
                            );
                          });
                        }
                      },
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),

            // 开始推流按钮
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => GetUserMediaSample(
                        streamSettings: _streamSettings,
                      ),
                    ),
                  );
                },
                child: Text('開始推流'),
                style: ElevatedButton.styleFrom(
                  padding: EdgeInsets.symmetric(vertical: 16),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class TestJumpPage extends StatelessWidget {
  final String? receivedUrl;
  
  const TestJumpPage({Key? key, this.receivedUrl}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('✅ 自動跳轉成功'),
        backgroundColor: Colors.green,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Icon(
              Icons.check_circle,
              color: Colors.green,
              size: 64,
            ),
            const SizedBox(height: 16),
            const Text(
              '🎉 頁面跳轉成功！',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.green,
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              '接收到的數據：',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey),
              ),
              child: SelectableText(
                receivedUrl ?? '無數據',
                style: const TextStyle(
                  fontSize: 14,
                  fontFamily: 'monospace',
                ),
              ),
            ),
            const SizedBox(height: 24),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  Navigator.pushReplacement(
                    context,
                    MaterialPageRoute(
                      builder: (context) => StreamSettingsPage(initialStreamUrl: receivedUrl),
                    ),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.all(16),
                ),
                child: const Text(
                  '前往推流設置頁面',
                  style: TextStyle(fontSize: 16),
                ),
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.grey,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.all(16),
                ),
                child: const Text(
                  '返回 WebView',
                  style: TextStyle(fontSize: 16),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

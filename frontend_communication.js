// 優化後的傳送資料到Flutter的函數
function sendDataToFlutter(autoNavigate = true) {
    try {
        // 檢查備援直播是否被勾選
        const useBackup = document.getElementById('useBackup').checked;
        const primaryUrl = document.getElementById('stream-link-url').value;
        const backupUrl = document.getElementById('stream-link2-url').value;
        
        // 根據勾選狀態決定使用哪個直播連結
        const streamUrl = useBackup ? backupUrl : primaryUrl;

        // 驗證URL是否有效
        if (!streamUrl || streamUrl.trim() === '') {
            alert('請輸入有效的直播連結');
            return;
        }

        // 準備要發送的數據
        const dataToSend = JSON.stringify({
            streamUrl: streamUrl,
            useBackup: useBackup,
            autoNavigate: autoNavigate,
            timestamp: Date.now()
        });

        // 方式1: flutter_inappwebview (主要推薦方式)
        if (window.flutter_inappwebview && window.flutter_inappwebview.callHandler) {
            window.flutter_inappwebview.callHandler('sendDataToDart', dataToSend)
                .then(function(result) {
                    console.log('Flutter response:', result);
                    const linkType = useBackup ? '備援直播連結' : '主要直播連結';
                    const jumpMsg = autoNavigate ? '並將自動跳轉到推流頁面' : '';
                    
                    if (result && result.status === 'success') {
                        alert('✓ 直播連結已成功傳送到Flutter ' + jumpMsg + ' (使用' + linkType + ')\n響應: ' + result.message);
                    } else {
                        alert('⚠ 傳送成功但Flutter回應異常: ' + (result ? result.message : '未知錯誤'));
                    }
                })
                .catch(function(error) {
                    console.error('Error sending data to Flutter:', error);
                    alert('❌ 傳送直播連結到Flutter時發生錯誤: ' + error.message);
                    
                    // 嘗試備用方法
                    tryAlternativeMethods(streamUrl, useBackup, autoNavigate);
                });
        }
        // 備用方法
        else {
            tryAlternativeMethods(streamUrl, useBackup, autoNavigate);
        }

    } catch (error) {
        console.error('Error in sendDataToFlutter:', error);
        alert('❌ 傳送資料時發生錯誤: ' + error.message);
    }
}

// 嘗試備用通信方法
function tryAlternativeMethods(streamUrl, useBackup, autoNavigate = true) {
    const linkType = useBackup ? '備援直播連結' : '主要直播連結';
    const jumpMsg = autoNavigate ? '並將自動跳轉' : '';
    let success = false;

    // 方式2: FlutterChannel
    if (window.FlutterChannel && window.FlutterChannel.postMessage) {
        try {
            const dataToSend = JSON.stringify({
                streamUrl: streamUrl,
                useBackup: useBackup,
                autoNavigate: autoNavigate
            });
            window.FlutterChannel.postMessage(dataToSend);
            alert('✓ 直播連結已透過FlutterChannel傳送 ' + jumpMsg + ' (使用' + linkType + ')');
            success = true;
        } catch (error) {
            console.error('FlutterChannel error:', error);
        }
    }

    // 方式3: sendToFlutter (通用方法) - 簡化版本，只發送URL
    if (!success && window.sendToFlutter) {
        try {
            window.sendToFlutter(streamUrl);
            alert('✓ 直播連結已透過sendToFlutter傳送 (使用' + linkType + ')');
            success = true;
        } catch (error) {
            console.error('sendToFlutter error:', error);
        }
    }

    // 方式4: webkit messageHandlers (iOS WebView)
    if (!success && window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.FlutterChannel) {
        try {
            window.webkit.messageHandlers.FlutterChannel.postMessage(streamUrl);
            alert('✓ 直播連結已透過webkit messageHandler傳送 (使用' + linkType + ')');
            success = true;
        } catch (error) {
            console.error('webkit messageHandler error:', error);
        }
    }

    // 方式5: postMessage (最後備用)
    if (!success && window.parent && window.parent.postMessage) {
        try {
            window.parent.postMessage({
                type: 'STREAM_URL',
                url: streamUrl,
                useBackup: useBackup,
                autoNavigate: autoNavigate
            }, '*');
            alert('✓ 直播連結已透過postMessage傳送 ' + jumpMsg + ' (使用' + linkType + ')');
            success = true;
        } catch (error) {
            console.error('postMessage error:', error);
        }
    }

    if (!success) {
        console.warn('No Flutter communication channel found');
        alert('❌ 未找到Flutter通信渠道，請確認WebView配置是否正確');
        
        // 顯示調試信息
        showDebugInfo();
    }
}

// 便捷函數：自動跳轉
function sendAndJump() {
    sendDataToFlutter(true);
}

// 便捷函數：不自動跳轉
function sendOnly() {
    sendDataToFlutter(false);
}

// 顯示調試信息
function showDebugInfo() {
    const debugInfo = {
        'flutter_inappwebview': !!window.flutter_inappwebview,
        'FlutterChannel': !!window.FlutterChannel,
        'sendToFlutter': !!window.sendToFlutter,
        'webkit.messageHandlers': !!(window.webkit && window.webkit.messageHandlers),
        'parent.postMessage': !!(window.parent && window.parent.postMessage),
        'userAgent': navigator.userAgent
    };
    
    console.log('Debug Info:', debugInfo);
    
    // 可選：顯示調試信息給用戶
    const debugText = Object.entries(debugInfo)
        .map(([key, value]) => `${key}: ${value}`)
        .join('\n');
    
    if (confirm('通信失敗。是否要查看調試信息？')) {
        alert('調試信息:\n\n' + debugText);
    }
}

// 接收來自Flutter的數據
window.receiveFromFlutter = function(data) {
    console.log('收到Flutter數據:', data);
    
    if (typeof data === 'string' && data.includes('success')) {
        console.log('Flutter確認接收成功');
    }
    
    // 可選：顯示通知
    // alert('Flutter傳回數據: ' + data);
};

// 設置響應處理函數
window.onFlutterResponse = function(status) {
    console.log('Flutter響應狀態:', status);
    
    if (status === 'success') {
        console.log('Flutter確認已成功接收直播連結');
        // 可以在這裡執行後續操作，例如更新UI狀態
    }
};

// 頁面載入後初始化
document.addEventListener('DOMContentLoaded', function() {
    // 檢查必要的DOM元素是否存在
    const requiredElements = ['useBackup', 'stream-link-url', 'stream-link2-url'];
    const missingElements = requiredElements.filter(id => !document.getElementById(id));
    
    if (missingElements.length > 0) {
        console.warn('缺少必要的DOM元素:', missingElements);
    }
    
    // 測試Flutter通信是否可用
    setTimeout(function() {
        if (window.flutter_inappwebview && window.flutter_inappwebview.callHandler) {
            console.log('✓ Flutter InAppWebView通信已就緒');
        } else {
            console.log('⚠ Flutter InAppWebView通信未檢測到，將使用備用方法');
        }
    }, 1000);
});

// 測試函數：用於調試
function testFlutterCommunication() {
    const testData = 'test-stream-url-' + Date.now();
    console.log('測試Flutter通信，發送數據:', testData);
    
    if (window.flutter_inappwebview && window.flutter_inappwebview.callHandler) {
        window.flutter_inappwebview.callHandler('sendDataToDart', testData)
            .then(function(result) {
                console.log('測試成功，Flutter響應:', result);
                alert('✓ Flutter通信測試成功！');
            })
            .catch(function(error) {
                console.error('測試失敗:', error);
                alert('❌ Flutter通信測試失敗: ' + error.message);
            });
    } else {
        alert('❌ Flutter InAppWebView未檢測到');
    }
} 
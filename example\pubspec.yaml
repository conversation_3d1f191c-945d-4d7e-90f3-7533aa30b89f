name: flutter_webrtc_example
description: Demonstrates how to use the webrtc plugin.
version: 1.0.0
publish_to: none
environment:
  sdk: '>=3.3.0 <4.0.0'
  
dependencies:
  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.2
  flutter:
    sdk: flutter
  flutter_background: ^1.0.0
  flutter_webrtc_plus:
    path: ../
  # Required for MediaRecorder example
  path_provider: ^2.0.2
  permission_handler: ^11.3.1
  sdp_transform: ^0.3.2
  dio: ^5.4.0
  shared_preferences: ^2.2.2
  url_launcher: ^6.1.14
  flutter_inappwebview: ^6.0.0


dev_dependencies:
  flutter_test:
    sdk: flutter

  pedantic: ^1.11.0

# For information on the generic Dart part of this file, see the
# following page: https://www.dartlang.org/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  assets:
    - assets/icons/
    - assets/bundle/

  # To add assets to your application, add an assets section, like this:
  # assets:
  #  - images/a_dot_burr.jpeg
  #  - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.io/assets-and-images/#resolution-aware.

  # For details regarding adding assets from package dependencies, see
  # https://flutter.io/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies, 
  # see https://flutter.io/custom-fonts/#from-packages
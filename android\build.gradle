group 'com.cloudwebrtc.webrtc'
version '1.0-SNAPSHOT'

buildscript {
    ext.kotlin_version = '1.9.21'
    repositories {
        google()
        mavenCentral()
        jcenter()

        flatDir {
            dirs 'libs'
        }
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:7.4.2'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath 'de.undercouch:gradle-download-task:4.1.2'
    }
}

rootProject.allprojects {
    repositories {
        google()
        mavenCentral()
        maven { url 'https://jitpack.io' }
    }
}

apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'
apply plugin: 'de.undercouch.download'

android {
    if (project.android.hasProperty("namespace")) {
        namespace 'com.cloudwebrtc.webrtc'
    }
    compileSdkVersion 31

    defaultConfig {
        minSdkVersion 21
        testInstrumentationRunner 'androidx.test.runner.AndroidJUnitRunner'
        consumerProguardFiles 'proguard-rules.pro'
    }

    lintOptions {
        disable 'InvalidPackage'
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }
    sourceSets {
        main {
            jni {
                srcDirs 'src/main/jni', 'src/main/jniLibs'
            }
        }
    }

    // Specify tflite file should not be compressed for the app apk
    aaptOptions {
        noCompress "tflite"
    }
}

// import DownloadModels task
project.ext.ASSET_DIR = projectDir.toString() + '/src/main/assets'
project.ext.TEST_ASSETS_DIR = projectDir.toString() + '/src/androidTest/assets'

// Download default models; if you wish to use your own models then
// place them in the "assets" directory and comment out this line.
apply from: 'download_models.gradle'

dependencies {
    implementation 'tech.waterbus:webrtc:128.6613.02'
    implementation 'com.github.davidliu:audioswitch:89582c47c9a04c62f90aa5e57251af4800a62c9a'
    implementation 'androidx.annotation:annotation:1.8.0'
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3"
    implementation "org.jetbrains.kotlin:kotlin-stdlib:1.9.21"

    // ML Kit
    //noinspection GradleDependency
    implementation 'com.google.mediapipe:tasks-vision:0.10.11'
    implementation 'com.google.android.gms:play-services-tflite-java:16.1.0'
    implementation 'com.google.android.gms:play-services-tflite-gpu:16.2.0'

    // Beauty Filters
    implementation 'tech.waterbus:gpupixel:1.0.2'

    implementation 'io.github.uu-code007:lib_pixelFree:2.4.9'
}

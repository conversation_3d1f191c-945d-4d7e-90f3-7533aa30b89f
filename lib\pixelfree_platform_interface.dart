enum PFBeautyFiterType {
  // 大眼
  eyeStrength,
  // 瘦脸
  faceThinning,
  // 窄脸
  faceNarrow,
  // 下巴
  faceChin,
  // V脸
  faceV,
  // 小脸
  faceSmall,
  // 鼻子
  faceNose,
  // 额头
  faceForehead,
  // 嘴巴
  faceMouth,
  // 人中
  facePhiltrum,
  // 长鼻
  faceLongNose,
  // 眼距
  faceEyeSpace,
  // 微笑嘴角
  faceSmile,
  // 旋转眼睛
  faceEyeRotate,
  // 开眼角
  faceCanthus,
  // 磨皮
  faceBlurStrength,
  // 美白
  faceWhitenStrength,
  // 红润
  faceRuddyStrength,
  // 锐化
  faceSharpenStrength,
  // 新美白算法
  faceNewWhitenStrength,
  // 画质增强
  faceQualityStrength,
  // 亮眼
  faceEyeBrighten,
  // 滤镜类型
  filterName,
  // 滤镜强度
  filterStrength,
  // 绿幕
  lvmu,
  // 2D贴纸
  sticker2DFilter,
  // 一键美颜
  typeOneKey,
  // 水印
  watermark,
  // 扩展字段
  extend,
}


// Generated by dart2js (NullSafetyMode.sound, csp, deferred-serialization, intern-composite-values), the Dart to JavaScript compiler version: 2.19.6.
// The code supports the following hooks:
// dartPrint(message):
//    if this function is defined it is called instead of the Dart [print]
//    method.
//
// dartMainRunner(main, args):
//    if this function is defined, the Dart [main] method will not be invoked
//    directly. Instead, a closure that will invoke [main], and its arguments
//    [args] is passed to [dart<PERSON>ain<PERSON>unner].
//
// dartDeferredLibraryLoader(uri, successCallback, errorCallback, loadId):
//    if this function is defined, it will be called when a deferred library
//    is loaded. It should load and eval the javascript of `uri`, and call
//    successCallback. If it fails to do so, it should call errorCallback with
//    an error. The loadId argument is the deferred import that resulted in
//    this uri being loaded.
//
// dartCallInstrumentation(id, qualifiedName):
//    if this function is defined, it will be called at each entry of a
//    method or constructor. Used only when compiling programs with
//    --experiment-call-instrumentation.
(function dartProgram() {
  function copyProperties(from, to) {
    var keys = Object.keys(from);
    for (var i = 0; i < keys.length; i++) {
      var key = keys[i];
      to[key] = from[key];
    }
  }
  function mixinPropertiesHard(from, to) {
    var keys = Object.keys(from);
    for (var i = 0; i < keys.length; i++) {
      var key = keys[i];
      if (!to.hasOwnProperty(key))
        to[key] = from[key];
    }
  }
  function mixinPropertiesEasy(from, to) {
    Object.assign(to, from);
  }
  var supportsDirectProtoAccess = function() {
    var cls = function() {
    };
    cls.prototype = {p: {}};
    var object = new cls();
    if (!(object.__proto__ && object.__proto__.p === cls.prototype.p))
      return false;
    try {
      if (typeof navigator != "undefined" && typeof navigator.userAgent == "string" && navigator.userAgent.indexOf("Chrome/") >= 0)
        return true;
      if (typeof version == "function" && version.length == 0) {
        var v = version();
        if (/^\d+\.\d+\.\d+\.\d+$/.test(v))
          return true;
      }
    } catch (_) {
    }
    return false;
  }();
  function inherit(cls, sup) {
    cls.prototype.constructor = cls;
    cls.prototype["$is" + cls.name] = cls;
    if (sup != null) {
      if (supportsDirectProtoAccess) {
        cls.prototype.__proto__ = sup.prototype;
        return;
      }
      var clsPrototype = Object.create(sup.prototype);
      copyProperties(cls.prototype, clsPrototype);
      cls.prototype = clsPrototype;
    }
  }
  function inheritMany(sup, classes) {
    for (var i = 0; i < classes.length; i++)
      inherit(classes[i], sup);
  }
  function mixinEasy(cls, mixin) {
    mixinPropertiesEasy(mixin.prototype, cls.prototype);
    cls.prototype.constructor = cls;
  }
  function mixinHard(cls, mixin) {
    mixinPropertiesHard(mixin.prototype, cls.prototype);
    cls.prototype.constructor = cls;
  }
  function lazyOld(holder, name, getterName, initializer) {
    var uninitializedSentinel = holder;
    holder[name] = uninitializedSentinel;
    holder[getterName] = function() {
      holder[getterName] = function() {
        A.throwCyclicInit(name);
      };
      var result;
      var sentinelInProgress = initializer;
      try {
        if (holder[name] === uninitializedSentinel) {
          result = holder[name] = sentinelInProgress;
          result = holder[name] = initializer();
        } else
          result = holder[name];
      } finally {
        if (result === sentinelInProgress)
          holder[name] = null;
        holder[getterName] = function() {
          return this[name];
        };
      }
      return result;
    };
  }
  function lazy(holder, name, getterName, initializer) {
    var uninitializedSentinel = holder;
    holder[name] = uninitializedSentinel;
    holder[getterName] = function() {
      if (holder[name] === uninitializedSentinel)
        holder[name] = initializer();
      holder[getterName] = function() {
        return this[name];
      };
      return holder[name];
    };
  }
  function lazyFinal(holder, name, getterName, initializer) {
    var uninitializedSentinel = holder;
    holder[name] = uninitializedSentinel;
    holder[getterName] = function() {
      if (holder[name] === uninitializedSentinel) {
        var value = initializer();
        if (holder[name] !== uninitializedSentinel)
          A.throwLateFieldADI(name);
        holder[name] = value;
      }
      var finalValue = holder[name];
      holder[getterName] = function() {
        return finalValue;
      };
      return finalValue;
    };
  }
  function makeConstList(list) {
    list.immutable$list = Array;
    list.fixed$length = Array;
    return list;
  }
  function convertToFastObject(properties) {
    function t() {
    }
    t.prototype = properties;
    new t();
    return properties;
  }
  function convertAllToFastObject(arrayOfObjects) {
    for (var i = 0; i < arrayOfObjects.length; ++i)
      convertToFastObject(arrayOfObjects[i]);
  }
  var functionCounter = 0;
  function instanceTearOffGetter(isIntercepted, parameters) {
    var cache = null;
    return isIntercepted ? function(receiver) {
      if (cache === null)
        cache = A.closureFromTearOff(parameters);
      return new cache(receiver, this);
    } : function() {
      if (cache === null)
        cache = A.closureFromTearOff(parameters);
      return new cache(this, null);
    };
  }
  function staticTearOffGetter(parameters) {
    var cache = null;
    return function() {
      if (cache === null)
        cache = A.closureFromTearOff(parameters).prototype;
      return cache;
    };
  }
  var typesOffset = 0;
  function tearOffParameters(container, isStatic, isIntercepted, requiredParameterCount, optionalParameterDefaultValues, callNames, funsOrNames, funType, applyIndex, needsDirectAccess) {
    if (typeof funType == "number")
      funType += typesOffset;
    return {co: container, iS: isStatic, iI: isIntercepted, rC: requiredParameterCount, dV: optionalParameterDefaultValues, cs: callNames, fs: funsOrNames, fT: funType, aI: applyIndex || 0, nDA: needsDirectAccess};
  }
  function installStaticTearOff(holder, getterName, requiredParameterCount, optionalParameterDefaultValues, callNames, funsOrNames, funType, applyIndex) {
    var parameters = tearOffParameters(holder, true, false, requiredParameterCount, optionalParameterDefaultValues, callNames, funsOrNames, funType, applyIndex, false);
    var getterFunction = staticTearOffGetter(parameters);
    holder[getterName] = getterFunction;
  }
  function installInstanceTearOff(prototype, getterName, isIntercepted, requiredParameterCount, optionalParameterDefaultValues, callNames, funsOrNames, funType, applyIndex, needsDirectAccess) {
    isIntercepted = !!isIntercepted;
    var parameters = tearOffParameters(prototype, false, isIntercepted, requiredParameterCount, optionalParameterDefaultValues, callNames, funsOrNames, funType, applyIndex, !!needsDirectAccess);
    var getterFunction = instanceTearOffGetter(isIntercepted, parameters);
    prototype[getterName] = getterFunction;
  }
  function setOrUpdateInterceptorsByTag(newTags) {
    var tags = init.interceptorsByTag;
    if (!tags) {
      init.interceptorsByTag = newTags;
      return;
    }
    copyProperties(newTags, tags);
  }
  function setOrUpdateLeafTags(newTags) {
    var tags = init.leafTags;
    if (!tags) {
      init.leafTags = newTags;
      return;
    }
    copyProperties(newTags, tags);
  }
  function updateTypes(newTypes) {
    var types = init.types;
    var length = types.length;
    types.push.apply(types, newTypes);
    return length;
  }
  function updateHolder(holder, newHolder) {
    copyProperties(newHolder, holder);
    return holder;
  }
  var hunkHelpers = function() {
    var mkInstance = function(isIntercepted, requiredParameterCount, optionalParameterDefaultValues, callNames, applyIndex) {
        return function(container, getterName, name, funType) {
          return installInstanceTearOff(container, getterName, isIntercepted, requiredParameterCount, optionalParameterDefaultValues, callNames, [name], funType, applyIndex, false);
        };
      },
      mkStatic = function(requiredParameterCount, optionalParameterDefaultValues, callNames, applyIndex) {
        return function(container, getterName, name, funType) {
          return installStaticTearOff(container, getterName, requiredParameterCount, optionalParameterDefaultValues, callNames, [name], funType, applyIndex);
        };
      };
    return {inherit: inherit, inheritMany: inheritMany, mixin: mixinEasy, mixinHard: mixinHard, installStaticTearOff: installStaticTearOff, installInstanceTearOff: installInstanceTearOff, _instance_0u: mkInstance(0, 0, null, ["call$0"], 0), _instance_1u: mkInstance(0, 1, null, ["call$1"], 0), _instance_2u: mkInstance(0, 2, null, ["call$2"], 0), _instance_0i: mkInstance(1, 0, null, ["call$0"], 0), _instance_1i: mkInstance(1, 1, null, ["call$1"], 0), _instance_2i: mkInstance(1, 2, null, ["call$2"], 0), _static_0: mkStatic(0, null, ["call$0"], 0), _static_1: mkStatic(1, null, ["call$1"], 0), _static_2: mkStatic(2, null, ["call$2"], 0), makeConstList: makeConstList, lazy: lazy, lazyFinal: lazyFinal, lazyOld: lazyOld, updateHolder: updateHolder, convertToFastObject: convertToFastObject, updateTypes: updateTypes, setOrUpdateInterceptorsByTag: setOrUpdateInterceptorsByTag, setOrUpdateLeafTags: setOrUpdateLeafTags};
  }();
  function initializeDeferredHunk(hunk) {
    typesOffset = init.types.length;
    hunk(hunkHelpers, init, holders, $);
  }
  var A = {JS_CONST: function JS_CONST() {
    },
    LateError$fieldNI(fieldName) {
      return new A.LateError("Field '" + fieldName + "' has not been initialized.");
    },
    SystemHash_combine(hash, value) {
      hash = hash + value & 536870911;
      hash = hash + ((hash & 524287) << 10) & 536870911;
      return hash ^ hash >>> 6;
    },
    SystemHash_finish(hash) {
      hash = hash + ((hash & 67108863) << 3) & 536870911;
      hash ^= hash >>> 11;
      return hash + ((hash & 16383) << 15) & 536870911;
    },
    checkNotNullable(value, $name, $T) {
      return value;
    },
    SubListIterable$(_iterable, _start, _endOrLength, $E) {
      A.RangeError_checkNotNegative(_start, "start");
      if (_endOrLength != null) {
        A.RangeError_checkNotNegative(_endOrLength, "end");
        if (_start > _endOrLength)
          A.throwExpression(A.RangeError$range(_start, 0, _endOrLength, "start", null));
      }
      return new A.SubListIterable(_iterable, _start, _endOrLength, $E._eval$1("SubListIterable<0>"));
    },
    MappedIterable_MappedIterable(iterable, $function, $S, $T) {
      if (type$.EfficientLengthIterable_dynamic._is(iterable))
        return new A.EfficientLengthMappedIterable(iterable, $function, $S._eval$1("@<0>")._bind$1($T)._eval$1("EfficientLengthMappedIterable<1,2>"));
      return new A.MappedIterable(iterable, $function, $S._eval$1("@<0>")._bind$1($T)._eval$1("MappedIterable<1,2>"));
    },
    _CopyingBytesBuilder: function _CopyingBytesBuilder(t0) {
      this.__internal$_length = 0;
      this._buffer = t0;
    },
    LateError: function LateError(t0) {
      this._message = t0;
    },
    CodeUnits: function CodeUnits(t0) {
      this._string = t0;
    },
    SentinelValue: function SentinelValue() {
    },
    EfficientLengthIterable: function EfficientLengthIterable() {
    },
    ListIterable: function ListIterable() {
    },
    SubListIterable: function SubListIterable(t0, t1, t2, t3) {
      var _ = this;
      _.__internal$_iterable = t0;
      _._start = t1;
      _._endOrLength = t2;
      _.$ti = t3;
    },
    ListIterator: function ListIterator(t0, t1, t2) {
      var _ = this;
      _.__internal$_iterable = t0;
      _.__internal$_length = t1;
      _.__internal$_index = 0;
      _.__internal$_current = null;
      _.$ti = t2;
    },
    MappedIterable: function MappedIterable(t0, t1, t2) {
      this.__internal$_iterable = t0;
      this._f = t1;
      this.$ti = t2;
    },
    EfficientLengthMappedIterable: function EfficientLengthMappedIterable(t0, t1, t2) {
      this.__internal$_iterable = t0;
      this._f = t1;
      this.$ti = t2;
    },
    MappedIterator: function MappedIterator(t0, t1, t2) {
      var _ = this;
      _.__internal$_current = null;
      _._iterator = t0;
      _._f = t1;
      _.$ti = t2;
    },
    MappedListIterable: function MappedListIterable(t0, t1, t2) {
      this._source = t0;
      this._f = t1;
      this.$ti = t2;
    },
    WhereIterable: function WhereIterable(t0, t1, t2) {
      this.__internal$_iterable = t0;
      this._f = t1;
      this.$ti = t2;
    },
    WhereIterator: function WhereIterator(t0, t1, t2) {
      this._iterator = t0;
      this._f = t1;
      this.$ti = t2;
    },
    FixedLengthListMixin: function FixedLengthListMixin() {
    },
    UnmodifiableListMixin: function UnmodifiableListMixin() {
    },
    UnmodifiableListBase: function UnmodifiableListBase() {
    },
    Symbol: function Symbol(t0) {
      this.__internal$_name = t0;
    },
    unminifyOrTag(rawClassName) {
      var preserved = init.mangledGlobalNames[rawClassName];
      if (preserved != null)
        return preserved;
      return rawClassName;
    },
    isJsIndexable(object, record) {
      var result;
      if (record != null) {
        result = record.x;
        if (result != null)
          return result;
      }
      return type$.JavaScriptIndexingBehavior_dynamic._is(object);
    },
    S(value) {
      var result;
      if (typeof value == "string")
        return value;
      if (typeof value == "number") {
        if (value !== 0)
          return "" + value;
      } else if (true === value)
        return "true";
      else if (false === value)
        return "false";
      else if (value == null)
        return "null";
      result = J.toString$0$(value);
      return result;
    },
    Primitives_objectHashCode(object) {
      var hash,
        property = $.Primitives__identityHashCodeProperty;
      if (property == null)
        property = $.Primitives__identityHashCodeProperty = Symbol("identityHashCode");
      hash = object[property];
      if (hash == null) {
        hash = Math.random() * 0x3fffffff | 0;
        object[property] = hash;
      }
      return hash;
    },
    Primitives_objectTypeName(object) {
      return A.Primitives__objectTypeNameNewRti(object);
    },
    Primitives__objectTypeNameNewRti(object) {
      var interceptor, dispatchName, $constructor, constructorName;
      if (object instanceof A.Object)
        return A._rtiToString(A.instanceType(object), null);
      interceptor = J.getInterceptor$(object);
      if (interceptor === B.Interceptor_methods || interceptor === B.JavaScriptObject_methods || type$.UnknownJavaScriptObject._is(object)) {
        dispatchName = B.C_JS_CONST(object);
        if (dispatchName !== "Object" && dispatchName !== "")
          return dispatchName;
        $constructor = object.constructor;
        if (typeof $constructor == "function") {
          constructorName = $constructor.name;
          if (typeof constructorName == "string" && constructorName !== "Object" && constructorName !== "")
            return constructorName;
        }
      }
      return A._rtiToString(A.instanceType(object), null);
    },
    Primitives_stringFromNativeUint8List(charCodes, start, end) {
      var i, result, i0, chunkEnd;
      if (end <= 500 && start === 0 && end === charCodes.length)
        return String.fromCharCode.apply(null, charCodes);
      for (i = start, result = ""; i < end; i = i0) {
        i0 = i + 500;
        chunkEnd = i0 < end ? i0 : end;
        result += String.fromCharCode.apply(null, charCodes.subarray(i, chunkEnd));
      }
      return result;
    },
    Primitives_lazyAsJsDate(receiver) {
      if (receiver.date === void 0)
        receiver.date = new Date(receiver._value);
      return receiver.date;
    },
    Primitives_getYear(receiver) {
      var t1 = A.Primitives_lazyAsJsDate(receiver).getUTCFullYear() + 0;
      return t1;
    },
    Primitives_getMonth(receiver) {
      var t1 = A.Primitives_lazyAsJsDate(receiver).getUTCMonth() + 1;
      return t1;
    },
    Primitives_getDay(receiver) {
      var t1 = A.Primitives_lazyAsJsDate(receiver).getUTCDate() + 0;
      return t1;
    },
    Primitives_getHours(receiver) {
      var t1 = A.Primitives_lazyAsJsDate(receiver).getUTCHours() + 0;
      return t1;
    },
    Primitives_getMinutes(receiver) {
      var t1 = A.Primitives_lazyAsJsDate(receiver).getUTCMinutes() + 0;
      return t1;
    },
    Primitives_getSeconds(receiver) {
      var t1 = A.Primitives_lazyAsJsDate(receiver).getUTCSeconds() + 0;
      return t1;
    },
    Primitives_getMilliseconds(receiver) {
      var t1 = A.Primitives_lazyAsJsDate(receiver).getUTCMilliseconds() + 0;
      return t1;
    },
    Primitives_functionNoSuchMethod($function, positionalArguments, namedArguments) {
      var $arguments, namedArgumentList, t1 = {};
      t1.argumentCount = 0;
      $arguments = [];
      namedArgumentList = [];
      t1.argumentCount = positionalArguments.length;
      B.JSArray_methods.addAll$1($arguments, positionalArguments);
      t1.names = "";
      if (namedArguments != null && namedArguments._length !== 0)
        namedArguments.forEach$1(0, new A.Primitives_functionNoSuchMethod_closure(t1, namedArgumentList, $arguments));
      return J.noSuchMethod$1$($function, new A.JSInvocationMirror(B.Symbol_call, 0, $arguments, namedArgumentList, 0));
    },
    Primitives_applyFunction($function, positionalArguments, namedArguments) {
      var t1, argumentCount, jsStub;
      if (Array.isArray(positionalArguments))
        t1 = namedArguments == null || namedArguments._length === 0;
      else
        t1 = false;
      if (t1) {
        argumentCount = positionalArguments.length;
        if (argumentCount === 0) {
          if (!!$function.call$0)
            return $function.call$0();
        } else if (argumentCount === 1) {
          if (!!$function.call$1)
            return $function.call$1(positionalArguments[0]);
        } else if (argumentCount === 2) {
          if (!!$function.call$2)
            return $function.call$2(positionalArguments[0], positionalArguments[1]);
        } else if (argumentCount === 3) {
          if (!!$function.call$3)
            return $function.call$3(positionalArguments[0], positionalArguments[1], positionalArguments[2]);
        } else if (argumentCount === 4) {
          if (!!$function.call$4)
            return $function.call$4(positionalArguments[0], positionalArguments[1], positionalArguments[2], positionalArguments[3]);
        } else if (argumentCount === 5)
          if (!!$function.call$5)
            return $function.call$5(positionalArguments[0], positionalArguments[1], positionalArguments[2], positionalArguments[3], positionalArguments[4]);
        jsStub = $function["call" + "$" + argumentCount];
        if (jsStub != null)
          return jsStub.apply($function, positionalArguments);
      }
      return A.Primitives__generalApplyFunction($function, positionalArguments, namedArguments);
    },
    Primitives__generalApplyFunction($function, positionalArguments, namedArguments) {
      var defaultValuesClosure, t1, defaultValues, interceptor, jsFunction, maxArguments, missingDefaults, keys, _i, defaultValue, used, key,
        $arguments = Array.isArray(positionalArguments) ? positionalArguments : A.List_List$of(positionalArguments, true, type$.dynamic),
        argumentCount = $arguments.length,
        requiredParameterCount = $function.$requiredArgCount;
      if (argumentCount < requiredParameterCount)
        return A.Primitives_functionNoSuchMethod($function, $arguments, namedArguments);
      defaultValuesClosure = $function.$defaultValues;
      t1 = defaultValuesClosure == null;
      defaultValues = !t1 ? defaultValuesClosure() : null;
      interceptor = J.getInterceptor$($function);
      jsFunction = interceptor["call*"];
      if (typeof jsFunction == "string")
        jsFunction = interceptor[jsFunction];
      if (t1) {
        if (namedArguments != null && namedArguments._length !== 0)
          return A.Primitives_functionNoSuchMethod($function, $arguments, namedArguments);
        if (argumentCount === requiredParameterCount)
          return jsFunction.apply($function, $arguments);
        return A.Primitives_functionNoSuchMethod($function, $arguments, namedArguments);
      }
      if (Array.isArray(defaultValues)) {
        if (namedArguments != null && namedArguments._length !== 0)
          return A.Primitives_functionNoSuchMethod($function, $arguments, namedArguments);
        maxArguments = requiredParameterCount + defaultValues.length;
        if (argumentCount > maxArguments)
          return A.Primitives_functionNoSuchMethod($function, $arguments, null);
        if (argumentCount < maxArguments) {
          missingDefaults = defaultValues.slice(argumentCount - requiredParameterCount);
          if ($arguments === positionalArguments)
            $arguments = A.List_List$of($arguments, true, type$.dynamic);
          B.JSArray_methods.addAll$1($arguments, missingDefaults);
        }
        return jsFunction.apply($function, $arguments);
      } else {
        if (argumentCount > requiredParameterCount)
          return A.Primitives_functionNoSuchMethod($function, $arguments, namedArguments);
        if ($arguments === positionalArguments)
          $arguments = A.List_List$of($arguments, true, type$.dynamic);
        keys = Object.keys(defaultValues);
        if (namedArguments == null)
          for (t1 = keys.length, _i = 0; _i < keys.length; keys.length === t1 || (0, A.throwConcurrentModificationError)(keys), ++_i) {
            defaultValue = defaultValues[A._asString(keys[_i])];
            if (B.C__Required === defaultValue)
              return A.Primitives_functionNoSuchMethod($function, $arguments, namedArguments);
            B.JSArray_methods.add$1($arguments, defaultValue);
          }
        else {
          for (t1 = keys.length, used = 0, _i = 0; _i < keys.length; keys.length === t1 || (0, A.throwConcurrentModificationError)(keys), ++_i) {
            key = A._asString(keys[_i]);
            if (namedArguments.containsKey$1(0, key)) {
              ++used;
              B.JSArray_methods.add$1($arguments, namedArguments.$index(0, key));
            } else {
              defaultValue = defaultValues[key];
              if (B.C__Required === defaultValue)
                return A.Primitives_functionNoSuchMethod($function, $arguments, namedArguments);
              B.JSArray_methods.add$1($arguments, defaultValue);
            }
          }
          if (used !== namedArguments._length)
            return A.Primitives_functionNoSuchMethod($function, $arguments, namedArguments);
        }
        return jsFunction.apply($function, $arguments);
      }
    },
    iae(argument) {
      throw A.wrapException(A.argumentErrorValue(argument));
    },
    ioore(receiver, index) {
      if (receiver == null)
        J.get$length$asx(receiver);
      throw A.wrapException(A.diagnoseIndexError(receiver, index));
    },
    diagnoseIndexError(indexable, index) {
      var $length, _s5_ = "index";
      if (!A._isInt(index))
        return new A.ArgumentError(true, index, _s5_, null);
      $length = A._asInt(J.get$length$asx(indexable));
      if (index < 0 || index >= $length)
        return A.IndexError$withLength(index, $length, indexable, _s5_);
      return A.RangeError$value(index, _s5_);
    },
    diagnoseRangeError(start, end, $length) {
      if (start < 0 || start > $length)
        return A.RangeError$range(start, 0, $length, "start", null);
      if (end != null)
        if (end < start || end > $length)
          return A.RangeError$range(end, start, $length, "end", null);
      return new A.ArgumentError(true, end, "end", null);
    },
    argumentErrorValue(object) {
      return new A.ArgumentError(true, object, null, null);
    },
    wrapException(ex) {
      var wrapper, t1;
      if (ex == null)
        ex = new A.NullThrownError();
      wrapper = new Error();
      wrapper.dartException = ex;
      t1 = A.toStringWrapper;
      if ("defineProperty" in Object) {
        Object.defineProperty(wrapper, "message", {get: t1});
        wrapper.name = "";
      } else
        wrapper.toString = t1;
      return wrapper;
    },
    toStringWrapper() {
      return J.toString$0$(this.dartException);
    },
    throwExpression(ex) {
      throw A.wrapException(ex);
    },
    throwConcurrentModificationError(collection) {
      throw A.wrapException(A.ConcurrentModificationError$(collection));
    },
    TypeErrorDecoder_extractPattern(message) {
      var match, $arguments, argumentsExpr, expr, method, receiver;
      message = A.quoteStringForRegExp(message.replace(String({}), "$receiver$"));
      match = message.match(/\\\$[a-zA-Z]+\\\$/g);
      if (match == null)
        match = A._setArrayType([], type$.JSArray_String);
      $arguments = match.indexOf("\\$arguments\\$");
      argumentsExpr = match.indexOf("\\$argumentsExpr\\$");
      expr = match.indexOf("\\$expr\\$");
      method = match.indexOf("\\$method\\$");
      receiver = match.indexOf("\\$receiver\\$");
      return new A.TypeErrorDecoder(message.replace(new RegExp("\\\\\\$arguments\\\\\\$", "g"), "((?:x|[^x])*)").replace(new RegExp("\\\\\\$argumentsExpr\\\\\\$", "g"), "((?:x|[^x])*)").replace(new RegExp("\\\\\\$expr\\\\\\$", "g"), "((?:x|[^x])*)").replace(new RegExp("\\\\\\$method\\\\\\$", "g"), "((?:x|[^x])*)").replace(new RegExp("\\\\\\$receiver\\\\\\$", "g"), "((?:x|[^x])*)"), $arguments, argumentsExpr, expr, method, receiver);
    },
    TypeErrorDecoder_provokeCallErrorOn(expression) {
      return function($expr$) {
        var $argumentsExpr$ = "$arguments$";
        try {
          $expr$.$method$($argumentsExpr$);
        } catch (e) {
          return e.message;
        }
      }(expression);
    },
    TypeErrorDecoder_provokePropertyErrorOn(expression) {
      return function($expr$) {
        try {
          $expr$.$method$;
        } catch (e) {
          return e.message;
        }
      }(expression);
    },
    JsNoSuchMethodError$(_message, match) {
      var t1 = match == null,
        t2 = t1 ? null : match.method;
      return new A.JsNoSuchMethodError(_message, t2, t1 ? null : match.receiver);
    },
    unwrapException(ex) {
      var t1;
      if (ex == null)
        return new A.NullThrownFromJavaScriptException(ex);
      if (ex instanceof A.ExceptionAndStackTrace) {
        t1 = ex.dartException;
        return A.saveStackTrace(ex, t1 == null ? type$.Object._as(t1) : t1);
      }
      if (typeof ex !== "object")
        return ex;
      if ("dartException" in ex)
        return A.saveStackTrace(ex, ex.dartException);
      return A._unwrapNonDartException(ex);
    },
    saveStackTrace(ex, error) {
      if (type$.Error._is(error))
        if (error.$thrownJsError == null)
          error.$thrownJsError = ex;
      return error;
    },
    _unwrapNonDartException(ex) {
      var message, number, ieErrorCode, t1, nsme, notClosure, nullCall, nullLiteralCall, undefCall, undefLiteralCall, nullProperty, undefProperty, undefLiteralProperty, match, _null = null;
      if (!("message" in ex))
        return ex;
      message = ex.message;
      if ("number" in ex && typeof ex.number == "number") {
        number = ex.number;
        ieErrorCode = number & 65535;
        if ((B.JSInt_methods._shrOtherPositive$1(number, 16) & 8191) === 10)
          switch (ieErrorCode) {
            case 438:
              return A.saveStackTrace(ex, A.JsNoSuchMethodError$(A.S(message) + " (Error " + ieErrorCode + ")", _null));
            case 445:
            case 5007:
              t1 = A.S(message);
              return A.saveStackTrace(ex, new A.NullError(t1 + " (Error " + ieErrorCode + ")", _null));
          }
      }
      if (ex instanceof TypeError) {
        nsme = $.$get$TypeErrorDecoder_noSuchMethodPattern();
        notClosure = $.$get$TypeErrorDecoder_notClosurePattern();
        nullCall = $.$get$TypeErrorDecoder_nullCallPattern();
        nullLiteralCall = $.$get$TypeErrorDecoder_nullLiteralCallPattern();
        undefCall = $.$get$TypeErrorDecoder_undefinedCallPattern();
        undefLiteralCall = $.$get$TypeErrorDecoder_undefinedLiteralCallPattern();
        nullProperty = $.$get$TypeErrorDecoder_nullPropertyPattern();
        $.$get$TypeErrorDecoder_nullLiteralPropertyPattern();
        undefProperty = $.$get$TypeErrorDecoder_undefinedPropertyPattern();
        undefLiteralProperty = $.$get$TypeErrorDecoder_undefinedLiteralPropertyPattern();
        match = nsme.matchTypeError$1(message);
        if (match != null)
          return A.saveStackTrace(ex, A.JsNoSuchMethodError$(A._asString(message), match));
        else {
          match = notClosure.matchTypeError$1(message);
          if (match != null) {
            match.method = "call";
            return A.saveStackTrace(ex, A.JsNoSuchMethodError$(A._asString(message), match));
          } else {
            match = nullCall.matchTypeError$1(message);
            if (match == null) {
              match = nullLiteralCall.matchTypeError$1(message);
              if (match == null) {
                match = undefCall.matchTypeError$1(message);
                if (match == null) {
                  match = undefLiteralCall.matchTypeError$1(message);
                  if (match == null) {
                    match = nullProperty.matchTypeError$1(message);
                    if (match == null) {
                      match = nullLiteralCall.matchTypeError$1(message);
                      if (match == null) {
                        match = undefProperty.matchTypeError$1(message);
                        if (match == null) {
                          match = undefLiteralProperty.matchTypeError$1(message);
                          t1 = match != null;
                        } else
                          t1 = true;
                      } else
                        t1 = true;
                    } else
                      t1 = true;
                  } else
                    t1 = true;
                } else
                  t1 = true;
              } else
                t1 = true;
            } else
              t1 = true;
            if (t1) {
              A._asString(message);
              return A.saveStackTrace(ex, new A.NullError(message, match == null ? _null : match.method));
            }
          }
        }
        return A.saveStackTrace(ex, new A.UnknownJsTypeError(typeof message == "string" ? message : ""));
      }
      if (ex instanceof RangeError) {
        if (typeof message == "string" && message.indexOf("call stack") !== -1)
          return new A.StackOverflowError();
        message = function(ex) {
          try {
            return String(ex);
          } catch (e) {
          }
          return null;
        }(ex);
        return A.saveStackTrace(ex, new A.ArgumentError(false, _null, _null, typeof message == "string" ? message.replace(/^RangeError:\s*/, "") : message));
      }
      if (typeof InternalError == "function" && ex instanceof InternalError)
        if (typeof message == "string" && message === "too much recursion")
          return new A.StackOverflowError();
      return ex;
    },
    getTraceFromException(exception) {
      var trace;
      if (exception instanceof A.ExceptionAndStackTrace)
        return exception.stackTrace;
      if (exception == null)
        return new A._StackTrace(exception);
      trace = exception.$cachedTrace;
      if (trace != null)
        return trace;
      return exception.$cachedTrace = new A._StackTrace(exception);
    },
    objectHashCode(object) {
      if (object == null || typeof object != "object")
        return J.get$hashCode$(object);
      else
        return A.Primitives_objectHashCode(object);
    },
    fillLiteralMap(keyValuePairs, result) {
      var index, index0, index1,
        $length = keyValuePairs.length;
      for (index = 0; index < $length; index = index1) {
        index0 = index + 1;
        index1 = index0 + 1;
        result.$indexSet(0, keyValuePairs[index], keyValuePairs[index0]);
      }
      return result;
    },
    invokeClosure(closure, numberOfArguments, arg1, arg2, arg3, arg4) {
      type$.Function._as(closure);
      switch (A._asInt(numberOfArguments)) {
        case 0:
          return closure.call$0();
        case 1:
          return closure.call$1(arg1);
        case 2:
          return closure.call$2(arg1, arg2);
        case 3:
          return closure.call$3(arg1, arg2, arg3);
        case 4:
          return closure.call$4(arg1, arg2, arg3, arg4);
      }
      throw A.wrapException(A.Exception_Exception("Unsupported number of arguments for wrapped closure"));
    },
    convertDartClosureToJS(closure, arity) {
      var $function;
      if (closure == null)
        return null;
      $function = closure.$identity;
      if (!!$function)
        return $function;
      $function = function(closure, arity, invoke) {
        return function(a1, a2, a3, a4) {
          return invoke(closure, arity, a1, a2, a3, a4);
        };
      }(closure, arity, A.invokeClosure);
      closure.$identity = $function;
      return $function;
    },
    Closure_fromTearOff(parameters) {
      var $prototype, $constructor, t2, trampoline, applyTrampoline, i, stub, stub0, stubName, stubCallName,
        container = parameters.co,
        isStatic = parameters.iS,
        isIntercepted = parameters.iI,
        needsDirectAccess = parameters.nDA,
        applyTrampolineIndex = parameters.aI,
        funsOrNames = parameters.fs,
        callNames = parameters.cs,
        $name = funsOrNames[0],
        callName = callNames[0],
        $function = container[$name],
        t1 = parameters.fT;
      t1.toString;
      $prototype = isStatic ? Object.create(new A.StaticClosure().constructor.prototype) : Object.create(new A.BoundClosure(null, null).constructor.prototype);
      $prototype.$initialize = $prototype.constructor;
      if (isStatic)
        $constructor = function static_tear_off() {
          this.$initialize();
        };
      else
        $constructor = function tear_off(a, b) {
          this.$initialize(a, b);
        };
      $prototype.constructor = $constructor;
      $constructor.prototype = $prototype;
      $prototype.$_name = $name;
      $prototype.$_target = $function;
      t2 = !isStatic;
      if (t2)
        trampoline = A.Closure_forwardCallTo($name, $function, isIntercepted, needsDirectAccess);
      else {
        $prototype.$static_name = $name;
        trampoline = $function;
      }
      $prototype.$signature = A.Closure__computeSignatureFunctionNewRti(t1, isStatic, isIntercepted);
      $prototype[callName] = trampoline;
      for (applyTrampoline = trampoline, i = 1; i < funsOrNames.length; ++i) {
        stub = funsOrNames[i];
        if (typeof stub == "string") {
          stub0 = container[stub];
          stubName = stub;
          stub = stub0;
        } else
          stubName = "";
        stubCallName = callNames[i];
        if (stubCallName != null) {
          if (t2)
            stub = A.Closure_forwardCallTo(stubName, stub, isIntercepted, needsDirectAccess);
          $prototype[stubCallName] = stub;
        }
        if (i === applyTrampolineIndex)
          applyTrampoline = stub;
      }
      $prototype["call*"] = applyTrampoline;
      $prototype.$requiredArgCount = parameters.rC;
      $prototype.$defaultValues = parameters.dV;
      return $constructor;
    },
    Closure__computeSignatureFunctionNewRti(functionType, isStatic, isIntercepted) {
      if (typeof functionType == "number")
        return functionType;
      if (typeof functionType == "string") {
        if (isStatic)
          throw A.wrapException("Cannot compute signature for static tearoff.");
        return function(recipe, evalOnReceiver) {
          return function() {
            return evalOnReceiver(this, recipe);
          };
        }(functionType, A.BoundClosure_evalRecipe);
      }
      throw A.wrapException("Error in functionType of tearoff");
    },
    Closure_cspForwardCall(arity, needsDirectAccess, stubName, $function) {
      var getReceiver = A.BoundClosure_receiverOf;
      switch (needsDirectAccess ? -1 : arity) {
        case 0:
          return function(entry, receiverOf) {
            return function() {
              return receiverOf(this)[entry]();
            };
          }(stubName, getReceiver);
        case 1:
          return function(entry, receiverOf) {
            return function(a) {
              return receiverOf(this)[entry](a);
            };
          }(stubName, getReceiver);
        case 2:
          return function(entry, receiverOf) {
            return function(a, b) {
              return receiverOf(this)[entry](a, b);
            };
          }(stubName, getReceiver);
        case 3:
          return function(entry, receiverOf) {
            return function(a, b, c) {
              return receiverOf(this)[entry](a, b, c);
            };
          }(stubName, getReceiver);
        case 4:
          return function(entry, receiverOf) {
            return function(a, b, c, d) {
              return receiverOf(this)[entry](a, b, c, d);
            };
          }(stubName, getReceiver);
        case 5:
          return function(entry, receiverOf) {
            return function(a, b, c, d, e) {
              return receiverOf(this)[entry](a, b, c, d, e);
            };
          }(stubName, getReceiver);
        default:
          return function(f, receiverOf) {
            return function() {
              return f.apply(receiverOf(this), arguments);
            };
          }($function, getReceiver);
      }
    },
    Closure_forwardCallTo(stubName, $function, isIntercepted, needsDirectAccess) {
      var arity, t1;
      if (isIntercepted)
        return A.Closure_forwardInterceptedCallTo(stubName, $function, needsDirectAccess);
      arity = $function.length;
      t1 = A.Closure_cspForwardCall(arity, needsDirectAccess, stubName, $function);
      return t1;
    },
    Closure_cspForwardInterceptedCall(arity, needsDirectAccess, stubName, $function) {
      var getReceiver = A.BoundClosure_receiverOf,
        getInterceptor = A.BoundClosure_interceptorOf;
      switch (needsDirectAccess ? -1 : arity) {
        case 0:
          throw A.wrapException(new A.RuntimeError("Intercepted function with no arguments."));
        case 1:
          return function(entry, interceptorOf, receiverOf) {
            return function() {
              return interceptorOf(this)[entry](receiverOf(this));
            };
          }(stubName, getInterceptor, getReceiver);
        case 2:
          return function(entry, interceptorOf, receiverOf) {
            return function(a) {
              return interceptorOf(this)[entry](receiverOf(this), a);
            };
          }(stubName, getInterceptor, getReceiver);
        case 3:
          return function(entry, interceptorOf, receiverOf) {
            return function(a, b) {
              return interceptorOf(this)[entry](receiverOf(this), a, b);
            };
          }(stubName, getInterceptor, getReceiver);
        case 4:
          return function(entry, interceptorOf, receiverOf) {
            return function(a, b, c) {
              return interceptorOf(this)[entry](receiverOf(this), a, b, c);
            };
          }(stubName, getInterceptor, getReceiver);
        case 5:
          return function(entry, interceptorOf, receiverOf) {
            return function(a, b, c, d) {
              return interceptorOf(this)[entry](receiverOf(this), a, b, c, d);
            };
          }(stubName, getInterceptor, getReceiver);
        case 6:
          return function(entry, interceptorOf, receiverOf) {
            return function(a, b, c, d, e) {
              return interceptorOf(this)[entry](receiverOf(this), a, b, c, d, e);
            };
          }(stubName, getInterceptor, getReceiver);
        default:
          return function(f, interceptorOf, receiverOf) {
            return function() {
              var a = [receiverOf(this)];
              Array.prototype.push.apply(a, arguments);
              return f.apply(interceptorOf(this), a);
            };
          }($function, getInterceptor, getReceiver);
      }
    },
    Closure_forwardInterceptedCallTo(stubName, $function, needsDirectAccess) {
      var arity, t1;
      if ($.BoundClosure__interceptorFieldNameCache == null)
        $.BoundClosure__interceptorFieldNameCache = A.BoundClosure__computeFieldNamed("interceptor");
      if ($.BoundClosure__receiverFieldNameCache == null)
        $.BoundClosure__receiverFieldNameCache = A.BoundClosure__computeFieldNamed("receiver");
      arity = $function.length;
      t1 = A.Closure_cspForwardInterceptedCall(arity, needsDirectAccess, stubName, $function);
      return t1;
    },
    closureFromTearOff(parameters) {
      return A.Closure_fromTearOff(parameters);
    },
    BoundClosure_evalRecipe(closure, recipe) {
      return A._Universe_evalInEnvironment(init.typeUniverse, A.instanceType(closure._receiver), recipe);
    },
    BoundClosure_receiverOf(closure) {
      return closure._receiver;
    },
    BoundClosure_interceptorOf(closure) {
      return closure._interceptor;
    },
    BoundClosure__computeFieldNamed(fieldName) {
      var t1, i, $name,
        template = new A.BoundClosure("receiver", "interceptor"),
        names = J.JSArray_markFixedList(Object.getOwnPropertyNames(template), type$.nullable_Object);
      for (t1 = names.length, i = 0; i < t1; ++i) {
        $name = names[i];
        if (template[$name] === fieldName)
          return $name;
      }
      throw A.wrapException(A.ArgumentError$("Field name " + fieldName + " not found.", null));
    },
    boolConversionCheck(value) {
      if (value == null)
        A.assertThrow("boolean expression must not be null");
      return value;
    },
    assertThrow(message) {
      throw A.wrapException(new A._AssertionError(message));
    },
    throwCyclicInit(staticName) {
      throw A.wrapException(new A.CyclicInitializationError(staticName));
    },
    getIsolateAffinityTag($name) {
      return init.getIsolateTag($name);
    },
    defineProperty(obj, property, value) {
      Object.defineProperty(obj, property, {value: value, enumerable: false, writable: true, configurable: true});
    },
    lookupAndCacheInterceptor(obj) {
      var interceptor, interceptorClass, altTag, mark, t1,
        tag = A._asString($.getTagFunction.call$1(obj)),
        record = $.dispatchRecordsForInstanceTags[tag];
      if (record != null) {
        Object.defineProperty(obj, init.dispatchPropertyName, {value: record, enumerable: false, writable: true, configurable: true});
        return record.i;
      }
      interceptor = $.interceptorsForUncacheableTags[tag];
      if (interceptor != null)
        return interceptor;
      interceptorClass = init.interceptorsByTag[tag];
      if (interceptorClass == null) {
        altTag = A._asStringQ($.alternateTagFunction.call$2(obj, tag));
        if (altTag != null) {
          record = $.dispatchRecordsForInstanceTags[altTag];
          if (record != null) {
            Object.defineProperty(obj, init.dispatchPropertyName, {value: record, enumerable: false, writable: true, configurable: true});
            return record.i;
          }
          interceptor = $.interceptorsForUncacheableTags[altTag];
          if (interceptor != null)
            return interceptor;
          interceptorClass = init.interceptorsByTag[altTag];
          tag = altTag;
        }
      }
      if (interceptorClass == null)
        return null;
      interceptor = interceptorClass.prototype;
      mark = tag[0];
      if (mark === "!") {
        record = A.makeLeafDispatchRecord(interceptor);
        $.dispatchRecordsForInstanceTags[tag] = record;
        Object.defineProperty(obj, init.dispatchPropertyName, {value: record, enumerable: false, writable: true, configurable: true});
        return record.i;
      }
      if (mark === "~") {
        $.interceptorsForUncacheableTags[tag] = interceptor;
        return interceptor;
      }
      if (mark === "-") {
        t1 = A.makeLeafDispatchRecord(interceptor);
        Object.defineProperty(Object.getPrototypeOf(obj), init.dispatchPropertyName, {value: t1, enumerable: false, writable: true, configurable: true});
        return t1.i;
      }
      if (mark === "+")
        return A.patchInteriorProto(obj, interceptor);
      if (mark === "*")
        throw A.wrapException(A.UnimplementedError$(tag));
      if (init.leafTags[tag] === true) {
        t1 = A.makeLeafDispatchRecord(interceptor);
        Object.defineProperty(Object.getPrototypeOf(obj), init.dispatchPropertyName, {value: t1, enumerable: false, writable: true, configurable: true});
        return t1.i;
      } else
        return A.patchInteriorProto(obj, interceptor);
    },
    patchInteriorProto(obj, interceptor) {
      var proto = Object.getPrototypeOf(obj);
      Object.defineProperty(proto, init.dispatchPropertyName, {value: J.makeDispatchRecord(interceptor, proto, null, null), enumerable: false, writable: true, configurable: true});
      return interceptor;
    },
    makeLeafDispatchRecord(interceptor) {
      return J.makeDispatchRecord(interceptor, false, null, !!interceptor.$isJavaScriptIndexingBehavior);
    },
    makeDefaultDispatchRecord(tag, interceptorClass, proto) {
      var interceptor = interceptorClass.prototype;
      if (init.leafTags[tag] === true)
        return A.makeLeafDispatchRecord(interceptor);
      else
        return J.makeDispatchRecord(interceptor, proto, null, null);
    },
    initNativeDispatch() {
      if (true === $.initNativeDispatchFlag)
        return;
      $.initNativeDispatchFlag = true;
      A.initNativeDispatchContinue();
    },
    initNativeDispatchContinue() {
      var map, tags, fun, i, tag, proto, record, interceptorClass;
      $.dispatchRecordsForInstanceTags = Object.create(null);
      $.interceptorsForUncacheableTags = Object.create(null);
      A.initHooks();
      map = init.interceptorsByTag;
      tags = Object.getOwnPropertyNames(map);
      if (typeof window != "undefined") {
        window;
        fun = function() {
        };
        for (i = 0; i < tags.length; ++i) {
          tag = tags[i];
          proto = $.prototypeForTagFunction.call$1(tag);
          if (proto != null) {
            record = A.makeDefaultDispatchRecord(tag, map[tag], proto);
            if (record != null) {
              Object.defineProperty(proto, init.dispatchPropertyName, {value: record, enumerable: false, writable: true, configurable: true});
              fun.prototype = proto;
            }
          }
        }
      }
      for (i = 0; i < tags.length; ++i) {
        tag = tags[i];
        if (/^[A-Za-z_]/.test(tag)) {
          interceptorClass = map[tag];
          map["!" + tag] = interceptorClass;
          map["~" + tag] = interceptorClass;
          map["-" + tag] = interceptorClass;
          map["+" + tag] = interceptorClass;
          map["*" + tag] = interceptorClass;
        }
      }
    },
    initHooks() {
      var transformers, i, transformer, getTag, getUnknownTag, prototypeForTag,
        hooks = B.C_JS_CONST0();
      hooks = A.applyHooksTransformer(B.C_JS_CONST1, A.applyHooksTransformer(B.C_JS_CONST2, A.applyHooksTransformer(B.C_JS_CONST3, A.applyHooksTransformer(B.C_JS_CONST3, A.applyHooksTransformer(B.C_JS_CONST4, A.applyHooksTransformer(B.C_JS_CONST5, A.applyHooksTransformer(B.C_JS_CONST6(B.C_JS_CONST), hooks)))))));
      if (typeof dartNativeDispatchHooksTransformer != "undefined") {
        transformers = dartNativeDispatchHooksTransformer;
        if (typeof transformers == "function")
          transformers = [transformers];
        if (transformers.constructor == Array)
          for (i = 0; i < transformers.length; ++i) {
            transformer = transformers[i];
            if (typeof transformer == "function")
              hooks = transformer(hooks) || hooks;
          }
      }
      getTag = hooks.getTag;
      getUnknownTag = hooks.getUnknownTag;
      prototypeForTag = hooks.prototypeForTag;
      $.getTagFunction = new A.initHooks_closure(getTag);
      $.alternateTagFunction = new A.initHooks_closure0(getUnknownTag);
      $.prototypeForTagFunction = new A.initHooks_closure1(prototypeForTag);
    },
    applyHooksTransformer(transformer, hooks) {
      return transformer(hooks) || hooks;
    },
    quoteStringForRegExp(string) {
      if (/[[\]{}()*+?.\\^$|]/.test(string))
        return string.replace(/[[\]{}()*+?.\\^$|]/g, "\\$&");
      return string;
    },
    ConstantMapView: function ConstantMapView(t0, t1) {
      this._collection$_map = t0;
      this.$ti = t1;
    },
    ConstantMap: function ConstantMap() {
    },
    ConstantStringMap: function ConstantStringMap(t0, t1, t2, t3) {
      var _ = this;
      _._length = t0;
      _._jsObject = t1;
      _.__js_helper$_keys = t2;
      _.$ti = t3;
    },
    _ConstantMapKeyIterable: function _ConstantMapKeyIterable(t0, t1) {
      this._map = t0;
      this.$ti = t1;
    },
    JSInvocationMirror: function JSInvocationMirror(t0, t1, t2, t3, t4) {
      var _ = this;
      _._memberName = t0;
      _.__js_helper$_kind = t1;
      _._arguments = t2;
      _._namedArgumentNames = t3;
      _._typeArgumentCount = t4;
    },
    Primitives_functionNoSuchMethod_closure: function Primitives_functionNoSuchMethod_closure(t0, t1, t2) {
      this._box_0 = t0;
      this.namedArgumentList = t1;
      this.$arguments = t2;
    },
    TypeErrorDecoder: function TypeErrorDecoder(t0, t1, t2, t3, t4, t5) {
      var _ = this;
      _._pattern = t0;
      _._arguments = t1;
      _._argumentsExpr = t2;
      _._expr = t3;
      _._method = t4;
      _._receiver = t5;
    },
    NullError: function NullError(t0, t1) {
      this.__js_helper$_message = t0;
      this._method = t1;
    },
    JsNoSuchMethodError: function JsNoSuchMethodError(t0, t1, t2) {
      this.__js_helper$_message = t0;
      this._method = t1;
      this._receiver = t2;
    },
    UnknownJsTypeError: function UnknownJsTypeError(t0) {
      this.__js_helper$_message = t0;
    },
    NullThrownFromJavaScriptException: function NullThrownFromJavaScriptException(t0) {
      this._irritant = t0;
    },
    ExceptionAndStackTrace: function ExceptionAndStackTrace(t0, t1) {
      this.dartException = t0;
      this.stackTrace = t1;
    },
    _StackTrace: function _StackTrace(t0) {
      this._exception = t0;
      this._trace = null;
    },
    Closure: function Closure() {
    },
    Closure0Args: function Closure0Args() {
    },
    Closure2Args: function Closure2Args() {
    },
    TearOffClosure: function TearOffClosure() {
    },
    StaticClosure: function StaticClosure() {
    },
    BoundClosure: function BoundClosure(t0, t1) {
      this._receiver = t0;
      this._interceptor = t1;
    },
    RuntimeError: function RuntimeError(t0) {
      this.message = t0;
    },
    _AssertionError: function _AssertionError(t0) {
      this.message = t0;
    },
    _Required: function _Required() {
    },
    JsLinkedHashMap: function JsLinkedHashMap(t0) {
      var _ = this;
      _._length = 0;
      _._last = _._first = _.__js_helper$_rest = _._nums = _._strings = null;
      _._modifications = 0;
      _.$ti = t0;
    },
    LinkedHashMapCell: function LinkedHashMapCell(t0, t1) {
      this.hashMapCellKey = t0;
      this.hashMapCellValue = t1;
      this._next = null;
    },
    LinkedHashMapKeyIterable: function LinkedHashMapKeyIterable(t0, t1) {
      this._map = t0;
      this.$ti = t1;
    },
    LinkedHashMapKeyIterator: function LinkedHashMapKeyIterator(t0, t1, t2) {
      var _ = this;
      _._map = t0;
      _._modifications = t1;
      _.__js_helper$_current = _._cell = null;
      _.$ti = t2;
    },
    initHooks_closure: function initHooks_closure(t0) {
      this.getTag = t0;
    },
    initHooks_closure0: function initHooks_closure0(t0) {
      this.getUnknownTag = t0;
    },
    initHooks_closure1: function initHooks_closure1(t0) {
      this.prototypeForTag = t0;
    },
    _ensureNativeList(list) {
      var t1, result, i;
      if (type$.JSIndexable_dynamic._is(list))
        return list;
      t1 = J.getInterceptor$asx(list);
      result = A.List_List$filled(t1.get$length(list), null, false, type$.dynamic);
      for (i = 0; i < t1.get$length(list); ++i)
        B.JSArray_methods.$indexSet(result, i, t1.$index(list, i));
      return result;
    },
    NativeByteData_NativeByteData($length) {
      return new DataView(new ArrayBuffer($length));
    },
    NativeUint8List_NativeUint8List($length) {
      return new Uint8Array($length);
    },
    NativeUint8List_NativeUint8List$fromList(elements) {
      return new Uint8Array(A._ensureNativeList(elements));
    },
    NativeUint8List_NativeUint8List$view(buffer, offsetInBytes, $length) {
      return $length == null ? new Uint8Array(buffer, offsetInBytes) : new Uint8Array(buffer, offsetInBytes, $length);
    },
    _checkValidIndex(index, list, $length) {
      if (index >>> 0 !== index || index >= $length)
        throw A.wrapException(A.diagnoseIndexError(list, index));
    },
    _checkValidRange(start, end, $length) {
      var t1;
      if (!(start >>> 0 !== start))
        if (end == null)
          t1 = start > $length;
        else
          t1 = end >>> 0 !== end || start > end || end > $length;
      else
        t1 = true;
      if (t1)
        throw A.wrapException(A.diagnoseRangeError(start, end, $length));
      if (end == null)
        return $length;
      return end;
    },
    NativeByteBuffer: function NativeByteBuffer() {
    },
    NativeTypedData: function NativeTypedData() {
    },
    NativeByteData: function NativeByteData() {
    },
    NativeTypedArray: function NativeTypedArray() {
    },
    NativeTypedArrayOfDouble: function NativeTypedArrayOfDouble() {
    },
    NativeTypedArrayOfInt: function NativeTypedArrayOfInt() {
    },
    NativeInt16List: function NativeInt16List() {
    },
    NativeInt32List: function NativeInt32List() {
    },
    NativeInt8List: function NativeInt8List() {
    },
    NativeUint16List: function NativeUint16List() {
    },
    NativeUint32List: function NativeUint32List() {
    },
    NativeUint8ClampedList: function NativeUint8ClampedList() {
    },
    NativeUint8List: function NativeUint8List() {
    },
    _NativeTypedArrayOfDouble_NativeTypedArray_ListMixin: function _NativeTypedArrayOfDouble_NativeTypedArray_ListMixin() {
    },
    _NativeTypedArrayOfDouble_NativeTypedArray_ListMixin_FixedLengthListMixin: function _NativeTypedArrayOfDouble_NativeTypedArray_ListMixin_FixedLengthListMixin() {
    },
    _NativeTypedArrayOfInt_NativeTypedArray_ListMixin: function _NativeTypedArrayOfInt_NativeTypedArray_ListMixin() {
    },
    _NativeTypedArrayOfInt_NativeTypedArray_ListMixin_FixedLengthListMixin: function _NativeTypedArrayOfInt_NativeTypedArray_ListMixin_FixedLengthListMixin() {
    },
    Rti__getQuestionFromStar(universe, rti) {
      var question = rti._precomputed1;
      return question == null ? rti._precomputed1 = A._Universe__lookupQuestionRti(universe, rti._primary, true) : question;
    },
    Rti__getFutureFromFutureOr(universe, rti) {
      var future = rti._precomputed1;
      return future == null ? rti._precomputed1 = A._Universe__lookupInterfaceRti(universe, "Future", [rti._primary]) : future;
    },
    Rti__isUnionOfFunctionType(rti) {
      var kind = rti._kind;
      if (kind === 6 || kind === 7 || kind === 8)
        return A.Rti__isUnionOfFunctionType(rti._primary);
      return kind === 12 || kind === 13;
    },
    Rti__getCanonicalRecipe(rti) {
      return rti._canonicalRecipe;
    },
    findType(recipe) {
      return A._Universe_eval(init.typeUniverse, recipe, false);
    },
    _substitute(universe, rti, typeArguments, depth) {
      var baseType, substitutedBaseType, interfaceTypeArguments, substitutedInterfaceTypeArguments, base, substitutedBase, $arguments, substitutedArguments, returnType, substitutedReturnType, functionParameters, substitutedFunctionParameters, bounds, substitutedBounds, index, argument,
        kind = rti._kind;
      switch (kind) {
        case 5:
        case 1:
        case 2:
        case 3:
        case 4:
          return rti;
        case 6:
          baseType = rti._primary;
          substitutedBaseType = A._substitute(universe, baseType, typeArguments, depth);
          if (substitutedBaseType === baseType)
            return rti;
          return A._Universe__lookupStarRti(universe, substitutedBaseType, true);
        case 7:
          baseType = rti._primary;
          substitutedBaseType = A._substitute(universe, baseType, typeArguments, depth);
          if (substitutedBaseType === baseType)
            return rti;
          return A._Universe__lookupQuestionRti(universe, substitutedBaseType, true);
        case 8:
          baseType = rti._primary;
          substitutedBaseType = A._substitute(universe, baseType, typeArguments, depth);
          if (substitutedBaseType === baseType)
            return rti;
          return A._Universe__lookupFutureOrRti(universe, substitutedBaseType, true);
        case 9:
          interfaceTypeArguments = rti._rest;
          substitutedInterfaceTypeArguments = A._substituteArray(universe, interfaceTypeArguments, typeArguments, depth);
          if (substitutedInterfaceTypeArguments === interfaceTypeArguments)
            return rti;
          return A._Universe__lookupInterfaceRti(universe, rti._primary, substitutedInterfaceTypeArguments);
        case 10:
          base = rti._primary;
          substitutedBase = A._substitute(universe, base, typeArguments, depth);
          $arguments = rti._rest;
          substitutedArguments = A._substituteArray(universe, $arguments, typeArguments, depth);
          if (substitutedBase === base && substitutedArguments === $arguments)
            return rti;
          return A._Universe__lookupBindingRti(universe, substitutedBase, substitutedArguments);
        case 12:
          returnType = rti._primary;
          substitutedReturnType = A._substitute(universe, returnType, typeArguments, depth);
          functionParameters = rti._rest;
          substitutedFunctionParameters = A._substituteFunctionParameters(universe, functionParameters, typeArguments, depth);
          if (substitutedReturnType === returnType && substitutedFunctionParameters === functionParameters)
            return rti;
          return A._Universe__lookupFunctionRti(universe, substitutedReturnType, substitutedFunctionParameters);
        case 13:
          bounds = rti._rest;
          depth += bounds.length;
          substitutedBounds = A._substituteArray(universe, bounds, typeArguments, depth);
          base = rti._primary;
          substitutedBase = A._substitute(universe, base, typeArguments, depth);
          if (substitutedBounds === bounds && substitutedBase === base)
            return rti;
          return A._Universe__lookupGenericFunctionRti(universe, substitutedBase, substitutedBounds, true);
        case 14:
          index = rti._primary;
          if (index < depth)
            return rti;
          argument = typeArguments[index - depth];
          if (argument == null)
            return rti;
          return argument;
        default:
          throw A.wrapException(A.AssertionError$("Attempted to substitute unexpected RTI kind " + kind));
      }
    },
    _substituteArray(universe, rtiArray, typeArguments, depth) {
      var changed, i, rti, substitutedRti,
        $length = rtiArray.length,
        result = A._Utils_newArrayOrEmpty($length);
      for (changed = false, i = 0; i < $length; ++i) {
        rti = rtiArray[i];
        substitutedRti = A._substitute(universe, rti, typeArguments, depth);
        if (substitutedRti !== rti)
          changed = true;
        result[i] = substitutedRti;
      }
      return changed ? result : rtiArray;
    },
    _substituteNamed(universe, namedArray, typeArguments, depth) {
      var changed, i, t1, t2, rti, substitutedRti,
        $length = namedArray.length,
        result = A._Utils_newArrayOrEmpty($length);
      for (changed = false, i = 0; i < $length; i += 3) {
        t1 = namedArray[i];
        t2 = namedArray[i + 1];
        rti = namedArray[i + 2];
        substitutedRti = A._substitute(universe, rti, typeArguments, depth);
        if (substitutedRti !== rti)
          changed = true;
        result.splice(i, 3, t1, t2, substitutedRti);
      }
      return changed ? result : namedArray;
    },
    _substituteFunctionParameters(universe, functionParameters, typeArguments, depth) {
      var result,
        requiredPositional = functionParameters._requiredPositional,
        substitutedRequiredPositional = A._substituteArray(universe, requiredPositional, typeArguments, depth),
        optionalPositional = functionParameters._optionalPositional,
        substitutedOptionalPositional = A._substituteArray(universe, optionalPositional, typeArguments, depth),
        named = functionParameters._named,
        substitutedNamed = A._substituteNamed(universe, named, typeArguments, depth);
      if (substitutedRequiredPositional === requiredPositional && substitutedOptionalPositional === optionalPositional && substitutedNamed === named)
        return functionParameters;
      result = new A._FunctionParameters();
      result._requiredPositional = substitutedRequiredPositional;
      result._optionalPositional = substitutedOptionalPositional;
      result._named = substitutedNamed;
      return result;
    },
    _setArrayType(target, rti) {
      target[init.arrayRti] = rti;
      return target;
    },
    closureFunctionType(closure) {
      var t1,
        signature = closure.$signature;
      if (signature != null) {
        if (typeof signature == "number")
          return A.getTypeFromTypesTable(signature);
        t1 = closure.$signature();
        return t1;
      }
      return null;
    },
    instanceOrFunctionType(object, testRti) {
      var rti;
      if (A.Rti__isUnionOfFunctionType(testRti))
        if (object instanceof A.Closure) {
          rti = A.closureFunctionType(object);
          if (rti != null)
            return rti;
        }
      return A.instanceType(object);
    },
    instanceType(object) {
      var rti;
      if (object instanceof A.Object) {
        rti = object.$ti;
        return rti != null ? rti : A._instanceTypeFromConstructor(object);
      }
      if (Array.isArray(object))
        return A._arrayInstanceType(object);
      return A._instanceTypeFromConstructor(J.getInterceptor$(object));
    },
    _arrayInstanceType(object) {
      var rti = object[init.arrayRti],
        defaultRti = type$.JSArray_dynamic;
      if (rti == null)
        return defaultRti;
      if (rti.constructor !== defaultRti.constructor)
        return defaultRti;
      return rti;
    },
    _instanceType(object) {
      var rti = object.$ti;
      return rti != null ? rti : A._instanceTypeFromConstructor(object);
    },
    _instanceTypeFromConstructor(instance) {
      var $constructor = instance.constructor,
        probe = $constructor.$ccache;
      if (probe != null)
        return probe;
      return A._instanceTypeFromConstructorMiss(instance, $constructor);
    },
    _instanceTypeFromConstructorMiss(instance, $constructor) {
      var effectiveConstructor = instance instanceof A.Closure ? instance.__proto__.__proto__.constructor : $constructor,
        rti = A._Universe_findErasedType(init.typeUniverse, effectiveConstructor.name);
      $constructor.$ccache = rti;
      return rti;
    },
    getTypeFromTypesTable(index) {
      var rti,
        table = init.types,
        type = table[index];
      if (typeof type == "string") {
        rti = A._Universe_eval(init.typeUniverse, type, false);
        table[index] = rti;
        return rti;
      }
      return type;
    },
    createRuntimeType(rti) {
      var s, starErasedRecipe, starErasedRti,
        type = rti._cachedRuntimeType;
      if (type != null)
        return type;
      s = rti._canonicalRecipe;
      starErasedRecipe = s.replace(/\*/g, "");
      if (starErasedRecipe === s)
        return rti._cachedRuntimeType = new A._Type(rti);
      starErasedRti = A._Universe_eval(init.typeUniverse, starErasedRecipe, true);
      type = starErasedRti._cachedRuntimeType;
      return rti._cachedRuntimeType = type == null ? starErasedRti._cachedRuntimeType = new A._Type(starErasedRti) : type;
    },
    typeLiteral(recipe) {
      return A.createRuntimeType(A._Universe_eval(init.typeUniverse, recipe, false));
    },
    _installSpecializedIsTest(object) {
      var t1, unstarred, isFn, $name, testRti = this;
      if (testRti === type$.Object)
        return A._finishIsFn(testRti, object, A._isObject);
      if (!A.isStrongTopType(testRti))
        if (!(testRti === type$.legacy_Object))
          t1 = false;
        else
          t1 = true;
      else
        t1 = true;
      if (t1)
        return A._finishIsFn(testRti, object, A._isTop);
      t1 = testRti._kind;
      unstarred = t1 === 6 ? testRti._primary : testRti;
      if (unstarred === type$.int)
        isFn = A._isInt;
      else if (unstarred === type$.double || unstarred === type$.num)
        isFn = A._isNum;
      else if (unstarred === type$.String)
        isFn = A._isString;
      else
        isFn = unstarred === type$.bool ? A._isBool : null;
      if (isFn != null)
        return A._finishIsFn(testRti, object, isFn);
      if (unstarred._kind === 9) {
        $name = unstarred._primary;
        if (unstarred._rest.every(A.isTopType)) {
          testRti._specializedTestResource = "$is" + $name;
          if ($name === "List")
            return A._finishIsFn(testRti, object, A._isListTestViaProperty);
          return A._finishIsFn(testRti, object, A._isTestViaProperty);
        }
      } else if (t1 === 7)
        return A._finishIsFn(testRti, object, A._generalNullableIsTestImplementation);
      return A._finishIsFn(testRti, object, A._generalIsTestImplementation);
    },
    _finishIsFn(testRti, object, isFn) {
      testRti._is = isFn;
      return testRti._is(object);
    },
    _installSpecializedAsCheck(object) {
      var t1, testRti = this,
        asFn = A._generalAsCheckImplementation;
      if (!A.isStrongTopType(testRti))
        if (!(testRti === type$.legacy_Object))
          t1 = false;
        else
          t1 = true;
      else
        t1 = true;
      if (t1)
        asFn = A._asTop;
      else if (testRti === type$.Object)
        asFn = A._asObject;
      else {
        t1 = A.isNullable(testRti);
        if (t1)
          asFn = A._generalNullableAsCheckImplementation;
      }
      testRti._as = asFn;
      return testRti._as(object);
    },
    _nullIs(testRti) {
      var t1,
        kind = testRti._kind;
      if (!A.isStrongTopType(testRti))
        if (!(testRti === type$.legacy_Object))
          if (!(testRti === type$.legacy_Never))
            if (kind !== 7)
              if (!(kind === 6 && A._nullIs(testRti._primary)))
                t1 = kind === 8 && A._nullIs(testRti._primary) || testRti === type$.Null || testRti === type$.JSNull;
              else
                t1 = true;
            else
              t1 = true;
          else
            t1 = true;
        else
          t1 = true;
      else
        t1 = true;
      return t1;
    },
    _generalIsTestImplementation(object) {
      var testRti = this;
      if (object == null)
        return A._nullIs(testRti);
      return A._isSubtype(init.typeUniverse, A.instanceOrFunctionType(object, testRti), null, testRti, null);
    },
    _generalNullableIsTestImplementation(object) {
      if (object == null)
        return true;
      return this._primary._is(object);
    },
    _isTestViaProperty(object) {
      var tag, testRti = this;
      if (object == null)
        return A._nullIs(testRti);
      tag = testRti._specializedTestResource;
      if (object instanceof A.Object)
        return !!object[tag];
      return !!J.getInterceptor$(object)[tag];
    },
    _isListTestViaProperty(object) {
      var tag, testRti = this;
      if (object == null)
        return A._nullIs(testRti);
      if (typeof object != "object")
        return false;
      if (Array.isArray(object))
        return true;
      tag = testRti._specializedTestResource;
      if (object instanceof A.Object)
        return !!object[tag];
      return !!J.getInterceptor$(object)[tag];
    },
    _generalAsCheckImplementation(object) {
      var t1, testRti = this;
      if (object == null) {
        t1 = A.isNullable(testRti);
        if (t1)
          return object;
      } else if (testRti._is(object))
        return object;
      A._failedAsCheck(object, testRti);
    },
    _generalNullableAsCheckImplementation(object) {
      var testRti = this;
      if (object == null)
        return object;
      else if (testRti._is(object))
        return object;
      A._failedAsCheck(object, testRti);
    },
    _failedAsCheck(object, testRti) {
      throw A.wrapException(A._TypeError$fromMessage(A._Error_compose(object, A.instanceOrFunctionType(object, testRti), A._rtiToString(testRti, null))));
    },
    _Error_compose(object, objectRti, checkedTypeDescription) {
      var objectDescription = A.Error_safeToString(object);
      return objectDescription + ": type '" + A._rtiToString(objectRti == null ? A.instanceType(object) : objectRti, null) + "' is not a subtype of type '" + checkedTypeDescription + "'";
    },
    _TypeError$fromMessage(message) {
      return new A._TypeError("TypeError: " + message);
    },
    _TypeError__TypeError$forType(object, type) {
      return new A._TypeError("TypeError: " + A._Error_compose(object, null, type));
    },
    _isObject(object) {
      return object != null;
    },
    _asObject(object) {
      if (object != null)
        return object;
      throw A.wrapException(A._TypeError__TypeError$forType(object, "Object"));
    },
    _isTop(object) {
      return true;
    },
    _asTop(object) {
      return object;
    },
    _isBool(object) {
      return true === object || false === object;
    },
    _asBool(object) {
      if (true === object)
        return true;
      if (false === object)
        return false;
      throw A.wrapException(A._TypeError__TypeError$forType(object, "bool"));
    },
    _asBoolS(object) {
      if (true === object)
        return true;
      if (false === object)
        return false;
      if (object == null)
        return object;
      throw A.wrapException(A._TypeError__TypeError$forType(object, "bool"));
    },
    _asBoolQ(object) {
      if (true === object)
        return true;
      if (false === object)
        return false;
      if (object == null)
        return object;
      throw A.wrapException(A._TypeError__TypeError$forType(object, "bool?"));
    },
    _asDouble(object) {
      if (typeof object == "number")
        return object;
      throw A.wrapException(A._TypeError__TypeError$forType(object, "double"));
    },
    _asDoubleS(object) {
      if (typeof object == "number")
        return object;
      if (object == null)
        return object;
      throw A.wrapException(A._TypeError__TypeError$forType(object, "double"));
    },
    _asDoubleQ(object) {
      if (typeof object == "number")
        return object;
      if (object == null)
        return object;
      throw A.wrapException(A._TypeError__TypeError$forType(object, "double?"));
    },
    _isInt(object) {
      return typeof object == "number" && Math.floor(object) === object;
    },
    _asInt(object) {
      if (typeof object == "number" && Math.floor(object) === object)
        return object;
      throw A.wrapException(A._TypeError__TypeError$forType(object, "int"));
    },
    _asIntS(object) {
      if (typeof object == "number" && Math.floor(object) === object)
        return object;
      if (object == null)
        return object;
      throw A.wrapException(A._TypeError__TypeError$forType(object, "int"));
    },
    _asIntQ(object) {
      if (typeof object == "number" && Math.floor(object) === object)
        return object;
      if (object == null)
        return object;
      throw A.wrapException(A._TypeError__TypeError$forType(object, "int?"));
    },
    _isNum(object) {
      return typeof object == "number";
    },
    _asNum(object) {
      if (typeof object == "number")
        return object;
      throw A.wrapException(A._TypeError__TypeError$forType(object, "num"));
    },
    _asNumS(object) {
      if (typeof object == "number")
        return object;
      if (object == null)
        return object;
      throw A.wrapException(A._TypeError__TypeError$forType(object, "num"));
    },
    _asNumQ(object) {
      if (typeof object == "number")
        return object;
      if (object == null)
        return object;
      throw A.wrapException(A._TypeError__TypeError$forType(object, "num?"));
    },
    _isString(object) {
      return typeof object == "string";
    },
    _asString(object) {
      if (typeof object == "string")
        return object;
      throw A.wrapException(A._TypeError__TypeError$forType(object, "String"));
    },
    _asStringS(object) {
      if (typeof object == "string")
        return object;
      if (object == null)
        return object;
      throw A.wrapException(A._TypeError__TypeError$forType(object, "String"));
    },
    _asStringQ(object) {
      if (typeof object == "string")
        return object;
      if (object == null)
        return object;
      throw A.wrapException(A._TypeError__TypeError$forType(object, "String?"));
    },
    _rtiArrayToString(array, genericContext) {
      var s, sep, i;
      for (s = "", sep = "", i = 0; i < array.length; ++i, sep = ", ")
        s += sep + A._rtiToString(array[i], genericContext);
      return s;
    },
    _recordRtiToString(recordType, genericContext) {
      var fieldCount, names, namesIndex, s, comma, i,
        partialShape = recordType._primary,
        fields = recordType._rest;
      if ("" === partialShape)
        return "(" + A._rtiArrayToString(fields, genericContext) + ")";
      fieldCount = fields.length;
      names = partialShape.split(",");
      namesIndex = names.length - fieldCount;
      for (s = "(", comma = "", i = 0; i < fieldCount; ++i, comma = ", ") {
        s += comma;
        if (namesIndex === 0)
          s += "{";
        s += A._rtiToString(fields[i], genericContext);
        if (namesIndex >= 0)
          s += " " + names[namesIndex];
        ++namesIndex;
      }
      return s + "})";
    },
    _functionRtiToString(functionType, genericContext, bounds) {
      var boundsLength, outerContextLength, offset, i, t1, t2, typeParametersText, typeSep, t3, t4, boundRti, kind, parameters, requiredPositional, requiredPositionalLength, optionalPositional, optionalPositionalLength, named, namedLength, returnTypeText, argumentsText, sep, _s2_ = ", ";
      if (bounds != null) {
        boundsLength = bounds.length;
        if (genericContext == null) {
          genericContext = A._setArrayType([], type$.JSArray_String);
          outerContextLength = null;
        } else
          outerContextLength = genericContext.length;
        offset = genericContext.length;
        for (i = boundsLength; i > 0; --i)
          B.JSArray_methods.add$1(genericContext, "T" + (offset + i));
        for (t1 = type$.nullable_Object, t2 = type$.legacy_Object, typeParametersText = "<", typeSep = "", i = 0; i < boundsLength; ++i, typeSep = _s2_) {
          t3 = genericContext.length;
          t4 = t3 - 1 - i;
          if (!(t4 >= 0))
            return A.ioore(genericContext, t4);
          typeParametersText = B.JSString_methods.$add(typeParametersText + typeSep, genericContext[t4]);
          boundRti = bounds[i];
          kind = boundRti._kind;
          if (!(kind === 2 || kind === 3 || kind === 4 || kind === 5 || boundRti === t1))
            if (!(boundRti === t2))
              t3 = false;
            else
              t3 = true;
          else
            t3 = true;
          if (!t3)
            typeParametersText += " extends " + A._rtiToString(boundRti, genericContext);
        }
        typeParametersText += ">";
      } else {
        typeParametersText = "";
        outerContextLength = null;
      }
      t1 = functionType._primary;
      parameters = functionType._rest;
      requiredPositional = parameters._requiredPositional;
      requiredPositionalLength = requiredPositional.length;
      optionalPositional = parameters._optionalPositional;
      optionalPositionalLength = optionalPositional.length;
      named = parameters._named;
      namedLength = named.length;
      returnTypeText = A._rtiToString(t1, genericContext);
      for (argumentsText = "", sep = "", i = 0; i < requiredPositionalLength; ++i, sep = _s2_)
        argumentsText += sep + A._rtiToString(requiredPositional[i], genericContext);
      if (optionalPositionalLength > 0) {
        argumentsText += sep + "[";
        for (sep = "", i = 0; i < optionalPositionalLength; ++i, sep = _s2_)
          argumentsText += sep + A._rtiToString(optionalPositional[i], genericContext);
        argumentsText += "]";
      }
      if (namedLength > 0) {
        argumentsText += sep + "{";
        for (sep = "", i = 0; i < namedLength; i += 3, sep = _s2_) {
          argumentsText += sep;
          if (named[i + 1])
            argumentsText += "required ";
          argumentsText += A._rtiToString(named[i + 2], genericContext) + " " + named[i];
        }
        argumentsText += "}";
      }
      if (outerContextLength != null) {
        genericContext.toString;
        genericContext.length = outerContextLength;
      }
      return typeParametersText + "(" + argumentsText + ") => " + returnTypeText;
    },
    _rtiToString(rti, genericContext) {
      var s, questionArgument, argumentKind, $name, $arguments, t1, t2,
        kind = rti._kind;
      if (kind === 5)
        return "erased";
      if (kind === 2)
        return "dynamic";
      if (kind === 3)
        return "void";
      if (kind === 1)
        return "Never";
      if (kind === 4)
        return "any";
      if (kind === 6) {
        s = A._rtiToString(rti._primary, genericContext);
        return s;
      }
      if (kind === 7) {
        questionArgument = rti._primary;
        s = A._rtiToString(questionArgument, genericContext);
        argumentKind = questionArgument._kind;
        return (argumentKind === 12 || argumentKind === 13 ? "(" + s + ")" : s) + "?";
      }
      if (kind === 8)
        return "FutureOr<" + A._rtiToString(rti._primary, genericContext) + ">";
      if (kind === 9) {
        $name = A._unminifyOrTag(rti._primary);
        $arguments = rti._rest;
        return $arguments.length > 0 ? $name + ("<" + A._rtiArrayToString($arguments, genericContext) + ">") : $name;
      }
      if (kind === 11)
        return A._recordRtiToString(rti, genericContext);
      if (kind === 12)
        return A._functionRtiToString(rti, genericContext, null);
      if (kind === 13)
        return A._functionRtiToString(rti._primary, genericContext, rti._rest);
      if (kind === 14) {
        t1 = rti._primary;
        t2 = genericContext.length;
        t1 = t2 - 1 - t1;
        if (!(t1 >= 0 && t1 < t2))
          return A.ioore(genericContext, t1);
        return genericContext[t1];
      }
      return "?";
    },
    _unminifyOrTag(rawClassName) {
      var preserved = init.mangledGlobalNames[rawClassName];
      if (preserved != null)
        return preserved;
      return rawClassName;
    },
    _Universe_findRule(universe, targetType) {
      var rule = universe.tR[targetType];
      for (; typeof rule == "string";)
        rule = universe.tR[rule];
      return rule;
    },
    _Universe_findErasedType(universe, cls) {
      var $length, erased, $arguments, i, $interface,
        t1 = universe.eT,
        probe = t1[cls];
      if (probe == null)
        return A._Universe_eval(universe, cls, false);
      else if (typeof probe == "number") {
        $length = probe;
        erased = A._Universe__lookupTerminalRti(universe, 5, "#");
        $arguments = A._Utils_newArrayOrEmpty($length);
        for (i = 0; i < $length; ++i)
          $arguments[i] = erased;
        $interface = A._Universe__lookupInterfaceRti(universe, cls, $arguments);
        t1[cls] = $interface;
        return $interface;
      } else
        return probe;
    },
    _Universe_addRules(universe, rules) {
      return A._Utils_objectAssign(universe.tR, rules);
    },
    _Universe_addErasedTypes(universe, types) {
      return A._Utils_objectAssign(universe.eT, types);
    },
    _Universe_eval(universe, recipe, normalize) {
      var rti,
        t1 = universe.eC,
        probe = t1.get(recipe);
      if (probe != null)
        return probe;
      rti = A._Parser_parse(A._Parser_create(universe, null, recipe, normalize));
      t1.set(recipe, rti);
      return rti;
    },
    _Universe_evalInEnvironment(universe, environment, recipe) {
      var probe, rti,
        cache = environment._evalCache;
      if (cache == null)
        cache = environment._evalCache = new Map();
      probe = cache.get(recipe);
      if (probe != null)
        return probe;
      rti = A._Parser_parse(A._Parser_create(universe, environment, recipe, true));
      cache.set(recipe, rti);
      return rti;
    },
    _Universe_bind(universe, environment, argumentsRti) {
      var argumentsRecipe, probe, rti,
        cache = environment._bindCache;
      if (cache == null)
        cache = environment._bindCache = new Map();
      argumentsRecipe = argumentsRti._canonicalRecipe;
      probe = cache.get(argumentsRecipe);
      if (probe != null)
        return probe;
      rti = A._Universe__lookupBindingRti(universe, environment, argumentsRti._kind === 10 ? argumentsRti._rest : [argumentsRti]);
      cache.set(argumentsRecipe, rti);
      return rti;
    },
    _Universe__installTypeTests(universe, rti) {
      rti._as = A._installSpecializedAsCheck;
      rti._is = A._installSpecializedIsTest;
      return rti;
    },
    _Universe__lookupTerminalRti(universe, kind, key) {
      var rti, t1,
        probe = universe.eC.get(key);
      if (probe != null)
        return probe;
      rti = new A.Rti(null, null);
      rti._kind = kind;
      rti._canonicalRecipe = key;
      t1 = A._Universe__installTypeTests(universe, rti);
      universe.eC.set(key, t1);
      return t1;
    },
    _Universe__lookupStarRti(universe, baseType, normalize) {
      var t1,
        key = baseType._canonicalRecipe + "*",
        probe = universe.eC.get(key);
      if (probe != null)
        return probe;
      t1 = A._Universe__createStarRti(universe, baseType, key, normalize);
      universe.eC.set(key, t1);
      return t1;
    },
    _Universe__createStarRti(universe, baseType, key, normalize) {
      var baseKind, t1, rti;
      if (normalize) {
        baseKind = baseType._kind;
        if (!A.isStrongTopType(baseType))
          t1 = baseType === type$.Null || baseType === type$.JSNull || baseKind === 7 || baseKind === 6;
        else
          t1 = true;
        if (t1)
          return baseType;
      }
      rti = new A.Rti(null, null);
      rti._kind = 6;
      rti._primary = baseType;
      rti._canonicalRecipe = key;
      return A._Universe__installTypeTests(universe, rti);
    },
    _Universe__lookupQuestionRti(universe, baseType, normalize) {
      var t1,
        key = baseType._canonicalRecipe + "?",
        probe = universe.eC.get(key);
      if (probe != null)
        return probe;
      t1 = A._Universe__createQuestionRti(universe, baseType, key, normalize);
      universe.eC.set(key, t1);
      return t1;
    },
    _Universe__createQuestionRti(universe, baseType, key, normalize) {
      var baseKind, t1, starArgument, rti;
      if (normalize) {
        baseKind = baseType._kind;
        if (!A.isStrongTopType(baseType))
          if (!(baseType === type$.Null || baseType === type$.JSNull))
            if (baseKind !== 7)
              t1 = baseKind === 8 && A.isNullable(baseType._primary);
            else
              t1 = true;
          else
            t1 = true;
        else
          t1 = true;
        if (t1)
          return baseType;
        else if (baseKind === 1 || baseType === type$.legacy_Never)
          return type$.Null;
        else if (baseKind === 6) {
          starArgument = baseType._primary;
          if (starArgument._kind === 8 && A.isNullable(starArgument._primary))
            return starArgument;
          else
            return A.Rti__getQuestionFromStar(universe, baseType);
        }
      }
      rti = new A.Rti(null, null);
      rti._kind = 7;
      rti._primary = baseType;
      rti._canonicalRecipe = key;
      return A._Universe__installTypeTests(universe, rti);
    },
    _Universe__lookupFutureOrRti(universe, baseType, normalize) {
      var t1,
        key = baseType._canonicalRecipe + "/",
        probe = universe.eC.get(key);
      if (probe != null)
        return probe;
      t1 = A._Universe__createFutureOrRti(universe, baseType, key, normalize);
      universe.eC.set(key, t1);
      return t1;
    },
    _Universe__createFutureOrRti(universe, baseType, key, normalize) {
      var t1, t2, rti;
      if (normalize) {
        t1 = baseType._kind;
        if (!A.isStrongTopType(baseType))
          if (!(baseType === type$.legacy_Object))
            t2 = false;
          else
            t2 = true;
        else
          t2 = true;
        if (t2 || baseType === type$.Object)
          return baseType;
        else if (t1 === 1)
          return A._Universe__lookupInterfaceRti(universe, "Future", [baseType]);
        else if (baseType === type$.Null || baseType === type$.JSNull)
          return type$.nullable_Future_Null;
      }
      rti = new A.Rti(null, null);
      rti._kind = 8;
      rti._primary = baseType;
      rti._canonicalRecipe = key;
      return A._Universe__installTypeTests(universe, rti);
    },
    _Universe__lookupGenericFunctionParameterRti(universe, index) {
      var rti, t1,
        key = "" + index + "^",
        probe = universe.eC.get(key);
      if (probe != null)
        return probe;
      rti = new A.Rti(null, null);
      rti._kind = 14;
      rti._primary = index;
      rti._canonicalRecipe = key;
      t1 = A._Universe__installTypeTests(universe, rti);
      universe.eC.set(key, t1);
      return t1;
    },
    _Universe__canonicalRecipeJoin($arguments) {
      var s, sep, i,
        $length = $arguments.length;
      for (s = "", sep = "", i = 0; i < $length; ++i, sep = ",")
        s += sep + $arguments[i]._canonicalRecipe;
      return s;
    },
    _Universe__canonicalRecipeJoinNamed($arguments) {
      var s, sep, i, t1, nameSep,
        $length = $arguments.length;
      for (s = "", sep = "", i = 0; i < $length; i += 3, sep = ",") {
        t1 = $arguments[i];
        nameSep = $arguments[i + 1] ? "!" : ":";
        s += sep + t1 + nameSep + $arguments[i + 2]._canonicalRecipe;
      }
      return s;
    },
    _Universe__lookupInterfaceRti(universe, $name, $arguments) {
      var probe, rti, t1,
        s = $name;
      if ($arguments.length > 0)
        s += "<" + A._Universe__canonicalRecipeJoin($arguments) + ">";
      probe = universe.eC.get(s);
      if (probe != null)
        return probe;
      rti = new A.Rti(null, null);
      rti._kind = 9;
      rti._primary = $name;
      rti._rest = $arguments;
      if ($arguments.length > 0)
        rti._precomputed1 = $arguments[0];
      rti._canonicalRecipe = s;
      t1 = A._Universe__installTypeTests(universe, rti);
      universe.eC.set(s, t1);
      return t1;
    },
    _Universe__lookupBindingRti(universe, base, $arguments) {
      var newBase, newArguments, key, probe, rti, t1;
      if (base._kind === 10) {
        newBase = base._primary;
        newArguments = base._rest.concat($arguments);
      } else {
        newArguments = $arguments;
        newBase = base;
      }
      key = newBase._canonicalRecipe + (";<" + A._Universe__canonicalRecipeJoin(newArguments) + ">");
      probe = universe.eC.get(key);
      if (probe != null)
        return probe;
      rti = new A.Rti(null, null);
      rti._kind = 10;
      rti._primary = newBase;
      rti._rest = newArguments;
      rti._canonicalRecipe = key;
      t1 = A._Universe__installTypeTests(universe, rti);
      universe.eC.set(key, t1);
      return t1;
    },
    _Universe__lookupRecordRti(universe, partialShapeTag, fields) {
      var rti, t1,
        key = "+" + (partialShapeTag + "(" + A._Universe__canonicalRecipeJoin(fields) + ")"),
        probe = universe.eC.get(key);
      if (probe != null)
        return probe;
      rti = new A.Rti(null, null);
      rti._kind = 11;
      rti._primary = partialShapeTag;
      rti._rest = fields;
      rti._canonicalRecipe = key;
      t1 = A._Universe__installTypeTests(universe, rti);
      universe.eC.set(key, t1);
      return t1;
    },
    _Universe__lookupFunctionRti(universe, returnType, parameters) {
      var sep, key, probe, rti, t1,
        s = returnType._canonicalRecipe,
        requiredPositional = parameters._requiredPositional,
        requiredPositionalLength = requiredPositional.length,
        optionalPositional = parameters._optionalPositional,
        optionalPositionalLength = optionalPositional.length,
        named = parameters._named,
        namedLength = named.length,
        recipe = "(" + A._Universe__canonicalRecipeJoin(requiredPositional);
      if (optionalPositionalLength > 0) {
        sep = requiredPositionalLength > 0 ? "," : "";
        recipe += sep + "[" + A._Universe__canonicalRecipeJoin(optionalPositional) + "]";
      }
      if (namedLength > 0) {
        sep = requiredPositionalLength > 0 ? "," : "";
        recipe += sep + "{" + A._Universe__canonicalRecipeJoinNamed(named) + "}";
      }
      key = s + (recipe + ")");
      probe = universe.eC.get(key);
      if (probe != null)
        return probe;
      rti = new A.Rti(null, null);
      rti._kind = 12;
      rti._primary = returnType;
      rti._rest = parameters;
      rti._canonicalRecipe = key;
      t1 = A._Universe__installTypeTests(universe, rti);
      universe.eC.set(key, t1);
      return t1;
    },
    _Universe__lookupGenericFunctionRti(universe, baseFunctionType, bounds, normalize) {
      var t1,
        key = baseFunctionType._canonicalRecipe + ("<" + A._Universe__canonicalRecipeJoin(bounds) + ">"),
        probe = universe.eC.get(key);
      if (probe != null)
        return probe;
      t1 = A._Universe__createGenericFunctionRti(universe, baseFunctionType, bounds, key, normalize);
      universe.eC.set(key, t1);
      return t1;
    },
    _Universe__createGenericFunctionRti(universe, baseFunctionType, bounds, key, normalize) {
      var $length, typeArguments, count, i, bound, substitutedBase, substitutedBounds, rti;
      if (normalize) {
        $length = bounds.length;
        typeArguments = A._Utils_newArrayOrEmpty($length);
        for (count = 0, i = 0; i < $length; ++i) {
          bound = bounds[i];
          if (bound._kind === 1) {
            typeArguments[i] = bound;
            ++count;
          }
        }
        if (count > 0) {
          substitutedBase = A._substitute(universe, baseFunctionType, typeArguments, 0);
          substitutedBounds = A._substituteArray(universe, bounds, typeArguments, 0);
          return A._Universe__lookupGenericFunctionRti(universe, substitutedBase, substitutedBounds, bounds !== substitutedBounds);
        }
      }
      rti = new A.Rti(null, null);
      rti._kind = 13;
      rti._primary = baseFunctionType;
      rti._rest = bounds;
      rti._canonicalRecipe = key;
      return A._Universe__installTypeTests(universe, rti);
    },
    _Parser_create(universe, environment, recipe, normalize) {
      return {u: universe, e: environment, r: recipe, s: [], p: 0, n: normalize};
    },
    _Parser_parse(parser) {
      var t2, i, ch, t3, array, head, base, end, item,
        source = parser.r,
        t1 = parser.s;
      for (t2 = source.length, i = 0; i < t2;) {
        ch = source.charCodeAt(i);
        if (ch >= 48 && ch <= 57)
          i = A._Parser_handleDigit(i + 1, ch, source, t1);
        else if ((((ch | 32) >>> 0) - 97 & 65535) < 26 || ch === 95 || ch === 36 || ch === 124)
          i = A._Parser_handleIdentifier(parser, i, source, t1, false);
        else if (ch === 46)
          i = A._Parser_handleIdentifier(parser, i, source, t1, true);
        else {
          ++i;
          switch (ch) {
            case 44:
              break;
            case 58:
              t1.push(false);
              break;
            case 33:
              t1.push(true);
              break;
            case 59:
              t1.push(A._Parser_toType(parser.u, parser.e, t1.pop()));
              break;
            case 94:
              t1.push(A._Universe__lookupGenericFunctionParameterRti(parser.u, t1.pop()));
              break;
            case 35:
              t1.push(A._Universe__lookupTerminalRti(parser.u, 5, "#"));
              break;
            case 64:
              t1.push(A._Universe__lookupTerminalRti(parser.u, 2, "@"));
              break;
            case 126:
              t1.push(A._Universe__lookupTerminalRti(parser.u, 3, "~"));
              break;
            case 60:
              t1.push(parser.p);
              parser.p = t1.length;
              break;
            case 62:
              t3 = parser.u;
              array = t1.splice(parser.p);
              A._Parser_toTypes(parser.u, parser.e, array);
              parser.p = t1.pop();
              head = t1.pop();
              if (typeof head == "string")
                t1.push(A._Universe__lookupInterfaceRti(t3, head, array));
              else {
                base = A._Parser_toType(t3, parser.e, head);
                switch (base._kind) {
                  case 12:
                    t1.push(A._Universe__lookupGenericFunctionRti(t3, base, array, parser.n));
                    break;
                  default:
                    t1.push(A._Universe__lookupBindingRti(t3, base, array));
                    break;
                }
              }
              break;
            case 38:
              A._Parser_handleExtendedOperations(parser, t1);
              break;
            case 42:
              t3 = parser.u;
              t1.push(A._Universe__lookupStarRti(t3, A._Parser_toType(t3, parser.e, t1.pop()), parser.n));
              break;
            case 63:
              t3 = parser.u;
              t1.push(A._Universe__lookupQuestionRti(t3, A._Parser_toType(t3, parser.e, t1.pop()), parser.n));
              break;
            case 47:
              t3 = parser.u;
              t1.push(A._Universe__lookupFutureOrRti(t3, A._Parser_toType(t3, parser.e, t1.pop()), parser.n));
              break;
            case 40:
              t1.push(-3);
              t1.push(parser.p);
              parser.p = t1.length;
              break;
            case 41:
              A._Parser_handleArguments(parser, t1);
              break;
            case 91:
              t1.push(parser.p);
              parser.p = t1.length;
              break;
            case 93:
              array = t1.splice(parser.p);
              A._Parser_toTypes(parser.u, parser.e, array);
              parser.p = t1.pop();
              t1.push(array);
              t1.push(-1);
              break;
            case 123:
              t1.push(parser.p);
              parser.p = t1.length;
              break;
            case 125:
              array = t1.splice(parser.p);
              A._Parser_toTypesNamed(parser.u, parser.e, array);
              parser.p = t1.pop();
              t1.push(array);
              t1.push(-2);
              break;
            case 43:
              end = source.indexOf("(", i);
              t1.push(source.substring(i, end));
              t1.push(-4);
              t1.push(parser.p);
              parser.p = t1.length;
              i = end + 1;
              break;
            default:
              throw "Bad character " + ch;
          }
        }
      }
      item = t1.pop();
      return A._Parser_toType(parser.u, parser.e, item);
    },
    _Parser_handleDigit(i, digit, source, stack) {
      var t1, ch,
        value = digit - 48;
      for (t1 = source.length; i < t1; ++i) {
        ch = source.charCodeAt(i);
        if (!(ch >= 48 && ch <= 57))
          break;
        value = value * 10 + (ch - 48);
      }
      stack.push(value);
      return i;
    },
    _Parser_handleIdentifier(parser, start, source, stack, hasPeriod) {
      var t1, ch, t2, string, environment, recipe,
        i = start + 1;
      for (t1 = source.length; i < t1; ++i) {
        ch = source.charCodeAt(i);
        if (ch === 46) {
          if (hasPeriod)
            break;
          hasPeriod = true;
        } else {
          if (!((((ch | 32) >>> 0) - 97 & 65535) < 26 || ch === 95 || ch === 36 || ch === 124))
            t2 = ch >= 48 && ch <= 57;
          else
            t2 = true;
          if (!t2)
            break;
        }
      }
      string = source.substring(start, i);
      if (hasPeriod) {
        t1 = parser.u;
        environment = parser.e;
        if (environment._kind === 10)
          environment = environment._primary;
        recipe = A._Universe_findRule(t1, environment._primary)[string];
        if (recipe == null)
          A.throwExpression('No "' + string + '" in "' + A.Rti__getCanonicalRecipe(environment) + '"');
        stack.push(A._Universe_evalInEnvironment(t1, environment, recipe));
      } else
        stack.push(string);
      return i;
    },
    _Parser_handleArguments(parser, stack) {
      var optionalPositional, named, requiredPositional, returnType, parameters, _null = null,
        t1 = parser.u,
        head = stack.pop();
      if (typeof head == "number")
        switch (head) {
          case -1:
            optionalPositional = stack.pop();
            named = _null;
            break;
          case -2:
            named = stack.pop();
            optionalPositional = _null;
            break;
          default:
            stack.push(head);
            named = _null;
            optionalPositional = named;
            break;
        }
      else {
        stack.push(head);
        named = _null;
        optionalPositional = named;
      }
      requiredPositional = A._Parser_collectArray(parser, stack);
      head = stack.pop();
      switch (head) {
        case -3:
          head = stack.pop();
          if (optionalPositional == null)
            optionalPositional = t1.sEA;
          if (named == null)
            named = t1.sEA;
          returnType = A._Parser_toType(t1, parser.e, head);
          parameters = new A._FunctionParameters();
          parameters._requiredPositional = requiredPositional;
          parameters._optionalPositional = optionalPositional;
          parameters._named = named;
          stack.push(A._Universe__lookupFunctionRti(t1, returnType, parameters));
          return;
        case -4:
          stack.push(A._Universe__lookupRecordRti(t1, stack.pop(), requiredPositional));
          return;
        default:
          throw A.wrapException(A.AssertionError$("Unexpected state under `()`: " + A.S(head)));
      }
    },
    _Parser_handleExtendedOperations(parser, stack) {
      var $top = stack.pop();
      if (0 === $top) {
        stack.push(A._Universe__lookupTerminalRti(parser.u, 1, "0&"));
        return;
      }
      if (1 === $top) {
        stack.push(A._Universe__lookupTerminalRti(parser.u, 4, "1&"));
        return;
      }
      throw A.wrapException(A.AssertionError$("Unexpected extended operation " + A.S($top)));
    },
    _Parser_collectArray(parser, stack) {
      var array = stack.splice(parser.p);
      A._Parser_toTypes(parser.u, parser.e, array);
      parser.p = stack.pop();
      return array;
    },
    _Parser_toType(universe, environment, item) {
      if (typeof item == "string")
        return A._Universe__lookupInterfaceRti(universe, item, universe.sEA);
      else if (typeof item == "number") {
        environment.toString;
        return A._Parser_indexToType(universe, environment, item);
      } else
        return item;
    },
    _Parser_toTypes(universe, environment, items) {
      var i,
        $length = items.length;
      for (i = 0; i < $length; ++i)
        items[i] = A._Parser_toType(universe, environment, items[i]);
    },
    _Parser_toTypesNamed(universe, environment, items) {
      var i,
        $length = items.length;
      for (i = 2; i < $length; i += 3)
        items[i] = A._Parser_toType(universe, environment, items[i]);
    },
    _Parser_indexToType(universe, environment, index) {
      var typeArguments, len,
        kind = environment._kind;
      if (kind === 10) {
        if (index === 0)
          return environment._primary;
        typeArguments = environment._rest;
        len = typeArguments.length;
        if (index <= len)
          return typeArguments[index - 1];
        index -= len;
        environment = environment._primary;
        kind = environment._kind;
      } else if (index === 0)
        return environment;
      if (kind !== 9)
        throw A.wrapException(A.AssertionError$("Indexed base must be an interface type"));
      typeArguments = environment._rest;
      if (index <= typeArguments.length)
        return typeArguments[index - 1];
      throw A.wrapException(A.AssertionError$("Bad index " + index + " for " + environment.toString$0(0)));
    },
    _isSubtype(universe, s, sEnv, t, tEnv) {
      var t1, sKind, leftTypeVariable, tKind, sBounds, tBounds, sLength, i, sBound, tBound;
      if (s === t)
        return true;
      if (!A.isStrongTopType(t))
        if (!(t === type$.legacy_Object))
          t1 = false;
        else
          t1 = true;
      else
        t1 = true;
      if (t1)
        return true;
      sKind = s._kind;
      if (sKind === 4)
        return true;
      if (A.isStrongTopType(s))
        return false;
      if (s._kind !== 1)
        t1 = false;
      else
        t1 = true;
      if (t1)
        return true;
      leftTypeVariable = sKind === 14;
      if (leftTypeVariable)
        if (A._isSubtype(universe, sEnv[s._primary], sEnv, t, tEnv))
          return true;
      tKind = t._kind;
      t1 = s === type$.Null || s === type$.JSNull;
      if (t1) {
        if (tKind === 8)
          return A._isSubtype(universe, s, sEnv, t._primary, tEnv);
        return t === type$.Null || t === type$.JSNull || tKind === 7 || tKind === 6;
      }
      if (t === type$.Object) {
        if (sKind === 8)
          return A._isSubtype(universe, s._primary, sEnv, t, tEnv);
        if (sKind === 6)
          return A._isSubtype(universe, s._primary, sEnv, t, tEnv);
        return sKind !== 7;
      }
      if (sKind === 6)
        return A._isSubtype(universe, s._primary, sEnv, t, tEnv);
      if (tKind === 6) {
        t1 = A.Rti__getQuestionFromStar(universe, t);
        return A._isSubtype(universe, s, sEnv, t1, tEnv);
      }
      if (sKind === 8) {
        if (!A._isSubtype(universe, s._primary, sEnv, t, tEnv))
          return false;
        return A._isSubtype(universe, A.Rti__getFutureFromFutureOr(universe, s), sEnv, t, tEnv);
      }
      if (sKind === 7) {
        t1 = A._isSubtype(universe, type$.Null, sEnv, t, tEnv);
        return t1 && A._isSubtype(universe, s._primary, sEnv, t, tEnv);
      }
      if (tKind === 8) {
        if (A._isSubtype(universe, s, sEnv, t._primary, tEnv))
          return true;
        return A._isSubtype(universe, s, sEnv, A.Rti__getFutureFromFutureOr(universe, t), tEnv);
      }
      if (tKind === 7) {
        t1 = A._isSubtype(universe, s, sEnv, type$.Null, tEnv);
        return t1 || A._isSubtype(universe, s, sEnv, t._primary, tEnv);
      }
      if (leftTypeVariable)
        return false;
      t1 = sKind !== 12;
      if ((!t1 || sKind === 13) && t === type$.Function)
        return true;
      if (tKind === 13) {
        if (s === type$.JavaScriptFunction)
          return true;
        if (sKind !== 13)
          return false;
        sBounds = s._rest;
        tBounds = t._rest;
        sLength = sBounds.length;
        if (sLength !== tBounds.length)
          return false;
        sEnv = sEnv == null ? sBounds : sBounds.concat(sEnv);
        tEnv = tEnv == null ? tBounds : tBounds.concat(tEnv);
        for (i = 0; i < sLength; ++i) {
          sBound = sBounds[i];
          tBound = tBounds[i];
          if (!A._isSubtype(universe, sBound, sEnv, tBound, tEnv) || !A._isSubtype(universe, tBound, tEnv, sBound, sEnv))
            return false;
        }
        return A._isFunctionSubtype(universe, s._primary, sEnv, t._primary, tEnv);
      }
      if (tKind === 12) {
        if (s === type$.JavaScriptFunction)
          return true;
        if (t1)
          return false;
        return A._isFunctionSubtype(universe, s, sEnv, t, tEnv);
      }
      if (sKind === 9) {
        if (tKind !== 9)
          return false;
        return A._isInterfaceSubtype(universe, s, sEnv, t, tEnv);
      }
      t1 = sKind === 11;
      if (t1 && t === type$.Record)
        return true;
      if (t1 && tKind === 11)
        return A._isRecordSubtype(universe, s, sEnv, t, tEnv);
      return false;
    },
    _isFunctionSubtype(universe, s, sEnv, t, tEnv) {
      var sParameters, tParameters, sRequiredPositional, tRequiredPositional, sRequiredPositionalLength, tRequiredPositionalLength, requiredPositionalDelta, sOptionalPositional, tOptionalPositional, sOptionalPositionalLength, tOptionalPositionalLength, i, t1, sNamed, tNamed, sNamedLength, tNamedLength, sIndex, tIndex, tName, sName, sIsRequired;
      if (!A._isSubtype(universe, s._primary, sEnv, t._primary, tEnv))
        return false;
      sParameters = s._rest;
      tParameters = t._rest;
      sRequiredPositional = sParameters._requiredPositional;
      tRequiredPositional = tParameters._requiredPositional;
      sRequiredPositionalLength = sRequiredPositional.length;
      tRequiredPositionalLength = tRequiredPositional.length;
      if (sRequiredPositionalLength > tRequiredPositionalLength)
        return false;
      requiredPositionalDelta = tRequiredPositionalLength - sRequiredPositionalLength;
      sOptionalPositional = sParameters._optionalPositional;
      tOptionalPositional = tParameters._optionalPositional;
      sOptionalPositionalLength = sOptionalPositional.length;
      tOptionalPositionalLength = tOptionalPositional.length;
      if (sRequiredPositionalLength + sOptionalPositionalLength < tRequiredPositionalLength + tOptionalPositionalLength)
        return false;
      for (i = 0; i < sRequiredPositionalLength; ++i) {
        t1 = sRequiredPositional[i];
        if (!A._isSubtype(universe, tRequiredPositional[i], tEnv, t1, sEnv))
          return false;
      }
      for (i = 0; i < requiredPositionalDelta; ++i) {
        t1 = sOptionalPositional[i];
        if (!A._isSubtype(universe, tRequiredPositional[sRequiredPositionalLength + i], tEnv, t1, sEnv))
          return false;
      }
      for (i = 0; i < tOptionalPositionalLength; ++i) {
        t1 = sOptionalPositional[requiredPositionalDelta + i];
        if (!A._isSubtype(universe, tOptionalPositional[i], tEnv, t1, sEnv))
          return false;
      }
      sNamed = sParameters._named;
      tNamed = tParameters._named;
      sNamedLength = sNamed.length;
      tNamedLength = tNamed.length;
      for (sIndex = 0, tIndex = 0; tIndex < tNamedLength; tIndex += 3) {
        tName = tNamed[tIndex];
        for (; true;) {
          if (sIndex >= sNamedLength)
            return false;
          sName = sNamed[sIndex];
          sIndex += 3;
          if (tName < sName)
            return false;
          sIsRequired = sNamed[sIndex - 2];
          if (sName < tName) {
            if (sIsRequired)
              return false;
            continue;
          }
          t1 = tNamed[tIndex + 1];
          if (sIsRequired && !t1)
            return false;
          t1 = sNamed[sIndex - 1];
          if (!A._isSubtype(universe, tNamed[tIndex + 2], tEnv, t1, sEnv))
            return false;
          break;
        }
      }
      for (; sIndex < sNamedLength;) {
        if (sNamed[sIndex + 1])
          return false;
        sIndex += 3;
      }
      return true;
    },
    _isInterfaceSubtype(universe, s, sEnv, t, tEnv) {
      var rule, recipes, $length, supertypeArgs, i, t1, t2,
        sName = s._primary,
        tName = t._primary;
      for (; sName !== tName;) {
        rule = universe.tR[sName];
        if (rule == null)
          return false;
        if (typeof rule == "string") {
          sName = rule;
          continue;
        }
        recipes = rule[tName];
        if (recipes == null)
          return false;
        $length = recipes.length;
        supertypeArgs = $length > 0 ? new Array($length) : init.typeUniverse.sEA;
        for (i = 0; i < $length; ++i)
          supertypeArgs[i] = A._Universe_evalInEnvironment(universe, s, recipes[i]);
        return A._areArgumentsSubtypes(universe, supertypeArgs, null, sEnv, t._rest, tEnv);
      }
      t1 = s._rest;
      t2 = t._rest;
      return A._areArgumentsSubtypes(universe, t1, null, sEnv, t2, tEnv);
    },
    _areArgumentsSubtypes(universe, sArgs, sVariances, sEnv, tArgs, tEnv) {
      var i, t1, t2,
        $length = sArgs.length;
      for (i = 0; i < $length; ++i) {
        t1 = sArgs[i];
        t2 = tArgs[i];
        if (!A._isSubtype(universe, t1, sEnv, t2, tEnv))
          return false;
      }
      return true;
    },
    _isRecordSubtype(universe, s, sEnv, t, tEnv) {
      var i,
        sFields = s._rest,
        tFields = t._rest,
        sCount = sFields.length;
      if (sCount !== tFields.length)
        return false;
      if (s._primary !== t._primary)
        return false;
      for (i = 0; i < sCount; ++i)
        if (!A._isSubtype(universe, sFields[i], sEnv, tFields[i], tEnv))
          return false;
      return true;
    },
    isNullable(t) {
      var t1,
        kind = t._kind;
      if (!(t === type$.Null || t === type$.JSNull))
        if (!A.isStrongTopType(t))
          if (kind !== 7)
            if (!(kind === 6 && A.isNullable(t._primary)))
              t1 = kind === 8 && A.isNullable(t._primary);
            else
              t1 = true;
          else
            t1 = true;
        else
          t1 = true;
      else
        t1 = true;
      return t1;
    },
    isTopType(t) {
      var t1;
      if (!A.isStrongTopType(t))
        if (!(t === type$.legacy_Object))
          t1 = false;
        else
          t1 = true;
      else
        t1 = true;
      return t1;
    },
    isStrongTopType(t) {
      var kind = t._kind;
      return kind === 2 || kind === 3 || kind === 4 || kind === 5 || t === type$.nullable_Object;
    },
    _Utils_objectAssign(o, other) {
      var i, key,
        keys = Object.keys(other),
        $length = keys.length;
      for (i = 0; i < $length; ++i) {
        key = keys[i];
        o[key] = other[key];
      }
    },
    _Utils_newArrayOrEmpty($length) {
      return $length > 0 ? new Array($length) : init.typeUniverse.sEA;
    },
    Rti: function Rti(t0, t1) {
      var _ = this;
      _._as = t0;
      _._is = t1;
      _._cachedRuntimeType = _._specializedTestResource = _._precomputed1 = null;
      _._kind = 0;
      _._canonicalRecipe = _._bindCache = _._evalCache = _._rest = _._primary = null;
    },
    _FunctionParameters: function _FunctionParameters() {
      this._named = this._optionalPositional = this._requiredPositional = null;
    },
    _Type: function _Type(t0) {
      this._rti = t0;
    },
    _Error: function _Error() {
    },
    _TypeError: function _TypeError(t0) {
      this.__rti$_message = t0;
    },
    _AsyncRun__initializeScheduleImmediate() {
      var div, span, t1 = {};
      if (self.scheduleImmediate != null)
        return A.async__AsyncRun__scheduleImmediateJsOverride$closure();
      if (self.MutationObserver != null && self.document != null) {
        div = self.document.createElement("div");
        span = self.document.createElement("span");
        t1.storedCallback = null;
        new self.MutationObserver(A.convertDartClosureToJS(new A._AsyncRun__initializeScheduleImmediate_internalCallback(t1), 1)).observe(div, {childList: true});
        return new A._AsyncRun__initializeScheduleImmediate_closure(t1, div, span);
      } else if (self.setImmediate != null)
        return A.async__AsyncRun__scheduleImmediateWithSetImmediate$closure();
      return A.async__AsyncRun__scheduleImmediateWithTimer$closure();
    },
    _AsyncRun__scheduleImmediateJsOverride(callback) {
      self.scheduleImmediate(A.convertDartClosureToJS(new A._AsyncRun__scheduleImmediateJsOverride_internalCallback(type$.void_Function._as(callback)), 0));
    },
    _AsyncRun__scheduleImmediateWithSetImmediate(callback) {
      self.setImmediate(A.convertDartClosureToJS(new A._AsyncRun__scheduleImmediateWithSetImmediate_internalCallback(type$.void_Function._as(callback)), 0));
    },
    _AsyncRun__scheduleImmediateWithTimer(callback) {
      type$.void_Function._as(callback);
      A._TimerImpl$(0, callback);
    },
    _TimerImpl$(milliseconds, callback) {
      var t1 = new A._TimerImpl();
      t1._TimerImpl$2(milliseconds, callback);
      return t1;
    },
    _makeAsyncAwaitCompleter($T) {
      return new A._AsyncAwaitCompleter(new A._Future($.Zone__current, $T._eval$1("_Future<0>")), $T._eval$1("_AsyncAwaitCompleter<0>"));
    },
    _asyncStartSync(bodyFunction, completer) {
      bodyFunction.call$2(0, null);
      completer.isSync = true;
      return completer._future;
    },
    _asyncAwait(object, bodyFunction) {
      A._awaitOnObject(object, bodyFunction);
    },
    _asyncReturn(object, completer) {
      completer.complete$1(0, object);
    },
    _asyncRethrow(object, completer) {
      completer.completeError$2(A.unwrapException(object), A.getTraceFromException(object));
    },
    _awaitOnObject(object, bodyFunction) {
      var t1, future,
        thenCallback = new A._awaitOnObject_closure(bodyFunction),
        errorCallback = new A._awaitOnObject_closure0(bodyFunction);
      if (object instanceof A._Future)
        object._thenAwait$1$2(thenCallback, errorCallback, type$.dynamic);
      else {
        t1 = type$.dynamic;
        if (type$.Future_dynamic._is(object))
          object.then$1$2$onError(thenCallback, errorCallback, t1);
        else {
          future = new A._Future($.Zone__current, type$._Future_dynamic);
          future._state = 8;
          future._resultOrListeners = object;
          future._thenAwait$1$2(thenCallback, errorCallback, t1);
        }
      }
    },
    _wrapJsFunctionForAsync($function) {
      var $protected = function(fn, ERROR) {
        return function(errorCode, result) {
          while (true)
            try {
              fn(errorCode, result);
              break;
            } catch (error) {
              result = error;
              errorCode = ERROR;
            }
        };
      }($function, 1);
      return $.Zone__current.registerBinaryCallback$3$1(new A._wrapJsFunctionForAsync_closure($protected), type$.void, type$.int, type$.dynamic);
    },
    AsyncError$(error, stackTrace) {
      var t1 = A.checkNotNullable(error, "error", type$.Object);
      return new A.AsyncError(t1, stackTrace == null ? A.AsyncError_defaultStackTrace(error) : stackTrace);
    },
    AsyncError_defaultStackTrace(error) {
      var stackTrace;
      if (type$.Error._is(error)) {
        stackTrace = error.get$stackTrace();
        if (stackTrace != null)
          return stackTrace;
      }
      return B.C__StringStackTrace;
    },
    _Future__chainCoreFuture(source, target) {
      var t1, t2, listeners;
      for (t1 = type$._Future_dynamic; t2 = source._state, (t2 & 4) !== 0;)
        source = t1._as(source._resultOrListeners);
      if ((t2 & 24) !== 0) {
        listeners = target._removeListeners$0();
        target._cloneResult$1(source);
        A._Future__propagateToListeners(target, listeners);
      } else {
        listeners = type$.nullable__FutureListener_dynamic_dynamic._as(target._resultOrListeners);
        target._state = target._state & 1 | 4;
        target._resultOrListeners = source;
        source._prependListeners$1(listeners);
      }
    },
    _Future__propagateToListeners(source, listeners) {
      var t2, t3, t4, _box_0, t5, t6, hasError, asyncError, nextListener, nextListener0, sourceResult, t7, zone, oldZone, result, current, _box_1 = {},
        t1 = _box_1.source = source;
      for (t2 = type$.AsyncError, t3 = type$.nullable__FutureListener_dynamic_dynamic, t4 = type$.Future_dynamic; true;) {
        _box_0 = {};
        t5 = t1._state;
        t6 = (t5 & 16) === 0;
        hasError = !t6;
        if (listeners == null) {
          if (hasError && (t5 & 1) === 0) {
            asyncError = t2._as(t1._resultOrListeners);
            A._rootHandleError(asyncError.error, asyncError.stackTrace);
          }
          return;
        }
        _box_0.listener = listeners;
        nextListener = listeners._nextListener;
        for (t1 = listeners; nextListener != null; t1 = nextListener, nextListener = nextListener0) {
          t1._nextListener = null;
          A._Future__propagateToListeners(_box_1.source, t1);
          _box_0.listener = nextListener;
          nextListener0 = nextListener._nextListener;
        }
        t5 = _box_1.source;
        sourceResult = t5._resultOrListeners;
        _box_0.listenerHasError = hasError;
        _box_0.listenerValueOrError = sourceResult;
        if (t6) {
          t7 = t1.state;
          t7 = (t7 & 1) !== 0 || (t7 & 15) === 8;
        } else
          t7 = true;
        if (t7) {
          zone = t1.result._zone;
          if (hasError) {
            t5 = t5._zone === zone;
            t5 = !(t5 || t5);
          } else
            t5 = false;
          if (t5) {
            t2._as(sourceResult);
            A._rootHandleError(sourceResult.error, sourceResult.stackTrace);
            return;
          }
          oldZone = $.Zone__current;
          if (oldZone !== zone)
            $.Zone__current = zone;
          else
            oldZone = null;
          t1 = t1.state;
          if ((t1 & 15) === 8)
            new A._Future__propagateToListeners_handleWhenCompleteCallback(_box_0, _box_1, hasError).call$0();
          else if (t6) {
            if ((t1 & 1) !== 0)
              new A._Future__propagateToListeners_handleValueCallback(_box_0, sourceResult).call$0();
          } else if ((t1 & 2) !== 0)
            new A._Future__propagateToListeners_handleError(_box_1, _box_0).call$0();
          if (oldZone != null)
            $.Zone__current = oldZone;
          t1 = _box_0.listenerValueOrError;
          if (t4._is(t1)) {
            t5 = _box_0.listener.$ti;
            t5 = t5._eval$1("Future<2>")._is(t1) || !t5._rest[1]._is(t1);
          } else
            t5 = false;
          if (t5) {
            t4._as(t1);
            result = _box_0.listener.result;
            if ((t1._state & 24) !== 0) {
              current = t3._as(result._resultOrListeners);
              result._resultOrListeners = null;
              listeners = result._reverseListeners$1(current);
              result._state = t1._state & 30 | result._state & 1;
              result._resultOrListeners = t1._resultOrListeners;
              _box_1.source = t1;
              continue;
            } else
              A._Future__chainCoreFuture(t1, result);
            return;
          }
        }
        result = _box_0.listener.result;
        current = t3._as(result._resultOrListeners);
        result._resultOrListeners = null;
        listeners = result._reverseListeners$1(current);
        t1 = _box_0.listenerHasError;
        t5 = _box_0.listenerValueOrError;
        if (!t1) {
          result.$ti._precomputed1._as(t5);
          result._state = 8;
          result._resultOrListeners = t5;
        } else {
          t2._as(t5);
          result._state = result._state & 1 | 16;
          result._resultOrListeners = t5;
        }
        _box_1.source = result;
        t1 = result;
      }
    },
    _registerErrorHandler(errorHandler, zone) {
      var t1;
      if (type$.dynamic_Function_Object_StackTrace._is(errorHandler))
        return zone.registerBinaryCallback$3$1(errorHandler, type$.dynamic, type$.Object, type$.StackTrace);
      t1 = type$.dynamic_Function_Object;
      if (t1._is(errorHandler))
        return t1._as(errorHandler);
      throw A.wrapException(A.ArgumentError$value(errorHandler, "onError", string$.Error_));
    },
    _microtaskLoop() {
      var entry, next;
      for (entry = $._nextCallback; entry != null; entry = $._nextCallback) {
        $._lastPriorityCallback = null;
        next = entry.next;
        $._nextCallback = next;
        if (next == null)
          $._lastCallback = null;
        entry.callback.call$0();
      }
    },
    _startMicrotaskLoop() {
      $._isInCallbackLoop = true;
      try {
        A._microtaskLoop();
      } finally {
        $._lastPriorityCallback = null;
        $._isInCallbackLoop = false;
        if ($._nextCallback != null)
          $.$get$_AsyncRun__scheduleImmediateClosure().call$1(A.async___startMicrotaskLoop$closure());
      }
    },
    _scheduleAsyncCallback(callback) {
      var newEntry = new A._AsyncCallbackEntry(callback),
        lastCallback = $._lastCallback;
      if (lastCallback == null) {
        $._nextCallback = $._lastCallback = newEntry;
        if (!$._isInCallbackLoop)
          $.$get$_AsyncRun__scheduleImmediateClosure().call$1(A.async___startMicrotaskLoop$closure());
      } else
        $._lastCallback = lastCallback.next = newEntry;
    },
    _schedulePriorityAsyncCallback(callback) {
      var entry, lastPriorityCallback, next,
        t1 = $._nextCallback;
      if (t1 == null) {
        A._scheduleAsyncCallback(callback);
        $._lastPriorityCallback = $._lastCallback;
        return;
      }
      entry = new A._AsyncCallbackEntry(callback);
      lastPriorityCallback = $._lastPriorityCallback;
      if (lastPriorityCallback == null) {
        entry.next = t1;
        $._nextCallback = $._lastPriorityCallback = entry;
      } else {
        next = lastPriorityCallback.next;
        entry.next = next;
        $._lastPriorityCallback = lastPriorityCallback.next = entry;
        if (next == null)
          $._lastCallback = entry;
      }
    },
    scheduleMicrotask(callback) {
      var t1, _null = null,
        currentZone = $.Zone__current;
      if (B.C__RootZone === currentZone) {
        A._rootScheduleMicrotask(_null, _null, B.C__RootZone, callback);
        return;
      }
      t1 = false;
      if (t1) {
        A._rootScheduleMicrotask(_null, _null, currentZone, type$.void_Function._as(callback));
        return;
      }
      A._rootScheduleMicrotask(_null, _null, currentZone, type$.void_Function._as(currentZone.bindCallbackGuarded$1(callback)));
    },
    StreamIterator_StreamIterator(stream, $T) {
      A.checkNotNullable(stream, "stream", type$.Object);
      return new A._StreamIterator($T._eval$1("_StreamIterator<0>"));
    },
    _rootHandleError(error, stackTrace) {
      A._schedulePriorityAsyncCallback(new A._rootHandleError_closure(error, stackTrace));
    },
    _rootRun($self, $parent, zone, f, $R) {
      var old,
        t1 = $.Zone__current;
      if (t1 === zone)
        return f.call$0();
      $.Zone__current = zone;
      old = t1;
      try {
        t1 = f.call$0();
        return t1;
      } finally {
        $.Zone__current = old;
      }
    },
    _rootRunUnary($self, $parent, zone, f, arg, $R, $T) {
      var old,
        t1 = $.Zone__current;
      if (t1 === zone)
        return f.call$1(arg);
      $.Zone__current = zone;
      old = t1;
      try {
        t1 = f.call$1(arg);
        return t1;
      } finally {
        $.Zone__current = old;
      }
    },
    _rootRunBinary($self, $parent, zone, f, arg1, arg2, $R, T1, T2) {
      var old,
        t1 = $.Zone__current;
      if (t1 === zone)
        return f.call$2(arg1, arg2);
      $.Zone__current = zone;
      old = t1;
      try {
        t1 = f.call$2(arg1, arg2);
        return t1;
      } finally {
        $.Zone__current = old;
      }
    },
    _rootScheduleMicrotask($self, $parent, zone, f) {
      type$.void_Function._as(f);
      if (B.C__RootZone !== zone)
        f = zone.bindCallbackGuarded$1(f);
      A._scheduleAsyncCallback(f);
    },
    _AsyncRun__initializeScheduleImmediate_internalCallback: function _AsyncRun__initializeScheduleImmediate_internalCallback(t0) {
      this._box_0 = t0;
    },
    _AsyncRun__initializeScheduleImmediate_closure: function _AsyncRun__initializeScheduleImmediate_closure(t0, t1, t2) {
      this._box_0 = t0;
      this.div = t1;
      this.span = t2;
    },
    _AsyncRun__scheduleImmediateJsOverride_internalCallback: function _AsyncRun__scheduleImmediateJsOverride_internalCallback(t0) {
      this.callback = t0;
    },
    _AsyncRun__scheduleImmediateWithSetImmediate_internalCallback: function _AsyncRun__scheduleImmediateWithSetImmediate_internalCallback(t0) {
      this.callback = t0;
    },
    _TimerImpl: function _TimerImpl() {
    },
    _TimerImpl_internalCallback: function _TimerImpl_internalCallback(t0, t1) {
      this.$this = t0;
      this.callback = t1;
    },
    _AsyncAwaitCompleter: function _AsyncAwaitCompleter(t0, t1) {
      this._future = t0;
      this.isSync = false;
      this.$ti = t1;
    },
    _awaitOnObject_closure: function _awaitOnObject_closure(t0) {
      this.bodyFunction = t0;
    },
    _awaitOnObject_closure0: function _awaitOnObject_closure0(t0) {
      this.bodyFunction = t0;
    },
    _wrapJsFunctionForAsync_closure: function _wrapJsFunctionForAsync_closure(t0) {
      this.$protected = t0;
    },
    AsyncError: function AsyncError(t0, t1) {
      this.error = t0;
      this.stackTrace = t1;
    },
    _Completer: function _Completer() {
    },
    _AsyncCompleter: function _AsyncCompleter(t0, t1) {
      this.future = t0;
      this.$ti = t1;
    },
    _FutureListener: function _FutureListener(t0, t1, t2, t3, t4) {
      var _ = this;
      _._nextListener = null;
      _.result = t0;
      _.state = t1;
      _.callback = t2;
      _.errorCallback = t3;
      _.$ti = t4;
    },
    _Future: function _Future(t0, t1) {
      var _ = this;
      _._state = 0;
      _._zone = t0;
      _._resultOrListeners = null;
      _.$ti = t1;
    },
    _Future__addListener_closure: function _Future__addListener_closure(t0, t1) {
      this.$this = t0;
      this.listener = t1;
    },
    _Future__prependListeners_closure: function _Future__prependListeners_closure(t0, t1) {
      this._box_0 = t0;
      this.$this = t1;
    },
    _Future__chainForeignFuture_closure: function _Future__chainForeignFuture_closure(t0) {
      this.$this = t0;
    },
    _Future__chainForeignFuture_closure0: function _Future__chainForeignFuture_closure0(t0) {
      this.$this = t0;
    },
    _Future__chainForeignFuture_closure1: function _Future__chainForeignFuture_closure1(t0, t1, t2) {
      this.$this = t0;
      this.e = t1;
      this.s = t2;
    },
    _Future__asyncCompleteWithValue_closure: function _Future__asyncCompleteWithValue_closure(t0, t1) {
      this.$this = t0;
      this.value = t1;
    },
    _Future__chainFuture_closure: function _Future__chainFuture_closure(t0, t1) {
      this.$this = t0;
      this.value = t1;
    },
    _Future__asyncCompleteError_closure: function _Future__asyncCompleteError_closure(t0, t1, t2) {
      this.$this = t0;
      this.error = t1;
      this.stackTrace = t2;
    },
    _Future__propagateToListeners_handleWhenCompleteCallback: function _Future__propagateToListeners_handleWhenCompleteCallback(t0, t1, t2) {
      this._box_0 = t0;
      this._box_1 = t1;
      this.hasError = t2;
    },
    _Future__propagateToListeners_handleWhenCompleteCallback_closure: function _Future__propagateToListeners_handleWhenCompleteCallback_closure(t0) {
      this.originalSource = t0;
    },
    _Future__propagateToListeners_handleValueCallback: function _Future__propagateToListeners_handleValueCallback(t0, t1) {
      this._box_0 = t0;
      this.sourceResult = t1;
    },
    _Future__propagateToListeners_handleError: function _Future__propagateToListeners_handleError(t0, t1) {
      this._box_1 = t0;
      this._box_0 = t1;
    },
    _AsyncCallbackEntry: function _AsyncCallbackEntry(t0) {
      this.callback = t0;
      this.next = null;
    },
    Stream: function Stream() {
    },
    Stream_length_closure: function Stream_length_closure(t0, t1) {
      this._box_0 = t0;
      this.$this = t1;
    },
    Stream_length_closure0: function Stream_length_closure0(t0, t1) {
      this._box_0 = t0;
      this.future = t1;
    },
    StreamSubscription: function StreamSubscription() {
    },
    StreamTransformerBase: function StreamTransformerBase() {
    },
    _StreamIterator: function _StreamIterator(t0) {
      this.$ti = t0;
    },
    _Zone: function _Zone() {
    },
    _rootHandleError_closure: function _rootHandleError_closure(t0, t1) {
      this.error = t0;
      this.stackTrace = t1;
    },
    _RootZone: function _RootZone() {
    },
    _RootZone_bindCallbackGuarded_closure: function _RootZone_bindCallbackGuarded_closure(t0, t1) {
      this.$this = t0;
      this.f = t1;
    },
    _RootZone_bindUnaryCallbackGuarded_closure: function _RootZone_bindUnaryCallbackGuarded_closure(t0, t1, t2) {
      this.$this = t0;
      this.f = t1;
      this.T = t2;
    },
    _HashMap__getTableEntry(table, key) {
      var entry = table[key];
      return entry === table ? null : entry;
    },
    _HashMap__setTableEntry(table, key, value) {
      if (value == null)
        table[key] = table;
      else
        table[key] = value;
    },
    _HashMap__newHashTable() {
      var table = Object.create(null);
      A._HashMap__setTableEntry(table, "<non-identifier-key>", table);
      delete table["<non-identifier-key>"];
      return table;
    },
    LinkedHashMap_LinkedHashMap$_literal(keyValuePairs, $K, $V) {
      return $K._eval$1("@<0>")._bind$1($V)._eval$1("LinkedHashMap<1,2>")._as(A.fillLiteralMap(keyValuePairs, new A.JsLinkedHashMap($K._eval$1("@<0>")._bind$1($V)._eval$1("JsLinkedHashMap<1,2>"))));
    },
    LinkedHashMap_LinkedHashMap$_empty($K, $V) {
      return new A.JsLinkedHashMap($K._eval$1("@<0>")._bind$1($V)._eval$1("JsLinkedHashMap<1,2>"));
    },
    IterableBase_iterableToShortString(iterable, leftDelimiter, rightDelimiter) {
      var parts, t1;
      if (A._isToStringVisiting(iterable)) {
        if (leftDelimiter === "(" && rightDelimiter === ")")
          return "(...)";
        return leftDelimiter + "..." + rightDelimiter;
      }
      parts = A._setArrayType([], type$.JSArray_String);
      B.JSArray_methods.add$1($._toStringVisiting, iterable);
      try {
        A._iterablePartsToStrings(iterable, parts);
      } finally {
        if (0 >= $._toStringVisiting.length)
          return A.ioore($._toStringVisiting, -1);
        $._toStringVisiting.pop();
      }
      t1 = A.StringBuffer__writeAll(leftDelimiter, type$.Iterable_dynamic._as(parts), ", ") + rightDelimiter;
      return t1.charCodeAt(0) == 0 ? t1 : t1;
    },
    IterableBase_iterableToFullString(iterable, leftDelimiter, rightDelimiter) {
      var buffer, t1;
      if (A._isToStringVisiting(iterable))
        return leftDelimiter + "..." + rightDelimiter;
      buffer = new A.StringBuffer(leftDelimiter);
      B.JSArray_methods.add$1($._toStringVisiting, iterable);
      try {
        t1 = buffer;
        t1._contents = A.StringBuffer__writeAll(t1._contents, iterable, ", ");
      } finally {
        if (0 >= $._toStringVisiting.length)
          return A.ioore($._toStringVisiting, -1);
        $._toStringVisiting.pop();
      }
      buffer._contents += rightDelimiter;
      t1 = buffer._contents;
      return t1.charCodeAt(0) == 0 ? t1 : t1;
    },
    _isToStringVisiting(o) {
      var t1, i;
      for (t1 = $._toStringVisiting.length, i = 0; i < t1; ++i)
        if (o === $._toStringVisiting[i])
          return true;
      return false;
    },
    _iterablePartsToStrings(iterable, parts) {
      var next, ultimateString, penultimateString, penultimate, ultimate, ultimate0, elision,
        it = iterable.get$iterator(iterable),
        $length = 0, count = 0;
      while (true) {
        if (!($length < 80 || count < 3))
          break;
        if (!it.moveNext$0())
          return;
        next = A.S(it.get$current(it));
        B.JSArray_methods.add$1(parts, next);
        $length += next.length + 2;
        ++count;
      }
      if (!it.moveNext$0()) {
        if (count <= 5)
          return;
        if (0 >= parts.length)
          return A.ioore(parts, -1);
        ultimateString = parts.pop();
        if (0 >= parts.length)
          return A.ioore(parts, -1);
        penultimateString = parts.pop();
      } else {
        penultimate = it.get$current(it);
        ++count;
        if (!it.moveNext$0()) {
          if (count <= 4) {
            B.JSArray_methods.add$1(parts, A.S(penultimate));
            return;
          }
          ultimateString = A.S(penultimate);
          if (0 >= parts.length)
            return A.ioore(parts, -1);
          penultimateString = parts.pop();
          $length += ultimateString.length + 2;
        } else {
          ultimate = it.get$current(it);
          ++count;
          for (; it.moveNext$0(); penultimate = ultimate, ultimate = ultimate0) {
            ultimate0 = it.get$current(it);
            ++count;
            if (count > 100) {
              while (true) {
                if (!($length > 75 && count > 3))
                  break;
                if (0 >= parts.length)
                  return A.ioore(parts, -1);
                $length -= parts.pop().length + 2;
                --count;
              }
              B.JSArray_methods.add$1(parts, "...");
              return;
            }
          }
          penultimateString = A.S(penultimate);
          ultimateString = A.S(ultimate);
          $length += ultimateString.length + penultimateString.length + 4;
        }
      }
      if (count > parts.length + 2) {
        $length += 5;
        elision = "...";
      } else
        elision = null;
      while (true) {
        if (!($length > 80 && parts.length > 3))
          break;
        if (0 >= parts.length)
          return A.ioore(parts, -1);
        $length -= parts.pop().length + 2;
        if (elision == null) {
          $length += 5;
          elision = "...";
        }
      }
      if (elision != null)
        B.JSArray_methods.add$1(parts, elision);
      B.JSArray_methods.add$1(parts, penultimateString);
      B.JSArray_methods.add$1(parts, ultimateString);
    },
    MapBase_mapToString(m) {
      var result, t1 = {};
      if (A._isToStringVisiting(m))
        return "{...}";
      result = new A.StringBuffer("");
      try {
        B.JSArray_methods.add$1($._toStringVisiting, m);
        result._contents += "{";
        t1.first = true;
        J.forEach$1$x(m, new A.MapBase_mapToString_closure(t1, result));
        result._contents += "}";
      } finally {
        if (0 >= $._toStringVisiting.length)
          return A.ioore($._toStringVisiting, -1);
        $._toStringVisiting.pop();
      }
      t1 = result._contents;
      return t1.charCodeAt(0) == 0 ? t1 : t1;
    },
    _HashMap: function _HashMap() {
    },
    _IdentityHashMap: function _IdentityHashMap(t0) {
      var _ = this;
      _._collection$_length = 0;
      _._keys = _._collection$_rest = _._collection$_nums = _._collection$_strings = null;
      _.$ti = t0;
    },
    _HashMapKeyIterable: function _HashMapKeyIterable(t0, t1) {
      this._collection$_map = t0;
      this.$ti = t1;
    },
    _HashMapKeyIterator: function _HashMapKeyIterator(t0, t1, t2) {
      var _ = this;
      _._collection$_map = t0;
      _._keys = t1;
      _._offset = 0;
      _._collection$_current = null;
      _.$ti = t2;
    },
    ListBase: function ListBase() {
    },
    ListMixin: function ListMixin() {
    },
    MapBase: function MapBase() {
    },
    MapBase_mapToString_closure: function MapBase_mapToString_closure(t0, t1) {
      this._box_0 = t0;
      this.result = t1;
    },
    MapMixin: function MapMixin() {
    },
    _UnmodifiableMapMixin: function _UnmodifiableMapMixin() {
    },
    MapView: function MapView() {
    },
    UnmodifiableMapView: function UnmodifiableMapView() {
    },
    _ListBase_Object_ListMixin: function _ListBase_Object_ListMixin() {
    },
    _UnmodifiableMapView_MapView__UnmodifiableMapMixin: function _UnmodifiableMapView_MapView__UnmodifiableMapMixin() {
    },
    _Base64Encoder_encodeChunk(alphabet, bytes, start, end, isLast, output, outputIndex, state) {
      var t1, t2, i, byteOr, byte, outputIndex0, t3, outputIndex1,
        bits = state >>> 2,
        expectedChars = 3 - (state & 3);
      for (t1 = bytes.length, t2 = output.length, i = start, byteOr = 0; i < end; ++i) {
        if (!(i < t1))
          return A.ioore(bytes, i);
        byte = bytes[i];
        byteOr |= byte;
        bits = (bits << 8 | byte) & 16777215;
        --expectedChars;
        if (expectedChars === 0) {
          outputIndex0 = outputIndex + 1;
          t3 = B.JSString_methods._codeUnitAt$1(alphabet, bits >>> 18 & 63);
          if (!(outputIndex < t2))
            return A.ioore(output, outputIndex);
          output[outputIndex] = t3;
          outputIndex = outputIndex0 + 1;
          t3 = B.JSString_methods._codeUnitAt$1(alphabet, bits >>> 12 & 63);
          if (!(outputIndex0 < t2))
            return A.ioore(output, outputIndex0);
          output[outputIndex0] = t3;
          outputIndex0 = outputIndex + 1;
          t3 = B.JSString_methods._codeUnitAt$1(alphabet, bits >>> 6 & 63);
          if (!(outputIndex < t2))
            return A.ioore(output, outputIndex);
          output[outputIndex] = t3;
          outputIndex = outputIndex0 + 1;
          t3 = B.JSString_methods._codeUnitAt$1(alphabet, bits & 63);
          if (!(outputIndex0 < t2))
            return A.ioore(output, outputIndex0);
          output[outputIndex0] = t3;
          bits = 0;
          expectedChars = 3;
        }
      }
      if (byteOr >= 0 && byteOr <= 255) {
        if (expectedChars < 3) {
          outputIndex0 = outputIndex + 1;
          outputIndex1 = outputIndex0 + 1;
          if (3 - expectedChars === 1) {
            t1 = B.JSString_methods._codeUnitAt$1(alphabet, bits >>> 2 & 63);
            if (!(outputIndex < t2))
              return A.ioore(output, outputIndex);
            output[outputIndex] = t1;
            t1 = B.JSString_methods._codeUnitAt$1(alphabet, bits << 4 & 63);
            if (!(outputIndex0 < t2))
              return A.ioore(output, outputIndex0);
            output[outputIndex0] = t1;
            outputIndex = outputIndex1 + 1;
            if (!(outputIndex1 < t2))
              return A.ioore(output, outputIndex1);
            output[outputIndex1] = 61;
            if (!(outputIndex < t2))
              return A.ioore(output, outputIndex);
            output[outputIndex] = 61;
          } else {
            t1 = B.JSString_methods._codeUnitAt$1(alphabet, bits >>> 10 & 63);
            if (!(outputIndex < t2))
              return A.ioore(output, outputIndex);
            output[outputIndex] = t1;
            t1 = B.JSString_methods._codeUnitAt$1(alphabet, bits >>> 4 & 63);
            if (!(outputIndex0 < t2))
              return A.ioore(output, outputIndex0);
            output[outputIndex0] = t1;
            outputIndex = outputIndex1 + 1;
            t1 = B.JSString_methods._codeUnitAt$1(alphabet, bits << 2 & 63);
            if (!(outputIndex1 < t2))
              return A.ioore(output, outputIndex1);
            output[outputIndex1] = t1;
            if (!(outputIndex < t2))
              return A.ioore(output, outputIndex);
            output[outputIndex] = 61;
          }
          return 0;
        }
        return (bits << 2 | 3 - expectedChars) >>> 0;
      }
      for (i = start; i < end;) {
        if (!(i < t1))
          return A.ioore(bytes, i);
        byte = bytes[i];
        if (byte > 255)
          break;
        ++i;
      }
      if (!(i < t1))
        return A.ioore(bytes, i);
      throw A.wrapException(A.ArgumentError$value(bytes, "Not a byte value at index " + i + ": 0x" + B.JSInt_methods.toRadixString$1(bytes[i], 16), null));
    },
    _Base64Decoder_decodeChunk(input, start, end, output, outIndex, state) {
      var t1, t2, i, charOr, char, t3, code, outIndex0, expectedPadding,
        _s31_ = "Invalid encoding before padding",
        _s17_ = "Invalid character",
        bits = B.JSInt_methods._shrOtherPositive$1(state, 2),
        count = state & 3,
        inverseAlphabet = $.$get$_Base64Decoder__inverseAlphabet();
      for (t1 = inverseAlphabet.length, t2 = output.length, i = start, charOr = 0; i < end; ++i) {
        char = B.JSString_methods._codeUnitAt$1(input, i);
        charOr |= char;
        t3 = char & 127;
        if (!(t3 < t1))
          return A.ioore(inverseAlphabet, t3);
        code = inverseAlphabet[t3];
        if (code >= 0) {
          bits = (bits << 6 | code) & 16777215;
          count = count + 1 & 3;
          if (count === 0) {
            outIndex0 = outIndex + 1;
            if (!(outIndex < t2))
              return A.ioore(output, outIndex);
            output[outIndex] = bits >>> 16 & 255;
            outIndex = outIndex0 + 1;
            if (!(outIndex0 < t2))
              return A.ioore(output, outIndex0);
            output[outIndex0] = bits >>> 8 & 255;
            outIndex0 = outIndex + 1;
            if (!(outIndex < t2))
              return A.ioore(output, outIndex);
            output[outIndex] = bits & 255;
            outIndex = outIndex0;
            bits = 0;
          }
          continue;
        } else if (code === -1 && count > 1) {
          if (charOr > 127)
            break;
          if (count === 3) {
            if ((bits & 3) !== 0)
              throw A.wrapException(A.FormatException$(_s31_, input, i));
            outIndex0 = outIndex + 1;
            if (!(outIndex < t2))
              return A.ioore(output, outIndex);
            output[outIndex] = bits >>> 10;
            if (!(outIndex0 < t2))
              return A.ioore(output, outIndex0);
            output[outIndex0] = bits >>> 2;
          } else {
            if ((bits & 15) !== 0)
              throw A.wrapException(A.FormatException$(_s31_, input, i));
            if (!(outIndex < t2))
              return A.ioore(output, outIndex);
            output[outIndex] = bits >>> 4;
          }
          expectedPadding = (3 - count) * 3;
          if (char === 37)
            expectedPadding += 2;
          return A._Base64Decoder__checkPadding(input, i + 1, end, -expectedPadding - 1);
        }
        throw A.wrapException(A.FormatException$(_s17_, input, i));
      }
      if (charOr >= 0 && charOr <= 127)
        return (bits << 2 | count) >>> 0;
      for (i = start; i < end; ++i) {
        char = B.JSString_methods._codeUnitAt$1(input, i);
        if (char > 127)
          break;
      }
      throw A.wrapException(A.FormatException$(_s17_, input, i));
    },
    _Base64Decoder__allocateBuffer(input, start, end, state) {
      var paddingStart = A._Base64Decoder__trimPaddingChars(input, start, end),
        $length = (state & 3) + (paddingStart - start),
        bufferLength = B.JSInt_methods._shrOtherPositive$1($length, 2) * 3,
        remainderLength = $length & 3;
      if (remainderLength !== 0 && paddingStart < end)
        bufferLength += remainderLength - 1;
      if (bufferLength > 0)
        return new Uint8Array(bufferLength);
      return $.$get$_Base64Decoder__emptyBuffer();
    },
    _Base64Decoder__trimPaddingChars(input, start, end) {
      var char,
        newEnd = end,
        index = newEnd,
        padding = 0;
      while (true) {
        if (!(index > start && padding < 2))
          break;
        c$0: {
          --index;
          char = B.JSString_methods.codeUnitAt$1(input, index);
          if (char === 61) {
            ++padding;
            newEnd = index;
            break c$0;
          }
          if ((char | 32) === 100) {
            if (index === start)
              break;
            --index;
            char = B.JSString_methods.codeUnitAt$1(input, index);
          }
          if (char === 51) {
            if (index === start)
              break;
            --index;
            char = B.JSString_methods.codeUnitAt$1(input, index);
          }
          if (char === 37) {
            ++padding;
            newEnd = index;
            break c$0;
          }
          break;
        }
      }
      return newEnd;
    },
    _Base64Decoder__checkPadding(input, start, end, state) {
      var expectedPadding, char;
      if (start === end)
        return state;
      expectedPadding = -state - 1;
      for (; expectedPadding > 0;) {
        char = B.JSString_methods._codeUnitAt$1(input, start);
        if (expectedPadding === 3) {
          if (char === 61) {
            expectedPadding -= 3;
            ++start;
            break;
          }
          if (char === 37) {
            --expectedPadding;
            ++start;
            if (start === end)
              break;
            char = B.JSString_methods._codeUnitAt$1(input, start);
          } else
            break;
        }
        if ((expectedPadding > 3 ? expectedPadding - 3 : expectedPadding) === 2) {
          if (char !== 51)
            break;
          ++start;
          --expectedPadding;
          if (start === end)
            break;
          char = B.JSString_methods._codeUnitAt$1(input, start);
        }
        if ((char | 32) !== 100)
          break;
        ++start;
        --expectedPadding;
        if (start === end)
          break;
      }
      if (start !== end)
        throw A.wrapException(A.FormatException$("Invalid padding character", input, start));
      return -expectedPadding - 1;
    },
    Base64Codec: function Base64Codec() {
    },
    Base64Encoder: function Base64Encoder() {
    },
    _Base64Encoder: function _Base64Encoder(t0) {
      this._convert$_state = 0;
      this._alphabet = t0;
    },
    Base64Decoder: function Base64Decoder() {
    },
    _Base64Decoder: function _Base64Decoder() {
      this._convert$_state = 0;
    },
    Codec: function Codec() {
    },
    Converter: function Converter() {
    },
    Error__objectToString(object) {
      if (object instanceof A.Closure)
        return object.toString$0(0);
      return "Instance of '" + A.Primitives_objectTypeName(object) + "'";
    },
    Error__throw(error, stackTrace) {
      error = A.wrapException(error);
      if (error == null)
        error = type$.Object._as(error);
      error.stack = stackTrace.toString$0(0);
      throw error;
      throw A.wrapException("unreachable");
    },
    List_List$filled($length, fill, growable, $E) {
      var i,
        result = J.JSArray_JSArray$fixed($length, $E);
      if ($length !== 0 && fill != null)
        for (i = 0; i < $length; ++i)
          result[i] = fill;
      return result;
    },
    List_List$of(elements, growable, $E) {
      var t1 = A.List_List$_of(elements, $E);
      return t1;
    },
    List_List$_of(elements, $E) {
      var list, t1;
      if (Array.isArray(elements))
        return A._setArrayType(elements.slice(0), $E._eval$1("JSArray<0>"));
      list = A._setArrayType([], $E._eval$1("JSArray<0>"));
      for (t1 = J.get$iterator$ax(elements); t1.moveNext$0();)
        B.JSArray_methods.add$1(list, t1.get$current(t1));
      return list;
    },
    String_String$fromCharCodes(charCodes) {
      var t1 = A.Primitives_stringFromNativeUint8List(charCodes, 0, A.RangeError_checkValidRange(0, null, charCodes.length));
      return t1;
    },
    StringBuffer__writeAll(string, objects, separator) {
      var iterator = J.get$iterator$ax(objects);
      if (!iterator.moveNext$0())
        return string;
      if (separator.length === 0) {
        do
          string += A.S(iterator.get$current(iterator));
        while (iterator.moveNext$0());
      } else {
        string += A.S(iterator.get$current(iterator));
        for (; iterator.moveNext$0();)
          string = string + separator + A.S(iterator.get$current(iterator));
      }
      return string;
    },
    NoSuchMethodError$_(receiver, memberName, positionalArguments, namedArguments, existingArgumentNames) {
      return new A.NoSuchMethodError(receiver, memberName, positionalArguments, namedArguments, existingArgumentNames);
    },
    DateTime__fourDigits(n) {
      var absN = Math.abs(n),
        sign = n < 0 ? "-" : "";
      if (absN >= 1000)
        return "" + n;
      if (absN >= 100)
        return sign + "0" + absN;
      if (absN >= 10)
        return sign + "00" + absN;
      return sign + "000" + absN;
    },
    DateTime__threeDigits(n) {
      if (n >= 100)
        return "" + n;
      if (n >= 10)
        return "0" + n;
      return "00" + n;
    },
    DateTime__twoDigits(n) {
      if (n >= 10)
        return "" + n;
      return "0" + n;
    },
    Error_safeToString(object) {
      if (typeof object == "number" || A._isBool(object) || object == null)
        return J.toString$0$(object);
      if (typeof object == "string")
        return JSON.stringify(object);
      return A.Error__objectToString(object);
    },
    AssertionError$(message) {
      return new A.AssertionError(message);
    },
    ArgumentError$(message, $name) {
      return new A.ArgumentError(false, null, $name, message);
    },
    ArgumentError$value(value, $name, message) {
      return new A.ArgumentError(true, value, $name, message);
    },
    RangeError$value(value, $name) {
      return new A.RangeError(null, null, true, value, $name, "Value not in range");
    },
    RangeError$range(invalidValue, minValue, maxValue, $name, message) {
      return new A.RangeError(minValue, maxValue, true, invalidValue, $name, "Invalid value");
    },
    RangeError_checkValidRange(start, end, $length) {
      if (0 > start || start > $length)
        throw A.wrapException(A.RangeError$range(start, 0, $length, "start", null));
      if (end != null) {
        if (start > end || end > $length)
          throw A.wrapException(A.RangeError$range(end, start, $length, "end", null));
        return end;
      }
      return $length;
    },
    RangeError_checkNotNegative(value, $name) {
      if (value < 0)
        throw A.wrapException(A.RangeError$range(value, 0, null, $name, null));
      return value;
    },
    IndexError$withLength(invalidValue, $length, indexable, $name) {
      return new A.IndexError($length, true, invalidValue, $name, "Index out of range");
    },
    UnsupportedError$(message) {
      return new A.UnsupportedError(message);
    },
    UnimplementedError$(message) {
      return new A.UnimplementedError(message);
    },
    StateError$(message) {
      return new A.StateError(message);
    },
    ConcurrentModificationError$(modifiedObject) {
      return new A.ConcurrentModificationError(modifiedObject);
    },
    Exception_Exception(message) {
      return new A._Exception(message);
    },
    FormatException$(message, source, offset) {
      return new A.FormatException(message, source, offset);
    },
    Object_hash(object1, object2, object3, object4) {
      var t2,
        t1 = B.JSNumber_methods.get$hashCode(object1);
      object2 = B.JSNumber_methods.get$hashCode(object2);
      object3 = B.JSNumber_methods.get$hashCode(object3);
      object4 = B.JSNumber_methods.get$hashCode(object4);
      t2 = $.$get$_hashSeed();
      return A.SystemHash_finish(A.SystemHash_combine(A.SystemHash_combine(A.SystemHash_combine(A.SystemHash_combine(t2, t1), object2), object3), object4));
    },
    print(object) {
      A.printString(object);
    },
    NoSuchMethodError_toString_closure: function NoSuchMethodError_toString_closure(t0, t1) {
      this._box_0 = t0;
      this.sb = t1;
    },
    DateTime: function DateTime(t0, t1) {
      this._value = t0;
      this.isUtc = t1;
    },
    _Enum: function _Enum() {
    },
    Error: function Error() {
    },
    AssertionError: function AssertionError(t0) {
      this.message = t0;
    },
    TypeError: function TypeError() {
    },
    NullThrownError: function NullThrownError() {
    },
    ArgumentError: function ArgumentError(t0, t1, t2, t3) {
      var _ = this;
      _._hasValue = t0;
      _.invalidValue = t1;
      _.name = t2;
      _.message = t3;
    },
    RangeError: function RangeError(t0, t1, t2, t3, t4, t5) {
      var _ = this;
      _.start = t0;
      _.end = t1;
      _._hasValue = t2;
      _.invalidValue = t3;
      _.name = t4;
      _.message = t5;
    },
    IndexError: function IndexError(t0, t1, t2, t3, t4) {
      var _ = this;
      _.length = t0;
      _._hasValue = t1;
      _.invalidValue = t2;
      _.name = t3;
      _.message = t4;
    },
    NoSuchMethodError: function NoSuchMethodError(t0, t1, t2, t3, t4) {
      var _ = this;
      _._core$_receiver = t0;
      _._core$_memberName = t1;
      _._core$_arguments = t2;
      _._namedArguments = t3;
      _._existingArgumentNames = t4;
    },
    UnsupportedError: function UnsupportedError(t0) {
      this.message = t0;
    },
    UnimplementedError: function UnimplementedError(t0) {
      this.message = t0;
    },
    StateError: function StateError(t0) {
      this.message = t0;
    },
    ConcurrentModificationError: function ConcurrentModificationError(t0) {
      this.modifiedObject = t0;
    },
    OutOfMemoryError: function OutOfMemoryError() {
    },
    StackOverflowError: function StackOverflowError() {
    },
    CyclicInitializationError: function CyclicInitializationError(t0) {
      this.variableName = t0;
    },
    _Exception: function _Exception(t0) {
      this.message = t0;
    },
    FormatException: function FormatException(t0, t1, t2) {
      this.message = t0;
      this.source = t1;
      this.offset = t2;
    },
    Iterable: function Iterable() {
    },
    Iterator: function Iterator() {
    },
    Null: function Null() {
    },
    Object: function Object() {
    },
    _StringStackTrace: function _StringStackTrace() {
    },
    StringBuffer: function StringBuffer(t0) {
      this._contents = t0;
    },
    _EventStreamSubscription$(_target, _eventType, onData, _useCapture, $T) {
      var t1 = A._wrapZone(new A._EventStreamSubscription_closure(onData), type$.Event);
      if (t1 != null && true)
        B.DedicatedWorkerGlobalScope_methods.addEventListener$3(_target, _eventType, t1, false);
      return new A._EventStreamSubscription(_target, _eventType, t1, false, $T._eval$1("_EventStreamSubscription<0>"));
    },
    _wrapZone(callback, $T) {
      var t1 = $.Zone__current;
      if (t1 === B.C__RootZone)
        return callback;
      return t1.bindUnaryCallbackGuarded$1$1(callback, $T);
    },
    HtmlElement: function HtmlElement() {
    },
    AccessibleNodeList: function AccessibleNodeList() {
    },
    AnchorElement: function AnchorElement() {
    },
    AreaElement: function AreaElement() {
    },
    Blob: function Blob() {
    },
    CharacterData: function CharacterData() {
    },
    CryptoKey: function CryptoKey() {
    },
    CssPerspective: function CssPerspective() {
    },
    CssRule: function CssRule() {
    },
    CssStyleDeclaration: function CssStyleDeclaration() {
    },
    CssStyleDeclarationBase: function CssStyleDeclarationBase() {
    },
    CssStyleValue: function CssStyleValue() {
    },
    CssTransformComponent: function CssTransformComponent() {
    },
    CssTransformValue: function CssTransformValue() {
    },
    CssUnparsedValue: function CssUnparsedValue() {
    },
    DataTransferItem: function DataTransferItem() {
    },
    DataTransferItemList: function DataTransferItemList() {
    },
    DedicatedWorkerGlobalScope: function DedicatedWorkerGlobalScope() {
    },
    DomException: function DomException() {
    },
    DomRectList: function DomRectList() {
    },
    DomRectReadOnly: function DomRectReadOnly() {
    },
    DomStringList: function DomStringList() {
    },
    DomTokenList: function DomTokenList() {
    },
    Element: function Element() {
    },
    Event: function Event() {
    },
    EventTarget: function EventTarget() {
    },
    File: function File() {
    },
    FileList: function FileList() {
    },
    FileWriter: function FileWriter() {
    },
    FormElement: function FormElement() {
    },
    Gamepad: function Gamepad() {
    },
    History: function History() {
    },
    HtmlCollection: function HtmlCollection() {
    },
    ImageData: function ImageData() {
    },
    Location: function Location() {
    },
    MediaDeviceInfo: function MediaDeviceInfo() {
    },
    MediaList: function MediaList() {
    },
    MediaStreamTrack: function MediaStreamTrack() {
    },
    MessageEvent: function MessageEvent() {
    },
    MessagePort: function MessagePort() {
    },
    MidiInputMap: function MidiInputMap() {
    },
    MidiInputMap_keys_closure: function MidiInputMap_keys_closure(t0) {
      this.keys = t0;
    },
    MidiOutputMap: function MidiOutputMap() {
    },
    MidiOutputMap_keys_closure: function MidiOutputMap_keys_closure(t0) {
      this.keys = t0;
    },
    MimeType: function MimeType() {
    },
    MimeTypeArray: function MimeTypeArray() {
    },
    Node: function Node() {
    },
    NodeList: function NodeList() {
    },
    Plugin: function Plugin() {
    },
    PluginArray: function PluginArray() {
    },
    RtcStatsReport: function RtcStatsReport() {
    },
    RtcStatsReport_keys_closure: function RtcStatsReport_keys_closure(t0) {
      this.keys = t0;
    },
    SelectElement: function SelectElement() {
    },
    SharedArrayBuffer: function SharedArrayBuffer() {
    },
    SourceBuffer: function SourceBuffer() {
    },
    SourceBufferList: function SourceBufferList() {
    },
    SpeechGrammar: function SpeechGrammar() {
    },
    SpeechGrammarList: function SpeechGrammarList() {
    },
    SpeechRecognitionResult: function SpeechRecognitionResult() {
    },
    Storage: function Storage() {
    },
    Storage_keys_closure: function Storage_keys_closure(t0) {
      this.keys = t0;
    },
    StyleSheet: function StyleSheet() {
    },
    TextTrack: function TextTrack() {
    },
    TextTrackCue: function TextTrackCue() {
    },
    TextTrackCueList: function TextTrackCueList() {
    },
    TextTrackList: function TextTrackList() {
    },
    TimeRanges: function TimeRanges() {
    },
    Touch: function Touch() {
    },
    TouchList: function TouchList() {
    },
    TrackDefaultList: function TrackDefaultList() {
    },
    TrackElement: function TrackElement() {
    },
    Url: function Url() {
    },
    VideoTrack: function VideoTrack() {
    },
    VideoTrackList: function VideoTrackList() {
    },
    WorkerGlobalScope: function WorkerGlobalScope() {
    },
    _CssRuleList: function _CssRuleList() {
    },
    _DomRect: function _DomRect() {
    },
    _GamepadList: function _GamepadList() {
    },
    _NamedNodeMap: function _NamedNodeMap() {
    },
    _SpeechRecognitionResultList: function _SpeechRecognitionResultList() {
    },
    _StyleSheetList: function _StyleSheetList() {
    },
    EventStreamProvider: function EventStreamProvider(t0) {
      this.$ti = t0;
    },
    _EventStream: function _EventStream(t0, t1, t2, t3) {
      var _ = this;
      _._target = t0;
      _._eventType = t1;
      _._useCapture = t2;
      _.$ti = t3;
    },
    _EventStreamSubscription: function _EventStreamSubscription(t0, t1, t2, t3, t4) {
      var _ = this;
      _._target = t0;
      _._eventType = t1;
      _._onData = t2;
      _._useCapture = t3;
      _.$ti = t4;
    },
    _EventStreamSubscription_closure: function _EventStreamSubscription_closure(t0) {
      this.onData = t0;
    },
    ImmutableListMixin: function ImmutableListMixin() {
    },
    FixedSizeListIterator: function FixedSizeListIterator(t0, t1, t2) {
      var _ = this;
      _._array = t0;
      _._html$_length = t1;
      _._position = -1;
      _._html$_current = null;
      _.$ti = t2;
    },
    _CssStyleDeclaration_JavaScriptObject_CssStyleDeclarationBase: function _CssStyleDeclaration_JavaScriptObject_CssStyleDeclarationBase() {
    },
    _DomRectList_JavaScriptObject_ListMixin: function _DomRectList_JavaScriptObject_ListMixin() {
    },
    _DomRectList_JavaScriptObject_ListMixin_ImmutableListMixin: function _DomRectList_JavaScriptObject_ListMixin_ImmutableListMixin() {
    },
    _DomStringList_JavaScriptObject_ListMixin: function _DomStringList_JavaScriptObject_ListMixin() {
    },
    _DomStringList_JavaScriptObject_ListMixin_ImmutableListMixin: function _DomStringList_JavaScriptObject_ListMixin_ImmutableListMixin() {
    },
    _FileList_JavaScriptObject_ListMixin: function _FileList_JavaScriptObject_ListMixin() {
    },
    _FileList_JavaScriptObject_ListMixin_ImmutableListMixin: function _FileList_JavaScriptObject_ListMixin_ImmutableListMixin() {
    },
    _HtmlCollection_JavaScriptObject_ListMixin: function _HtmlCollection_JavaScriptObject_ListMixin() {
    },
    _HtmlCollection_JavaScriptObject_ListMixin_ImmutableListMixin: function _HtmlCollection_JavaScriptObject_ListMixin_ImmutableListMixin() {
    },
    _MidiInputMap_JavaScriptObject_MapMixin: function _MidiInputMap_JavaScriptObject_MapMixin() {
    },
    _MidiOutputMap_JavaScriptObject_MapMixin: function _MidiOutputMap_JavaScriptObject_MapMixin() {
    },
    _MimeTypeArray_JavaScriptObject_ListMixin: function _MimeTypeArray_JavaScriptObject_ListMixin() {
    },
    _MimeTypeArray_JavaScriptObject_ListMixin_ImmutableListMixin: function _MimeTypeArray_JavaScriptObject_ListMixin_ImmutableListMixin() {
    },
    _NodeList_JavaScriptObject_ListMixin: function _NodeList_JavaScriptObject_ListMixin() {
    },
    _NodeList_JavaScriptObject_ListMixin_ImmutableListMixin: function _NodeList_JavaScriptObject_ListMixin_ImmutableListMixin() {
    },
    _PluginArray_JavaScriptObject_ListMixin: function _PluginArray_JavaScriptObject_ListMixin() {
    },
    _PluginArray_JavaScriptObject_ListMixin_ImmutableListMixin: function _PluginArray_JavaScriptObject_ListMixin_ImmutableListMixin() {
    },
    _RtcStatsReport_JavaScriptObject_MapMixin: function _RtcStatsReport_JavaScriptObject_MapMixin() {
    },
    _SourceBufferList_EventTarget_ListMixin: function _SourceBufferList_EventTarget_ListMixin() {
    },
    _SourceBufferList_EventTarget_ListMixin_ImmutableListMixin: function _SourceBufferList_EventTarget_ListMixin_ImmutableListMixin() {
    },
    _SpeechGrammarList_JavaScriptObject_ListMixin: function _SpeechGrammarList_JavaScriptObject_ListMixin() {
    },
    _SpeechGrammarList_JavaScriptObject_ListMixin_ImmutableListMixin: function _SpeechGrammarList_JavaScriptObject_ListMixin_ImmutableListMixin() {
    },
    _Storage_JavaScriptObject_MapMixin: function _Storage_JavaScriptObject_MapMixin() {
    },
    _TextTrackCueList_JavaScriptObject_ListMixin: function _TextTrackCueList_JavaScriptObject_ListMixin() {
    },
    _TextTrackCueList_JavaScriptObject_ListMixin_ImmutableListMixin: function _TextTrackCueList_JavaScriptObject_ListMixin_ImmutableListMixin() {
    },
    _TextTrackList_EventTarget_ListMixin: function _TextTrackList_EventTarget_ListMixin() {
    },
    _TextTrackList_EventTarget_ListMixin_ImmutableListMixin: function _TextTrackList_EventTarget_ListMixin_ImmutableListMixin() {
    },
    _TouchList_JavaScriptObject_ListMixin: function _TouchList_JavaScriptObject_ListMixin() {
    },
    _TouchList_JavaScriptObject_ListMixin_ImmutableListMixin: function _TouchList_JavaScriptObject_ListMixin_ImmutableListMixin() {
    },
    __CssRuleList_JavaScriptObject_ListMixin: function __CssRuleList_JavaScriptObject_ListMixin() {
    },
    __CssRuleList_JavaScriptObject_ListMixin_ImmutableListMixin: function __CssRuleList_JavaScriptObject_ListMixin_ImmutableListMixin() {
    },
    __GamepadList_JavaScriptObject_ListMixin: function __GamepadList_JavaScriptObject_ListMixin() {
    },
    __GamepadList_JavaScriptObject_ListMixin_ImmutableListMixin: function __GamepadList_JavaScriptObject_ListMixin_ImmutableListMixin() {
    },
    __NamedNodeMap_JavaScriptObject_ListMixin: function __NamedNodeMap_JavaScriptObject_ListMixin() {
    },
    __NamedNodeMap_JavaScriptObject_ListMixin_ImmutableListMixin: function __NamedNodeMap_JavaScriptObject_ListMixin_ImmutableListMixin() {
    },
    __SpeechRecognitionResultList_JavaScriptObject_ListMixin: function __SpeechRecognitionResultList_JavaScriptObject_ListMixin() {
    },
    __SpeechRecognitionResultList_JavaScriptObject_ListMixin_ImmutableListMixin: function __SpeechRecognitionResultList_JavaScriptObject_ListMixin_ImmutableListMixin() {
    },
    __StyleSheetList_JavaScriptObject_ListMixin: function __StyleSheetList_JavaScriptObject_ListMixin() {
    },
    __StyleSheetList_JavaScriptObject_ListMixin_ImmutableListMixin: function __StyleSheetList_JavaScriptObject_ListMixin_ImmutableListMixin() {
    },
    _convertNativeToDart_Value(value) {
      var values, i;
      if (value == null)
        return value;
      if (typeof value == "string" || typeof value == "number" || A._isBool(value))
        return value;
      if (A.isJavaScriptSimpleObject(value))
        return A.convertNativeToDart_Dictionary(value);
      if (Array.isArray(value)) {
        values = [];
        for (i = 0; i < value.length; ++i)
          values.push(A._convertNativeToDart_Value(value[i]));
        return values;
      }
      return value;
    },
    convertNativeToDart_Dictionary(object) {
      var dict, keys, t1, _i, key;
      if (object == null)
        return null;
      dict = A.LinkedHashMap_LinkedHashMap$_empty(type$.String, type$.dynamic);
      keys = Object.getOwnPropertyNames(object);
      for (t1 = keys.length, _i = 0; _i < keys.length; keys.length === t1 || (0, A.throwConcurrentModificationError)(keys), ++_i) {
        key = keys[_i];
        dict.$indexSet(0, key, A._convertNativeToDart_Value(object[key]));
      }
      return dict;
    },
    isJavaScriptSimpleObject(value) {
      var proto = Object.getPrototypeOf(value);
      return proto === Object.prototype || proto === null;
    },
    _StructuredClone: function _StructuredClone() {
    },
    _StructuredClone_walk_closure: function _StructuredClone_walk_closure(t0, t1) {
      this._box_0 = t0;
      this.$this = t1;
    },
    _StructuredClone_walk_closure0: function _StructuredClone_walk_closure0(t0, t1) {
      this._box_0 = t0;
      this.$this = t1;
    },
    _AcceptStructuredClone: function _AcceptStructuredClone() {
    },
    _AcceptStructuredClone_walk_closure: function _AcceptStructuredClone_walk_closure(t0, t1) {
      this.$this = t0;
      this.map = t1;
    },
    _StructuredCloneDart2Js: function _StructuredCloneDart2Js(t0, t1) {
      this.values = t0;
      this.copies = t1;
    },
    _AcceptStructuredCloneDart2Js: function _AcceptStructuredCloneDart2Js(t0, t1) {
      this.values = t0;
      this.copies = t1;
      this.mustCopy = false;
    },
    jsify(object) {
      return A._convertDataTree(object);
    },
    _convertDataTree(data) {
      var t1 = new A._convertDataTree__convert(new A._IdentityHashMap(type$._IdentityHashMap_dynamic_dynamic)).call$1(data);
      t1.toString;
      return t1;
    },
    promiseToFuture(jsPromise, $T) {
      var t1 = new A._Future($.Zone__current, $T._eval$1("_Future<0>")),
        completer = new A._AsyncCompleter(t1, $T._eval$1("_AsyncCompleter<0>"));
      jsPromise.then(A.convertDartClosureToJS(new A.promiseToFuture_closure(completer, $T), 1), A.convertDartClosureToJS(new A.promiseToFuture_closure0(completer), 1));
      return t1;
    },
    _convertDataTree__convert: function _convertDataTree__convert(t0) {
      this._convertedObjects = t0;
    },
    promiseToFuture_closure: function promiseToFuture_closure(t0, t1) {
      this.completer = t0;
      this.T = t1;
    },
    promiseToFuture_closure0: function promiseToFuture_closure0(t0) {
      this.completer = t0;
    },
    NullRejectionException: function NullRejectionException(t0) {
      this.isUndefined = t0;
    },
    _JSSecureRandom: function _JSSecureRandom(t0) {
      this._math$_buffer = t0;
    },
    Length: function Length() {
    },
    LengthList: function LengthList() {
    },
    Number: function Number() {
    },
    NumberList: function NumberList() {
    },
    PointList: function PointList() {
    },
    StringList: function StringList() {
    },
    Transform: function Transform() {
    },
    TransformList: function TransformList() {
    },
    _LengthList_JavaScriptObject_ListMixin: function _LengthList_JavaScriptObject_ListMixin() {
    },
    _LengthList_JavaScriptObject_ListMixin_ImmutableListMixin: function _LengthList_JavaScriptObject_ListMixin_ImmutableListMixin() {
    },
    _NumberList_JavaScriptObject_ListMixin: function _NumberList_JavaScriptObject_ListMixin() {
    },
    _NumberList_JavaScriptObject_ListMixin_ImmutableListMixin: function _NumberList_JavaScriptObject_ListMixin_ImmutableListMixin() {
    },
    _StringList_JavaScriptObject_ListMixin: function _StringList_JavaScriptObject_ListMixin() {
    },
    _StringList_JavaScriptObject_ListMixin_ImmutableListMixin: function _StringList_JavaScriptObject_ListMixin_ImmutableListMixin() {
    },
    _TransformList_JavaScriptObject_ListMixin: function _TransformList_JavaScriptObject_ListMixin() {
    },
    _TransformList_JavaScriptObject_ListMixin_ImmutableListMixin: function _TransformList_JavaScriptObject_ListMixin_ImmutableListMixin() {
    },
    AudioBuffer: function AudioBuffer() {
    },
    AudioParamMap: function AudioParamMap() {
    },
    AudioParamMap_keys_closure: function AudioParamMap_keys_closure(t0) {
      this.keys = t0;
    },
    AudioTrack: function AudioTrack() {
    },
    AudioTrackList: function AudioTrackList() {
    },
    BaseAudioContext: function BaseAudioContext() {
    },
    OfflineAudioContext: function OfflineAudioContext() {
    },
    _AudioParamMap_JavaScriptObject_MapMixin: function _AudioParamMap_JavaScriptObject_MapMixin() {
    },
    WritableStream: function WritableStream() {
    },
    ReadableStream: function ReadableStream() {
    },
    TransformStream: function TransformStream() {
    },
    TransformStreamDefaultController: function TransformStreamDefaultController() {
    },
    EncodedStreams: function EncodedStreams() {
    },
    RTCEncodedFrame: function RTCEncodedFrame() {
    },
    RTCEncodedAudioFrame: function RTCEncodedAudioFrame() {
    },
    RTCEncodedVideoFrame: function RTCEncodedVideoFrame() {
    },
    RTCEncodedFrameMetadata: function RTCEncodedFrameMetadata() {
    },
    RTCEncodedAudioFrameMetadata: function RTCEncodedAudioFrameMetadata() {
    },
    RTCEncodedVideoFrameMetadata: function RTCEncodedVideoFrameMetadata() {
    },
    RTCTransformEvent: function RTCTransformEvent() {
    },
    RTCRtpScriptTransformer: function RTCRtpScriptTransformer() {
    },
    RTCRtpScriptTransform: function RTCRtpScriptTransform() {
    },
    jsArrayBufferFrom(data) {
      if (type$.Uint8List._is(data) && data.byteOffset === 0 && data.byteLength === data.buffer.byteLength)
        return data.buffer;
      return new Uint8Array(A._ensureNativeList(data)).buffer;
    },
    impportKeyFromRawData(secretKeyData, keyUsages, webCryptoAlgorithm) {
      var $async$goto = 0,
        $async$completer = A._makeAsyncAwaitCompleter(type$.CryptoKey),
        $async$returnValue, t1;
      var $async$impportKeyFromRawData = A._wrapJsFunctionForAsync(function($async$errorCode, $async$result) {
        if ($async$errorCode === 1)
          return A._asyncRethrow($async$result, $async$completer);
        while (true)
          switch ($async$goto) {
            case 0:
              // Function start
              t1 = type$.String;
              $async$returnValue = A.promiseToFuture(self.crypto.subtle.importKey("raw", A.jsArrayBufferFrom(secretKeyData), A.jsify(A.LinkedHashMap_LinkedHashMap$_literal(["name", webCryptoAlgorithm], t1, t1)), false, keyUsages), type$.CryptoKey);
              // goto return
              $async$goto = 1;
              break;
            case 1:
              // return
              return A._asyncReturn($async$returnValue, $async$completer);
          }
      });
      return A._asyncStartSync($async$impportKeyFromRawData, $async$completer);
    },
    Promise: function Promise() {
    },
    Algorithm: function Algorithm() {
    },
    AesGcmParams: function AesGcmParams() {
    },
    KeyOptions$(ratchetSalt, ratchetWindowSize, sharedKey) {
      return new A.KeyOptions(sharedKey, ratchetSalt, ratchetWindowSize);
    },
    findNALUIndices(stream) {
      var start, pos0, t1, end,
        result = A._setArrayType([], type$.JSArray_int),
        pos = stream.length,
        searchLength = pos - 2;
      for (start = 0, pos0 = 0; pos0 < searchLength; start = pos0) {
        while (true) {
          if (pos0 < searchLength) {
            if (!(pos0 >= 0))
              return A.ioore(stream, pos0);
            t1 = !(stream[pos0] === 0 && stream[pos0 + 1] === 0 && stream[pos0 + 2] === 1);
          } else
            t1 = false;
          if (!t1)
            break;
          ++pos0;
        }
        if (pos0 >= searchLength)
          pos0 = pos;
        end = pos0;
        while (true) {
          if (end > start) {
            t1 = end - 1;
            if (!(t1 >= 0))
              return A.ioore(stream, t1);
            t1 = stream[t1] === 0;
          } else
            t1 = false;
          if (!t1)
            break;
          --end;
        }
        if (start === 0) {
          if (end !== start)
            throw A.wrapException(A.Exception_Exception("byte stream contains leading data"));
        } else
          B.JSArray_methods.add$1(result, start);
        pos0 += 3;
      }
      return result;
    },
    Cryptor$(keyOptions, participantId, trackId, worker) {
      var t1 = type$.int;
      return new A.Cryptor(A.LinkedHashMap_LinkedHashMap$_empty(t1, t1), participantId, trackId, keyOptions, B.CryptorError_0, worker, A.List_List$filled(16, null, false, type$.nullable_KeySet));
    },
    KeyOptions: function KeyOptions(t0, t1, t2) {
      this.sharedKey = t0;
      this.ratchetSalt = t1;
      this.ratchetWindowSize = t2;
    },
    CryptorError: function CryptorError(t0) {
      this._name = t0;
    },
    KeySet: function KeySet(t0, t1) {
      this.material = t0;
      this.encryptionKey = t1;
    },
    Cryptor: function Cryptor(t0, t1, t2, t3, t4, t5, t6) {
      var _ = this;
      _.sendCounts = t0;
      _.participantId = t1;
      _.trackId = t2;
      _.codec = null;
      _.keyOptions = t3;
      _.__Cryptor_kind_A = $;
      _.enabled = false;
      _.lastError = t4;
      _.worker = t5;
      _.currentKeyIndex = 0;
      _._ratchetCompleter = null;
      _.cryptoKeyRing = t6;
    },
    Cryptor_ratchetKey_closure: function Cryptor_ratchetKey_closure(t0, t1) {
      this.$this = t0;
      this.keyIndex = t1;
    },
    Cryptor_ratchetKey__closure: function Cryptor_ratchetKey__closure(t0, t1) {
      this.$this = t0;
      this.keyIndex = t1;
    },
    Cryptor_ratchetKey___closure: function Cryptor_ratchetKey___closure(t0) {
      this.$this = t0;
    },
    main() {
      var $async$goto = 0,
        $async$completer = A._makeAsyncAwaitCompleter(type$.dynamic),
        t1, t2;
      var $async$main = A._wrapJsFunctionForAsync(function($async$errorCode, $async$result) {
        if ($async$errorCode === 1)
          return A._asyncRethrow($async$result, $async$completer);
        while (true)
          switch ($async$goto) {
            case 0:
              // Function start
              A.print("E2EE Worker created");
              if (self.self.RTCTransformEvent != null) {
                A.print("setup transform event handler");
                self.self.onrtctransform = A.allowInterop(new A.main_closure(), type$.dynamic_Function_dynamic);
              }
              t1 = type$.nullable_EventTarget._as(self.self);
              t2 = type$.nullable_void_Function_MessageEvent._as(new A.main_closure0());
              type$.nullable_void_Function._as(null);
              A._EventStreamSubscription$(t1, "message", t2, false, type$.MessageEvent);
              // implicit return
              return A._asyncReturn(null, $async$completer);
          }
      });
      return A._asyncStartSync($async$main, $async$completer);
    },
    TransformMessage: function TransformMessage() {
    },
    EnableTransformMessage: function EnableTransformMessage() {
    },
    RemoveTransformMessage: function RemoveTransformMessage() {
    },
    main_closure: function main_closure() {
    },
    main__closure8: function main__closure8(t0) {
      this.trackId = t0;
    },
    main_closure0: function main_closure0() {
    },
    main__closure: function main__closure(t0) {
      this.participantId = t0;
    },
    main__closure0: function main__closure0(t0) {
      this.trackId = t0;
    },
    main__closure1: function main__closure1(t0) {
      this.trackId = t0;
    },
    main__closure2: function main__closure2(t0) {
      this.participantId = t0;
    },
    main__closure3: function main__closure3(t0) {
      this.participantId = t0;
    },
    main__closure4: function main__closure4(t0, t1, t2) {
      this.c = t0;
      this.keySet = t1;
      this.participantId = t2;
    },
    main__closure5: function main__closure5(t0) {
      this.participantId = t0;
    },
    main__closure6: function main__closure6(t0) {
      this.trackId = t0;
    },
    main__closure7: function main__closure7(t0) {
      this.trackId = t0;
    },
    printString(string) {
      if (typeof dartPrint == "function") {
        dartPrint(string);
        return;
      }
      if (typeof console == "object" && typeof console.log != "undefined") {
        console.log(string);
        return;
      }
      if (typeof window == "object")
        return;
      if (typeof print == "function") {
        print(string);
        return;
      }
      throw "Unable to print message: " + String(string);
    },
    throwLateFieldNI(fieldName) {
      return A.throwExpression(A.LateError$fieldNI(fieldName));
    },
    throwLateFieldADI(fieldName) {
      return A.throwExpression(new A.LateError("Field '" + fieldName + "' has been assigned during initialization."));
    },
    _convertDartFunctionFast(f) {
      var ret,
        existing = f.$dart_jsFunction;
      if (existing != null)
        return existing;
      ret = function(_call, f) {
        return function() {
          return _call(f, Array.prototype.slice.apply(arguments));
        };
      }(A._callDartFunctionFast, f);
      ret[$.$get$DART_CLOSURE_PROPERTY_NAME()] = f;
      f.$dart_jsFunction = ret;
      return ret;
    },
    _callDartFunctionFast(callback, $arguments) {
      type$.List_dynamic._as($arguments);
      type$.Function._as(callback);
      return A.Primitives_applyFunction(callback, $arguments, null);
    },
    allowInterop(f, $F) {
      if (typeof f == "function")
        return f;
      else
        return $F._as(A._convertDartFunctionFast(f));
    },
    IterableExtension_firstWhereOrNull(_this, test, $T) {
      var t1, _i, element;
      for (t1 = _this.length, _i = 0; _i < _this.length; _this.length === t1 || (0, A.throwConcurrentModificationError)(_this), ++_i) {
        element = _this[_i];
        if (A.boolConversionCheck(test.call$1(element)))
          return element;
      }
      return null;
    },
    getAlgoOptions(algorithmName, salt) {
      var t1;
      switch (algorithmName) {
        case "HKDF":
          t1 = A.jsArrayBufferFrom(salt);
          return A.LinkedHashMap_LinkedHashMap$_literal(["name", "HKDF", "salt", t1, "hash", "SHA-256", "info", A.jsArrayBufferFrom(new Uint8Array(128))], type$.String, type$.Object);
        case "PBKDF2":
          return A.LinkedHashMap_LinkedHashMap$_literal(["name", "PBKDF2", "salt", A.jsArrayBufferFrom(salt), "hash", "SHA-256", "iterations", 100000], type$.String, type$.Object);
        default:
          throw A.wrapException(A.Exception_Exception("algorithm " + algorithmName + " is currently unsupported"));
      }
    }
  },
  J = {
    makeDispatchRecord(interceptor, proto, extension, indexability) {
      return {i: interceptor, p: proto, e: extension, x: indexability};
    },
    getNativeInterceptor(object) {
      var proto, objectProto, $constructor, interceptor, t1,
        record = object[init.dispatchPropertyName];
      if (record == null)
        if ($.initNativeDispatchFlag == null) {
          A.initNativeDispatch();
          record = object[init.dispatchPropertyName];
        }
      if (record != null) {
        proto = record.p;
        if (false === proto)
          return record.i;
        if (true === proto)
          return object;
        objectProto = Object.getPrototypeOf(object);
        if (proto === objectProto)
          return record.i;
        if (record.e === objectProto)
          throw A.wrapException(A.UnimplementedError$("Return interceptor for " + A.S(proto(object, record))));
      }
      $constructor = object.constructor;
      if ($constructor == null)
        interceptor = null;
      else {
        t1 = $._JS_INTEROP_INTERCEPTOR_TAG;
        if (t1 == null)
          t1 = $._JS_INTEROP_INTERCEPTOR_TAG = init.getIsolateTag("_$dart_js");
        interceptor = $constructor[t1];
      }
      if (interceptor != null)
        return interceptor;
      interceptor = A.lookupAndCacheInterceptor(object);
      if (interceptor != null)
        return interceptor;
      if (typeof object == "function")
        return B.JavaScriptFunction_methods;
      proto = Object.getPrototypeOf(object);
      if (proto == null)
        return B.PlainJavaScriptObject_methods;
      if (proto === Object.prototype)
        return B.PlainJavaScriptObject_methods;
      if (typeof $constructor == "function") {
        t1 = $._JS_INTEROP_INTERCEPTOR_TAG;
        if (t1 == null)
          t1 = $._JS_INTEROP_INTERCEPTOR_TAG = init.getIsolateTag("_$dart_js");
        Object.defineProperty($constructor, t1, {value: B.UnknownJavaScriptObject_methods, enumerable: false, writable: true, configurable: true});
        return B.UnknownJavaScriptObject_methods;
      }
      return B.UnknownJavaScriptObject_methods;
    },
    JSArray_JSArray$fixed($length, $E) {
      if ($length < 0 || $length > 4294967295)
        throw A.wrapException(A.RangeError$range($length, 0, 4294967295, "length", null));
      return J.JSArray_JSArray$markFixed(new Array($length), $E);
    },
    JSArray_JSArray$markFixed(allocation, $E) {
      return J.JSArray_markFixedList(A._setArrayType(allocation, $E._eval$1("JSArray<0>")), $E);
    },
    JSArray_markFixedList(list, $T) {
      list.fixed$length = Array;
      return list;
    },
    getInterceptor$(receiver) {
      if (typeof receiver == "number") {
        if (Math.floor(receiver) == receiver)
          return J.JSInt.prototype;
        return J.JSNumNotInt.prototype;
      }
      if (typeof receiver == "string")
        return J.JSString.prototype;
      if (receiver == null)
        return J.JSNull.prototype;
      if (typeof receiver == "boolean")
        return J.JSBool.prototype;
      if (receiver.constructor == Array)
        return J.JSArray.prototype;
      if (typeof receiver != "object") {
        if (typeof receiver == "function")
          return J.JavaScriptFunction.prototype;
        return receiver;
      }
      if (receiver instanceof A.Object)
        return receiver;
      return J.getNativeInterceptor(receiver);
    },
    getInterceptor$asx(receiver) {
      if (typeof receiver == "string")
        return J.JSString.prototype;
      if (receiver == null)
        return receiver;
      if (receiver.constructor == Array)
        return J.JSArray.prototype;
      if (typeof receiver != "object") {
        if (typeof receiver == "function")
          return J.JavaScriptFunction.prototype;
        return receiver;
      }
      if (receiver instanceof A.Object)
        return receiver;
      return J.getNativeInterceptor(receiver);
    },
    getInterceptor$ax(receiver) {
      if (receiver == null)
        return receiver;
      if (receiver.constructor == Array)
        return J.JSArray.prototype;
      if (typeof receiver != "object") {
        if (typeof receiver == "function")
          return J.JavaScriptFunction.prototype;
        return receiver;
      }
      if (receiver instanceof A.Object)
        return receiver;
      return J.getNativeInterceptor(receiver);
    },
    getInterceptor$x(receiver) {
      if (receiver == null)
        return receiver;
      if (typeof receiver != "object") {
        if (typeof receiver == "function")
          return J.JavaScriptFunction.prototype;
        return receiver;
      }
      if (receiver instanceof A.Object)
        return receiver;
      return J.getNativeInterceptor(receiver);
    },
    get$data$x(receiver) {
      return J.getInterceptor$x(receiver).get$data(receiver);
    },
    get$hashCode$(receiver) {
      return J.getInterceptor$(receiver).get$hashCode(receiver);
    },
    get$iterator$ax(receiver) {
      return J.getInterceptor$ax(receiver).get$iterator(receiver);
    },
    get$length$asx(receiver) {
      return J.getInterceptor$asx(receiver).get$length(receiver);
    },
    get$name$x(receiver) {
      return J.getInterceptor$x(receiver).get$name(receiver);
    },
    get$synchronizationSource$x(receiver) {
      return J.getInterceptor$x(receiver).get$synchronizationSource(receiver);
    },
    get$type$x(receiver) {
      return J.getInterceptor$x(receiver).get$type(receiver);
    },
    $eq$(receiver, a0) {
      if (receiver == null)
        return a0 == null;
      if (typeof receiver != "object")
        return a0 != null && receiver === a0;
      return J.getInterceptor$(receiver).$eq(receiver, a0);
    },
    $index$asx(receiver, a0) {
      if (typeof a0 === "number")
        if (receiver.constructor == Array || typeof receiver == "string" || A.isJsIndexable(receiver, receiver[init.dispatchPropertyName]))
          if (a0 >>> 0 === a0 && a0 < receiver.length)
            return receiver[a0];
      return J.getInterceptor$asx(receiver).$index(receiver, a0);
    },
    add$1$ax(receiver, a0) {
      return J.getInterceptor$ax(receiver).add$1(receiver, a0);
    },
    elementAt$1$ax(receiver, a0) {
      return J.getInterceptor$ax(receiver).elementAt$1(receiver, a0);
    },
    enqueue$1$x(receiver, a0) {
      return J.getInterceptor$x(receiver).enqueue$1(receiver, a0);
    },
    forEach$1$x(receiver, a0) {
      return J.getInterceptor$x(receiver).forEach$1(receiver, a0);
    },
    map$1$1$ax(receiver, a0, $T1) {
      return J.getInterceptor$ax(receiver).map$1$1(receiver, a0, $T1);
    },
    noSuchMethod$1$(receiver, a0) {
      return J.getInterceptor$(receiver).noSuchMethod$1(receiver, a0);
    },
    pipeThrough$1$x(receiver, a0) {
      return J.getInterceptor$x(receiver).pipeThrough$1(receiver, a0);
    },
    pipeTo$1$x(receiver, a0) {
      return J.getInterceptor$x(receiver).pipeTo$1(receiver, a0);
    },
    postMessage$1$x(receiver, a0) {
      return J.getInterceptor$x(receiver).postMessage$1(receiver, a0);
    },
    setInt8$2$x(receiver, a0, a1) {
      return J.getInterceptor$x(receiver).setInt8$2(receiver, a0, a1);
    },
    skip$1$ax(receiver, a0) {
      return J.getInterceptor$ax(receiver).skip$1(receiver, a0);
    },
    sublist$1$x(receiver, a0) {
      return J.getInterceptor$x(receiver).sublist$1(receiver, a0);
    },
    sublist$2$x(receiver, a0, a1) {
      return J.getInterceptor$x(receiver).sublist$2(receiver, a0, a1);
    },
    toString$0$(receiver) {
      return J.getInterceptor$(receiver).toString$0(receiver);
    },
    Interceptor: function Interceptor() {
    },
    JSBool: function JSBool() {
    },
    JSNull: function JSNull() {
    },
    JavaScriptObject: function JavaScriptObject() {
    },
    LegacyJavaScriptObject: function LegacyJavaScriptObject() {
    },
    PlainJavaScriptObject: function PlainJavaScriptObject() {
    },
    UnknownJavaScriptObject: function UnknownJavaScriptObject() {
    },
    JavaScriptFunction: function JavaScriptFunction() {
    },
    JSArray: function JSArray(t0) {
      this.$ti = t0;
    },
    JSUnmodifiableArray: function JSUnmodifiableArray(t0) {
      this.$ti = t0;
    },
    ArrayIterator: function ArrayIterator(t0, t1, t2) {
      var _ = this;
      _._iterable = t0;
      _.__interceptors$_length = t1;
      _._index = 0;
      _._current = null;
      _.$ti = t2;
    },
    JSNumber: function JSNumber() {
    },
    JSInt: function JSInt() {
    },
    JSNumNotInt: function JSNumNotInt() {
    },
    JSString: function JSString() {
    }
  },
  B = {};
  var holders = [A, J, B];
  var $ = {};
  A.JS_CONST.prototype = {};
  J.Interceptor.prototype = {
    $eq(receiver, other) {
      return receiver === other;
    },
    get$hashCode(receiver) {
      return A.Primitives_objectHashCode(receiver);
    },
    toString$0(receiver) {
      return "Instance of '" + A.Primitives_objectTypeName(receiver) + "'";
    },
    noSuchMethod$1(receiver, invocation) {
      type$.Invocation._as(invocation);
      throw A.wrapException(new A.NoSuchMethodError(receiver, invocation.get$memberName(), invocation.get$positionalArguments(), invocation.get$namedArguments(), null));
    }
  };
  J.JSBool.prototype = {
    toString$0(receiver) {
      return String(receiver);
    },
    get$hashCode(receiver) {
      return receiver ? 519018 : 218159;
    },
    $isbool: 1
  };
  J.JSNull.prototype = {
    $eq(receiver, other) {
      return null == other;
    },
    toString$0(receiver) {
      return "null";
    },
    get$hashCode(receiver) {
      return 0;
    },
    $isNull: 1
  };
  J.JavaScriptObject.prototype = {};
  J.LegacyJavaScriptObject.prototype = {
    get$hashCode(receiver) {
      return 0;
    },
    toString$0(receiver) {
      return String(receiver);
    },
    $isJSObject: 1,
    $isWritableStream: 1,
    $isReadableStream: 1,
    $isTransformStreamDefaultController: 1,
    $isRTCEncodedFrame: 1,
    $isRTCTransformEvent: 1,
    $isAlgorithm: 1,
    pipeThrough$1(receiver, p0) {
      return receiver.pipeThrough(p0);
    },
    pipeTo$1(receiver, p0) {
      return receiver.pipeTo(p0);
    },
    enqueue$1(receiver, p0) {
      return receiver.enqueue(p0);
    },
    get$timestamp(obj) {
      return obj.timestamp;
    },
    get$data(obj) {
      return obj.data;
    },
    set$data(obj, v) {
      return obj.data = v;
    },
    getMetadata$0(receiver) {
      return receiver.getMetadata();
    },
    get$type(obj) {
      return obj.type;
    },
    get$synchronizationSource(obj) {
      return obj.synchronizationSource;
    },
    get$name(obj) {
      return obj.name;
    },
    get$msgType(obj) {
      return obj.msgType;
    },
    get$kind(obj) {
      return obj.kind;
    },
    get$participantId(obj) {
      return obj.participantId;
    },
    get$trackId(obj) {
      return obj.trackId;
    },
    get$codec(obj) {
      return obj.codec;
    }
  };
  J.PlainJavaScriptObject.prototype = {};
  J.UnknownJavaScriptObject.prototype = {};
  J.JavaScriptFunction.prototype = {
    toString$0(receiver) {
      var dartClosure = receiver[$.$get$DART_CLOSURE_PROPERTY_NAME()];
      if (dartClosure == null)
        return this.super$LegacyJavaScriptObject$toString(receiver);
      return "JavaScript function for " + J.toString$0$(dartClosure);
    },
    $isFunction: 1
  };
  J.JSArray.prototype = {
    add$1(receiver, value) {
      A._arrayInstanceType(receiver)._precomputed1._as(value);
      if (!!receiver.fixed$length)
        A.throwExpression(A.UnsupportedError$("add"));
      receiver.push(value);
    },
    _removeWhere$2(receiver, test, removeMatching) {
      var retained, end, i, element, t1;
      A._arrayInstanceType(receiver)._eval$1("bool(1)")._as(test);
      retained = [];
      end = receiver.length;
      for (i = 0; i < end; ++i) {
        element = receiver[i];
        if (!A.boolConversionCheck(test.call$1(element)))
          retained.push(element);
        if (receiver.length !== end)
          throw A.wrapException(A.ConcurrentModificationError$(receiver));
      }
      t1 = retained.length;
      if (t1 === end)
        return;
      this.set$length(receiver, t1);
      for (i = 0; i < retained.length; ++i)
        receiver[i] = retained[i];
    },
    addAll$1(receiver, collection) {
      var t1;
      A._arrayInstanceType(receiver)._eval$1("Iterable<1>")._as(collection);
      if (!!receiver.fixed$length)
        A.throwExpression(A.UnsupportedError$("addAll"));
      if (Array.isArray(collection)) {
        this._addAllFromArray$1(receiver, collection);
        return;
      }
      for (t1 = J.get$iterator$ax(collection); t1.moveNext$0();)
        receiver.push(t1.get$current(t1));
    },
    _addAllFromArray$1(receiver, array) {
      var len, i;
      type$.JSArray_dynamic._as(array);
      len = array.length;
      if (len === 0)
        return;
      if (receiver === array)
        throw A.wrapException(A.ConcurrentModificationError$(receiver));
      for (i = 0; i < len; ++i)
        receiver.push(array[i]);
    },
    map$1$1(receiver, f, $T) {
      var t1 = A._arrayInstanceType(receiver);
      return new A.MappedListIterable(receiver, t1._bind$1($T)._eval$1("1(2)")._as(f), t1._eval$1("@<1>")._bind$1($T)._eval$1("MappedListIterable<1,2>"));
    },
    skip$1(receiver, n) {
      return A.SubListIterable$(receiver, n, null, A._arrayInstanceType(receiver)._precomputed1);
    },
    elementAt$1(receiver, index) {
      if (!(index >= 0 && index < receiver.length))
        return A.ioore(receiver, index);
      return receiver[index];
    },
    toString$0(receiver) {
      return A.IterableBase_iterableToFullString(receiver, "[", "]");
    },
    get$iterator(receiver) {
      return new J.ArrayIterator(receiver, receiver.length, A._arrayInstanceType(receiver)._eval$1("ArrayIterator<1>"));
    },
    get$hashCode(receiver) {
      return A.Primitives_objectHashCode(receiver);
    },
    get$length(receiver) {
      return receiver.length;
    },
    set$length(receiver, newLength) {
      if (!!receiver.fixed$length)
        A.throwExpression(A.UnsupportedError$("set length"));
      if (newLength > receiver.length)
        A._arrayInstanceType(receiver)._precomputed1._as(null);
      receiver.length = newLength;
    },
    $index(receiver, index) {
      A._asInt(index);
      if (!(index >= 0 && index < receiver.length))
        throw A.wrapException(A.diagnoseIndexError(receiver, index));
      return receiver[index];
    },
    $indexSet(receiver, index, value) {
      A._arrayInstanceType(receiver)._precomputed1._as(value);
      if (!!receiver.immutable$list)
        A.throwExpression(A.UnsupportedError$("indexed set"));
      if (!(index >= 0 && index < receiver.length))
        throw A.wrapException(A.diagnoseIndexError(receiver, index));
      receiver[index] = value;
    },
    $isJSIndexable: 1,
    $isEfficientLengthIterable: 1,
    $isIterable: 1,
    $isList: 1
  };
  J.JSUnmodifiableArray.prototype = {};
  J.ArrayIterator.prototype = {
    get$current(_) {
      var t1 = this._current;
      return t1 == null ? this.$ti._precomputed1._as(t1) : t1;
    },
    moveNext$0() {
      var t2, _this = this,
        t1 = _this._iterable,
        $length = t1.length;
      if (_this.__interceptors$_length !== $length)
        throw A.wrapException(A.throwConcurrentModificationError(t1));
      t2 = _this._index;
      if (t2 >= $length) {
        _this.set$_current(null);
        return false;
      }
      _this.set$_current(t1[t2]);
      ++_this._index;
      return true;
    },
    set$_current(_current) {
      this._current = this.$ti._eval$1("1?")._as(_current);
    },
    $isIterator: 1
  };
  J.JSNumber.prototype = {
    toRadixString$1(receiver, radix) {
      var result, match, t1, exponent;
      if (radix < 2 || radix > 36)
        throw A.wrapException(A.RangeError$range(radix, 2, 36, "radix", null));
      result = receiver.toString(radix);
      if (B.JSString_methods.codeUnitAt$1(result, result.length - 1) !== 41)
        return result;
      match = /^([\da-z]+)(?:\.([\da-z]+))?\(e\+(\d+)\)$/.exec(result);
      if (match == null)
        A.throwExpression(A.UnsupportedError$("Unexpected toString result: " + result));
      t1 = match.length;
      if (1 >= t1)
        return A.ioore(match, 1);
      result = match[1];
      if (3 >= t1)
        return A.ioore(match, 3);
      exponent = +match[3];
      t1 = match[2];
      if (t1 != null) {
        result += t1;
        exponent -= t1.length;
      }
      return result + B.JSString_methods.$mul("0", exponent);
    },
    toString$0(receiver) {
      if (receiver === 0 && 1 / receiver < 0)
        return "-0.0";
      else
        return "" + receiver;
    },
    get$hashCode(receiver) {
      var absolute, floorLog2, factor, scaled,
        intValue = receiver | 0;
      if (receiver === intValue)
        return intValue & 536870911;
      absolute = Math.abs(receiver);
      floorLog2 = Math.log(absolute) / 0.6931471805599453 | 0;
      factor = Math.pow(2, floorLog2);
      scaled = absolute < 1 ? absolute / factor : factor / absolute;
      return ((scaled * 9007199254740992 | 0) + (scaled * 3542243181176521 | 0)) * 599197 + floorLog2 * 1259 & 536870911;
    },
    $mod(receiver, other) {
      var result = receiver % other;
      if (result === 0)
        return 0;
      if (result > 0)
        return result;
      return result + other;
    },
    _tdivFast$1(receiver, other) {
      return (receiver | 0) === receiver ? receiver / other | 0 : this._tdivSlow$1(receiver, other);
    },
    _tdivSlow$1(receiver, other) {
      var quotient = receiver / other;
      if (quotient >= -2147483648 && quotient <= 2147483647)
        return quotient | 0;
      if (quotient > 0) {
        if (quotient !== 1 / 0)
          return Math.floor(quotient);
      } else if (quotient > -1 / 0)
        return Math.ceil(quotient);
      throw A.wrapException(A.UnsupportedError$("Result of truncating division is " + A.S(quotient) + ": " + A.S(receiver) + " ~/ " + other));
    },
    _shrOtherPositive$1(receiver, other) {
      var t1;
      if (receiver > 0)
        t1 = this._shrBothPositive$1(receiver, other);
      else {
        t1 = other > 31 ? 31 : other;
        t1 = receiver >> t1 >>> 0;
      }
      return t1;
    },
    _shrBothPositive$1(receiver, other) {
      return other > 31 ? 0 : receiver >>> other;
    },
    $isdouble: 1,
    $isnum: 1
  };
  J.JSInt.prototype = {$isint: 1};
  J.JSNumNotInt.prototype = {};
  J.JSString.prototype = {
    codeUnitAt$1(receiver, index) {
      if (index < 0)
        throw A.wrapException(A.diagnoseIndexError(receiver, index));
      if (index >= receiver.length)
        A.throwExpression(A.diagnoseIndexError(receiver, index));
      return receiver.charCodeAt(index);
    },
    _codeUnitAt$1(receiver, index) {
      if (index >= receiver.length)
        throw A.wrapException(A.diagnoseIndexError(receiver, index));
      return receiver.charCodeAt(index);
    },
    $add(receiver, other) {
      return receiver + other;
    },
    substring$2(receiver, start, end) {
      return receiver.substring(start, A.RangeError_checkValidRange(start, end, receiver.length));
    },
    $mul(receiver, times) {
      var s, result;
      if (0 >= times)
        return "";
      if (times === 1 || receiver.length === 0)
        return receiver;
      if (times !== times >>> 0)
        throw A.wrapException(B.C_OutOfMemoryError);
      for (s = receiver, result = ""; true;) {
        if ((times & 1) === 1)
          result = s + result;
        times = times >>> 1;
        if (times === 0)
          break;
        s += s;
      }
      return result;
    },
    toString$0(receiver) {
      return receiver;
    },
    get$hashCode(receiver) {
      var t1, hash, i;
      for (t1 = receiver.length, hash = 0, i = 0; i < t1; ++i) {
        hash = hash + receiver.charCodeAt(i) & 536870911;
        hash = hash + ((hash & 524287) << 10) & 536870911;
        hash ^= hash >> 6;
      }
      hash = hash + ((hash & 67108863) << 3) & 536870911;
      hash ^= hash >> 11;
      return hash + ((hash & 16383) << 15) & 536870911;
    },
    get$length(receiver) {
      return receiver.length;
    },
    $index(receiver, index) {
      A._asInt(index);
      if (!(index.$ge(0, 0) && index.$lt(0, receiver.length)))
        throw A.wrapException(A.diagnoseIndexError(receiver, index));
      return receiver[index];
    },
    $isJSIndexable: 1,
    $isString: 1
  };
  A._CopyingBytesBuilder.prototype = {
    add$1(_, bytes) {
      var byteCount, required, t1, t2, newSize, x, newBuffer, _this = this;
      type$.List_int._as(bytes);
      byteCount = bytes.length;
      if (byteCount === 0)
        return;
      required = _this.__internal$_length + byteCount;
      t1 = _this._buffer;
      t2 = t1.length;
      if (t2 < required) {
        newSize = required * 2;
        if (newSize < 1024)
          newSize = 1024;
        else {
          x = newSize - 1;
          x |= B.JSInt_methods._shrOtherPositive$1(x, 1);
          x |= x >>> 2;
          x |= x >>> 4;
          x |= x >>> 8;
          newSize = ((x | x >>> 16) >>> 0) + 1;
        }
        newBuffer = new Uint8Array(newSize);
        B.NativeUint8List_methods.setRange$3(newBuffer, 0, t2, t1);
        _this._buffer = newBuffer;
        t1 = newBuffer;
      }
      B.NativeUint8List_methods.setRange$3(t1, _this.__internal$_length, required, bytes);
      _this.__internal$_length = required;
    },
    toBytes$0() {
      var t2,
        t1 = this.__internal$_length;
      if (t1 === 0)
        return $.$get$_CopyingBytesBuilder__emptyList();
      t2 = this._buffer;
      return new Uint8Array(A._ensureNativeList(A.NativeUint8List_NativeUint8List$view(t2.buffer, t2.byteOffset, t1)));
    },
    get$length(_) {
      return this.__internal$_length;
    }
  };
  A.LateError.prototype = {
    toString$0(_) {
      return "LateInitializationError: " + this._message;
    }
  };
  A.CodeUnits.prototype = {
    get$length(_) {
      return this._string.length;
    },
    $index(_, i) {
      return B.JSString_methods.codeUnitAt$1(this._string, A._asInt(i));
    }
  };
  A.SentinelValue.prototype = {};
  A.EfficientLengthIterable.prototype = {};
  A.ListIterable.prototype = {
    get$iterator(_) {
      var _this = this;
      return new A.ListIterator(_this, _this.get$length(_this), A._instanceType(_this)._eval$1("ListIterator<ListIterable.E>"));
    },
    map$1$1(_, toElement, $T) {
      var t1 = A._instanceType(this);
      return new A.MappedListIterable(this, t1._bind$1($T)._eval$1("1(ListIterable.E)")._as(toElement), t1._eval$1("@<ListIterable.E>")._bind$1($T)._eval$1("MappedListIterable<1,2>"));
    }
  };
  A.SubListIterable.prototype = {
    get$_endIndex() {
      var $length = J.get$length$asx(this.__internal$_iterable),
        endOrLength = this._endOrLength;
      if (endOrLength == null || endOrLength > $length)
        return $length;
      return endOrLength;
    },
    get$_startIndex() {
      var $length = J.get$length$asx(this.__internal$_iterable),
        t1 = this._start;
      if (t1 > $length)
        return $length;
      return t1;
    },
    get$length(_) {
      var endOrLength,
        $length = J.get$length$asx(this.__internal$_iterable),
        t1 = this._start;
      if (t1 >= $length)
        return 0;
      endOrLength = this._endOrLength;
      if (endOrLength == null || endOrLength >= $length)
        return $length - t1;
      if (typeof endOrLength !== "number")
        return endOrLength.$sub();
      return endOrLength - t1;
    },
    elementAt$1(_, index) {
      var _this = this,
        realIndex = _this.get$_startIndex() + index;
      if (index < 0 || realIndex >= _this.get$_endIndex())
        throw A.wrapException(A.IndexError$withLength(index, _this.get$length(_this), _this, "index"));
      return J.elementAt$1$ax(_this.__internal$_iterable, realIndex);
    },
    toList$1$growable(_, growable) {
      var $length, result, i, _this = this,
        start = _this._start,
        t1 = _this.__internal$_iterable,
        t2 = J.getInterceptor$asx(t1),
        end = t2.get$length(t1),
        endOrLength = _this._endOrLength;
      if (endOrLength != null && endOrLength < end)
        end = endOrLength;
      $length = end - start;
      if ($length <= 0) {
        t1 = J.JSArray_JSArray$fixed(0, _this.$ti._precomputed1);
        return t1;
      }
      result = A.List_List$filled($length, t2.elementAt$1(t1, start), false, _this.$ti._precomputed1);
      for (i = 1; i < $length; ++i) {
        B.JSArray_methods.$indexSet(result, i, t2.elementAt$1(t1, start + i));
        if (t2.get$length(t1) < end)
          throw A.wrapException(A.ConcurrentModificationError$(_this));
      }
      return result;
    }
  };
  A.ListIterator.prototype = {
    get$current(_) {
      var t1 = this.__internal$_current;
      return t1 == null ? this.$ti._precomputed1._as(t1) : t1;
    },
    moveNext$0() {
      var t3, _this = this,
        t1 = _this.__internal$_iterable,
        t2 = J.getInterceptor$asx(t1),
        $length = t2.get$length(t1);
      if (_this.__internal$_length !== $length)
        throw A.wrapException(A.ConcurrentModificationError$(t1));
      t3 = _this.__internal$_index;
      if (t3 >= $length) {
        _this.set$__internal$_current(null);
        return false;
      }
      _this.set$__internal$_current(t2.elementAt$1(t1, t3));
      ++_this.__internal$_index;
      return true;
    },
    set$__internal$_current(_current) {
      this.__internal$_current = this.$ti._eval$1("1?")._as(_current);
    },
    $isIterator: 1
  };
  A.MappedIterable.prototype = {
    get$iterator(_) {
      var t1 = this.__internal$_iterable,
        t2 = A._instanceType(this);
      return new A.MappedIterator(t1.get$iterator(t1), this._f, t2._eval$1("@<1>")._bind$1(t2._rest[1])._eval$1("MappedIterator<1,2>"));
    },
    get$length(_) {
      var t1 = this.__internal$_iterable;
      return t1.get$length(t1);
    }
  };
  A.EfficientLengthMappedIterable.prototype = {$isEfficientLengthIterable: 1};
  A.MappedIterator.prototype = {
    moveNext$0() {
      var _this = this,
        t1 = _this._iterator;
      if (t1.moveNext$0()) {
        _this.set$__internal$_current(_this._f.call$1(t1.get$current(t1)));
        return true;
      }
      _this.set$__internal$_current(null);
      return false;
    },
    get$current(_) {
      var t1 = this.__internal$_current;
      return t1 == null ? this.$ti._rest[1]._as(t1) : t1;
    },
    set$__internal$_current(_current) {
      this.__internal$_current = this.$ti._eval$1("2?")._as(_current);
    }
  };
  A.MappedListIterable.prototype = {
    get$length(_) {
      return J.get$length$asx(this._source);
    },
    elementAt$1(_, index) {
      return this._f.call$1(J.elementAt$1$ax(this._source, index));
    }
  };
  A.WhereIterable.prototype = {
    get$iterator(_) {
      return new A.WhereIterator(J.get$iterator$ax(this.__internal$_iterable), this._f, this.$ti._eval$1("WhereIterator<1>"));
    },
    map$1$1(_, toElement, $T) {
      var t1 = this.$ti;
      return new A.MappedIterable(this, t1._bind$1($T)._eval$1("1(2)")._as(toElement), t1._eval$1("@<1>")._bind$1($T)._eval$1("MappedIterable<1,2>"));
    }
  };
  A.WhereIterator.prototype = {
    moveNext$0() {
      var t1, t2;
      for (t1 = this._iterator, t2 = this._f; t1.moveNext$0();)
        if (A.boolConversionCheck(t2.call$1(t1.get$current(t1))))
          return true;
      return false;
    },
    get$current(_) {
      var t1 = this._iterator;
      return t1.get$current(t1);
    }
  };
  A.FixedLengthListMixin.prototype = {};
  A.UnmodifiableListMixin.prototype = {
    $indexSet(_, index, value) {
      A._instanceType(this)._eval$1("UnmodifiableListMixin.E")._as(value);
      throw A.wrapException(A.UnsupportedError$("Cannot modify an unmodifiable list"));
    }
  };
  A.UnmodifiableListBase.prototype = {};
  A.Symbol.prototype = {
    get$hashCode(_) {
      var hash = this._hashCode;
      if (hash != null)
        return hash;
      hash = 664597 * J.get$hashCode$(this.__internal$_name) & 536870911;
      this._hashCode = hash;
      return hash;
    },
    toString$0(_) {
      return 'Symbol("' + A.S(this.__internal$_name) + '")';
    },
    $eq(_, other) {
      if (other == null)
        return false;
      return other instanceof A.Symbol && this.__internal$_name == other.__internal$_name;
    },
    $isSymbol0: 1
  };
  A.ConstantMapView.prototype = {};
  A.ConstantMap.prototype = {
    toString$0(_) {
      return A.MapBase_mapToString(this);
    },
    $isMap: 1
  };
  A.ConstantStringMap.prototype = {
    get$length(_) {
      return this._length;
    },
    containsKey$1(_, key) {
      if (typeof key != "string")
        return false;
      if ("__proto__" === key)
        return false;
      return this._jsObject.hasOwnProperty(key);
    },
    $index(_, key) {
      if (!this.containsKey$1(0, key))
        return null;
      return this._jsObject[A._asString(key)];
    },
    forEach$1(_, f) {
      var keys, t2, t3, i, t4,
        t1 = this.$ti;
      t1._eval$1("~(1,2)")._as(f);
      keys = this.__js_helper$_keys;
      for (t2 = keys.length, t3 = this._jsObject, t1 = t1._rest[1], i = 0; i < t2; ++i) {
        t4 = A._asString(keys[i]);
        f.call$2(t4, t1._as(t3[t4]));
      }
    },
    get$keys(_) {
      return new A._ConstantMapKeyIterable(this, this.$ti._eval$1("_ConstantMapKeyIterable<1>"));
    }
  };
  A._ConstantMapKeyIterable.prototype = {
    get$iterator(_) {
      var t1 = this._map.__js_helper$_keys;
      return new J.ArrayIterator(t1, t1.length, A._arrayInstanceType(t1)._eval$1("ArrayIterator<1>"));
    },
    get$length(_) {
      return this._map.__js_helper$_keys.length;
    }
  };
  A.JSInvocationMirror.prototype = {
    get$memberName() {
      var t1 = this._memberName;
      return t1;
    },
    get$positionalArguments() {
      var t1, argumentCount, list, index, _this = this;
      if (_this.__js_helper$_kind === 1)
        return B.List_empty;
      t1 = _this._arguments;
      argumentCount = t1.length - _this._namedArgumentNames.length - _this._typeArgumentCount;
      if (argumentCount === 0)
        return B.List_empty;
      list = [];
      for (index = 0; index < argumentCount; ++index) {
        if (!(index < t1.length))
          return A.ioore(t1, index);
        list.push(t1[index]);
      }
      list.fixed$length = Array;
      list.immutable$list = Array;
      return list;
    },
    get$namedArguments() {
      var t1, namedArgumentCount, t2, namedArgumentsStartIndex, map, i, t3, t4, _this = this;
      if (_this.__js_helper$_kind !== 0)
        return B.Map_empty;
      t1 = _this._namedArgumentNames;
      namedArgumentCount = t1.length;
      t2 = _this._arguments;
      namedArgumentsStartIndex = t2.length - namedArgumentCount - _this._typeArgumentCount;
      if (namedArgumentCount === 0)
        return B.Map_empty;
      map = new A.JsLinkedHashMap(type$.JsLinkedHashMap_Symbol_dynamic);
      for (i = 0; i < namedArgumentCount; ++i) {
        if (!(i < t1.length))
          return A.ioore(t1, i);
        t3 = t1[i];
        t4 = namedArgumentsStartIndex + i;
        if (!(t4 >= 0 && t4 < t2.length))
          return A.ioore(t2, t4);
        map.$indexSet(0, new A.Symbol(t3), t2[t4]);
      }
      return new A.ConstantMapView(map, type$.ConstantMapView_Symbol_dynamic);
    },
    $isInvocation: 1
  };
  A.Primitives_functionNoSuchMethod_closure.prototype = {
    call$2($name, argument) {
      var t1;
      A._asString($name);
      t1 = this._box_0;
      t1.names = t1.names + "$" + $name;
      B.JSArray_methods.add$1(this.namedArgumentList, $name);
      B.JSArray_methods.add$1(this.$arguments, argument);
      ++t1.argumentCount;
    },
    $signature: 2
  };
  A.TypeErrorDecoder.prototype = {
    matchTypeError$1(message) {
      var result, t1, _this = this,
        match = new RegExp(_this._pattern).exec(message);
      if (match == null)
        return null;
      result = Object.create(null);
      t1 = _this._arguments;
      if (t1 !== -1)
        result.arguments = match[t1 + 1];
      t1 = _this._argumentsExpr;
      if (t1 !== -1)
        result.argumentsExpr = match[t1 + 1];
      t1 = _this._expr;
      if (t1 !== -1)
        result.expr = match[t1 + 1];
      t1 = _this._method;
      if (t1 !== -1)
        result.method = match[t1 + 1];
      t1 = _this._receiver;
      if (t1 !== -1)
        result.receiver = match[t1 + 1];
      return result;
    }
  };
  A.NullError.prototype = {
    toString$0(_) {
      var t1 = this._method;
      if (t1 == null)
        return "NoSuchMethodError: " + this.__js_helper$_message;
      return "NoSuchMethodError: method not found: '" + t1 + "' on null";
    }
  };
  A.JsNoSuchMethodError.prototype = {
    toString$0(_) {
      var t2, _this = this,
        _s38_ = "NoSuchMethodError: method not found: '",
        t1 = _this._method;
      if (t1 == null)
        return "NoSuchMethodError: " + _this.__js_helper$_message;
      t2 = _this._receiver;
      if (t2 == null)
        return _s38_ + t1 + "' (" + _this.__js_helper$_message + ")";
      return _s38_ + t1 + "' on '" + t2 + "' (" + _this.__js_helper$_message + ")";
    }
  };
  A.UnknownJsTypeError.prototype = {
    toString$0(_) {
      var t1 = this.__js_helper$_message;
      return t1.length === 0 ? "Error" : "Error: " + t1;
    }
  };
  A.NullThrownFromJavaScriptException.prototype = {
    toString$0(_) {
      return "Throw of null ('" + (this._irritant === null ? "null" : "undefined") + "' from JavaScript)";
    }
  };
  A.ExceptionAndStackTrace.prototype = {};
  A._StackTrace.prototype = {
    toString$0(_) {
      var trace,
        t1 = this._trace;
      if (t1 != null)
        return t1;
      t1 = this._exception;
      trace = t1 !== null && typeof t1 === "object" ? t1.stack : null;
      return this._trace = trace == null ? "" : trace;
    },
    $isStackTrace: 1
  };
  A.Closure.prototype = {
    toString$0(_) {
      var $constructor = this.constructor,
        $name = $constructor == null ? null : $constructor.name;
      return "Closure '" + A.unminifyOrTag($name == null ? "unknown" : $name) + "'";
    },
    $isFunction: 1,
    get$$call() {
      return this;
    },
    "call*": "call$1",
    $requiredArgCount: 1,
    $defaultValues: null
  };
  A.Closure0Args.prototype = {"call*": "call$0", $requiredArgCount: 0};
  A.Closure2Args.prototype = {"call*": "call$2", $requiredArgCount: 2};
  A.TearOffClosure.prototype = {};
  A.StaticClosure.prototype = {
    toString$0(_) {
      var $name = this.$static_name;
      if ($name == null)
        return "Closure of unknown static method";
      return "Closure '" + A.unminifyOrTag($name) + "'";
    }
  };
  A.BoundClosure.prototype = {
    $eq(_, other) {
      if (other == null)
        return false;
      if (this === other)
        return true;
      if (!(other instanceof A.BoundClosure))
        return false;
      return this.$_target === other.$_target && this._receiver === other._receiver;
    },
    get$hashCode(_) {
      return (A.objectHashCode(this._receiver) ^ A.Primitives_objectHashCode(this.$_target)) >>> 0;
    },
    toString$0(_) {
      return "Closure '" + this.$_name + "' of " + ("Instance of '" + A.Primitives_objectTypeName(this._receiver) + "'");
    }
  };
  A.RuntimeError.prototype = {
    toString$0(_) {
      return "RuntimeError: " + this.message;
    }
  };
  A._AssertionError.prototype = {
    toString$0(_) {
      return "Assertion failed: " + A.Error_safeToString(this.message);
    }
  };
  A._Required.prototype = {};
  A.JsLinkedHashMap.prototype = {
    get$length(_) {
      return this._length;
    },
    get$keys(_) {
      return new A.LinkedHashMapKeyIterable(this, A._instanceType(this)._eval$1("LinkedHashMapKeyIterable<1>"));
    },
    containsKey$1(_, key) {
      var strings = this._strings;
      if (strings == null)
        return false;
      return strings[key] != null;
    },
    $index(_, key) {
      var strings, cell, t1, nums, _null = null;
      if (typeof key == "string") {
        strings = this._strings;
        if (strings == null)
          return _null;
        cell = strings[key];
        t1 = cell == null ? _null : cell.hashMapCellValue;
        return t1;
      } else if (typeof key == "number" && (key & 0x3fffffff) === key) {
        nums = this._nums;
        if (nums == null)
          return _null;
        cell = nums[key];
        t1 = cell == null ? _null : cell.hashMapCellValue;
        return t1;
      } else
        return this.internalGet$1(key);
    },
    internalGet$1(key) {
      var bucket, index,
        rest = this.__js_helper$_rest;
      if (rest == null)
        return null;
      bucket = rest[this.internalComputeHashCode$1(key)];
      index = this.internalFindBucketIndex$2(bucket, key);
      if (index < 0)
        return null;
      return bucket[index].hashMapCellValue;
    },
    $indexSet(_, key, value) {
      var strings, nums, _this = this,
        t1 = A._instanceType(_this);
      t1._precomputed1._as(key);
      t1._rest[1]._as(value);
      if (typeof key == "string") {
        strings = _this._strings;
        _this._addHashTableEntry$3(strings == null ? _this._strings = _this._newHashTable$0() : strings, key, value);
      } else if (typeof key == "number" && (key & 0x3fffffff) === key) {
        nums = _this._nums;
        _this._addHashTableEntry$3(nums == null ? _this._nums = _this._newHashTable$0() : nums, key, value);
      } else
        _this.internalSet$2(key, value);
    },
    internalSet$2(key, value) {
      var rest, hash, bucket, index, _this = this,
        t1 = A._instanceType(_this);
      t1._precomputed1._as(key);
      t1._rest[1]._as(value);
      rest = _this.__js_helper$_rest;
      if (rest == null)
        rest = _this.__js_helper$_rest = _this._newHashTable$0();
      hash = _this.internalComputeHashCode$1(key);
      bucket = rest[hash];
      if (bucket == null)
        rest[hash] = [_this._newLinkedCell$2(key, value)];
      else {
        index = _this.internalFindBucketIndex$2(bucket, key);
        if (index >= 0)
          bucket[index].hashMapCellValue = value;
        else
          bucket.push(_this._newLinkedCell$2(key, value));
      }
    },
    forEach$1(_, action) {
      var cell, modifications, _this = this;
      A._instanceType(_this)._eval$1("~(1,2)")._as(action);
      cell = _this._first;
      modifications = _this._modifications;
      for (; cell != null;) {
        action.call$2(cell.hashMapCellKey, cell.hashMapCellValue);
        if (modifications !== _this._modifications)
          throw A.wrapException(A.ConcurrentModificationError$(_this));
        cell = cell._next;
      }
    },
    _addHashTableEntry$3(table, key, value) {
      var cell,
        t1 = A._instanceType(this);
      t1._precomputed1._as(key);
      t1._rest[1]._as(value);
      cell = table[key];
      if (cell == null)
        table[key] = this._newLinkedCell$2(key, value);
      else
        cell.hashMapCellValue = value;
    },
    _newLinkedCell$2(key, value) {
      var _this = this,
        t1 = A._instanceType(_this),
        cell = new A.LinkedHashMapCell(t1._precomputed1._as(key), t1._rest[1]._as(value));
      if (_this._first == null)
        _this._first = _this._last = cell;
      else
        _this._last = _this._last._next = cell;
      ++_this._length;
      _this._modifications = _this._modifications + 1 & 1073741823;
      return cell;
    },
    internalComputeHashCode$1(key) {
      return J.get$hashCode$(key) & 0x3fffffff;
    },
    internalFindBucketIndex$2(bucket, key) {
      var $length, i;
      if (bucket == null)
        return -1;
      $length = bucket.length;
      for (i = 0; i < $length; ++i)
        if (J.$eq$(bucket[i].hashMapCellKey, key))
          return i;
      return -1;
    },
    toString$0(_) {
      return A.MapBase_mapToString(this);
    },
    _newHashTable$0() {
      var table = Object.create(null);
      table["<non-identifier-key>"] = table;
      delete table["<non-identifier-key>"];
      return table;
    },
    $isLinkedHashMap: 1
  };
  A.LinkedHashMapCell.prototype = {};
  A.LinkedHashMapKeyIterable.prototype = {
    get$length(_) {
      return this._map._length;
    },
    get$iterator(_) {
      var t1 = this._map,
        t2 = new A.LinkedHashMapKeyIterator(t1, t1._modifications, this.$ti._eval$1("LinkedHashMapKeyIterator<1>"));
      t2._cell = t1._first;
      return t2;
    }
  };
  A.LinkedHashMapKeyIterator.prototype = {
    get$current(_) {
      return this.__js_helper$_current;
    },
    moveNext$0() {
      var cell, _this = this,
        t1 = _this._map;
      if (_this._modifications !== t1._modifications)
        throw A.wrapException(A.ConcurrentModificationError$(t1));
      cell = _this._cell;
      if (cell == null) {
        _this.set$__js_helper$_current(null);
        return false;
      } else {
        _this.set$__js_helper$_current(cell.hashMapCellKey);
        _this._cell = cell._next;
        return true;
      }
    },
    set$__js_helper$_current(_current) {
      this.__js_helper$_current = this.$ti._eval$1("1?")._as(_current);
    },
    $isIterator: 1
  };
  A.initHooks_closure.prototype = {
    call$1(o) {
      return this.getTag(o);
    },
    $signature: 8
  };
  A.initHooks_closure0.prototype = {
    call$2(o, tag) {
      return this.getUnknownTag(o, tag);
    },
    $signature: 9
  };
  A.initHooks_closure1.prototype = {
    call$1(tag) {
      return this.prototypeForTag(A._asString(tag));
    },
    $signature: 10
  };
  A.NativeByteBuffer.prototype = {$isNativeByteBuffer: 1, $isByteBuffer: 1};
  A.NativeTypedData.prototype = {
    _invalidPosition$3(receiver, position, $length, $name) {
      var t1 = A.RangeError$range(position, 0, $length, $name, null);
      throw A.wrapException(t1);
    },
    _checkPosition$3(receiver, position, $length, $name) {
      if (position >>> 0 !== position || position > $length)
        this._invalidPosition$3(receiver, position, $length, $name);
    },
    $isNativeTypedData: 1
  };
  A.NativeByteData.prototype = {
    _getUint32$2(receiver, byteOffset, littleEndian) {
      return receiver.getUint32(byteOffset, littleEndian);
    },
    setInt8$2(receiver, byteOffset, value) {
      return receiver.setInt8(byteOffset, value);
    },
    _setUint32$3(receiver, byteOffset, value, littleEndian) {
      return receiver.setUint32(byteOffset, value, littleEndian);
    },
    $isByteData: 1
  };
  A.NativeTypedArray.prototype = {
    get$length(receiver) {
      return receiver.length;
    },
    _setRangeFast$4(receiver, start, end, source, skipCount) {
      var count, sourceLength,
        targetLength = receiver.length;
      this._checkPosition$3(receiver, start, targetLength, "start");
      this._checkPosition$3(receiver, end, targetLength, "end");
      if (start > end)
        throw A.wrapException(A.RangeError$range(start, 0, end, null, null));
      count = end - start;
      sourceLength = source.length;
      if (sourceLength - skipCount < count)
        throw A.wrapException(A.StateError$("Not enough elements"));
      if (skipCount !== 0 || sourceLength !== count)
        source = source.subarray(skipCount, skipCount + count);
      receiver.set(source, start);
    },
    $isJSIndexable: 1,
    $isJavaScriptIndexingBehavior: 1
  };
  A.NativeTypedArrayOfDouble.prototype = {
    $index(receiver, index) {
      A._asInt(index);
      A._checkValidIndex(index, receiver, receiver.length);
      return receiver[index];
    },
    $indexSet(receiver, index, value) {
      A._asDouble(value);
      A._checkValidIndex(index, receiver, receiver.length);
      receiver[index] = value;
    },
    $isEfficientLengthIterable: 1,
    $isIterable: 1,
    $isList: 1
  };
  A.NativeTypedArrayOfInt.prototype = {
    $indexSet(receiver, index, value) {
      A._asInt(value);
      A._checkValidIndex(index, receiver, receiver.length);
      receiver[index] = value;
    },
    setRange$3(receiver, start, end, iterable) {
      type$.Iterable_int._as(iterable);
      if (type$.NativeTypedArrayOfInt._is(iterable)) {
        this._setRangeFast$4(receiver, start, end, iterable, 0);
        return;
      }
      this.super$ListMixin$setRange(receiver, start, end, iterable, 0);
    },
    $isEfficientLengthIterable: 1,
    $isIterable: 1,
    $isList: 1
  };
  A.NativeInt16List.prototype = {
    $index(receiver, index) {
      A._asInt(index);
      A._checkValidIndex(index, receiver, receiver.length);
      return receiver[index];
    }
  };
  A.NativeInt32List.prototype = {
    $index(receiver, index) {
      A._asInt(index);
      A._checkValidIndex(index, receiver, receiver.length);
      return receiver[index];
    }
  };
  A.NativeInt8List.prototype = {
    $index(receiver, index) {
      A._asInt(index);
      A._checkValidIndex(index, receiver, receiver.length);
      return receiver[index];
    }
  };
  A.NativeUint16List.prototype = {
    $index(receiver, index) {
      A._asInt(index);
      A._checkValidIndex(index, receiver, receiver.length);
      return receiver[index];
    }
  };
  A.NativeUint32List.prototype = {
    $index(receiver, index) {
      A._asInt(index);
      A._checkValidIndex(index, receiver, receiver.length);
      return receiver[index];
    }
  };
  A.NativeUint8ClampedList.prototype = {
    get$length(receiver) {
      return receiver.length;
    },
    $index(receiver, index) {
      A._asInt(index);
      A._checkValidIndex(index, receiver, receiver.length);
      return receiver[index];
    }
  };
  A.NativeUint8List.prototype = {
    get$length(receiver) {
      return receiver.length;
    },
    $index(receiver, index) {
      A._asInt(index);
      A._checkValidIndex(index, receiver, receiver.length);
      return receiver[index];
    },
    sublist$2(receiver, start, end) {
      return new Uint8Array(receiver.subarray(start, A._checkValidRange(start, end, receiver.length)));
    },
    sublist$1($receiver, start) {
      return this.sublist$2($receiver, start, null);
    },
    $isUint8List: 1
  };
  A._NativeTypedArrayOfDouble_NativeTypedArray_ListMixin.prototype = {};
  A._NativeTypedArrayOfDouble_NativeTypedArray_ListMixin_FixedLengthListMixin.prototype = {};
  A._NativeTypedArrayOfInt_NativeTypedArray_ListMixin.prototype = {};
  A._NativeTypedArrayOfInt_NativeTypedArray_ListMixin_FixedLengthListMixin.prototype = {};
  A.Rti.prototype = {
    _eval$1(recipe) {
      return A._Universe_evalInEnvironment(init.typeUniverse, this, recipe);
    },
    _bind$1(typeOrTuple) {
      return A._Universe_bind(init.typeUniverse, this, typeOrTuple);
    }
  };
  A._FunctionParameters.prototype = {};
  A._Type.prototype = {
    toString$0(_) {
      return A._rtiToString(this._rti, null);
    }
  };
  A._Error.prototype = {
    toString$0(_) {
      return this.__rti$_message;
    }
  };
  A._TypeError.prototype = {$isTypeError: 1};
  A._AsyncRun__initializeScheduleImmediate_internalCallback.prototype = {
    call$1(_) {
      var t1 = this._box_0,
        f = t1.storedCallback;
      t1.storedCallback = null;
      f.call$0();
    },
    $signature: 3
  };
  A._AsyncRun__initializeScheduleImmediate_closure.prototype = {
    call$1(callback) {
      var t1, t2;
      this._box_0.storedCallback = type$.void_Function._as(callback);
      t1 = this.div;
      t2 = this.span;
      t1.firstChild ? t1.removeChild(t2) : t1.appendChild(t2);
    },
    $signature: 11
  };
  A._AsyncRun__scheduleImmediateJsOverride_internalCallback.prototype = {
    call$0() {
      this.callback.call$0();
    },
    $signature: 6
  };
  A._AsyncRun__scheduleImmediateWithSetImmediate_internalCallback.prototype = {
    call$0() {
      this.callback.call$0();
    },
    $signature: 6
  };
  A._TimerImpl.prototype = {
    _TimerImpl$2(milliseconds, callback) {
      if (self.setTimeout != null)
        self.setTimeout(A.convertDartClosureToJS(new A._TimerImpl_internalCallback(this, callback), 0), milliseconds);
      else
        throw A.wrapException(A.UnsupportedError$("`setTimeout()` not found."));
    }
  };
  A._TimerImpl_internalCallback.prototype = {
    call$0() {
      this.callback.call$0();
    },
    $signature: 0
  };
  A._AsyncAwaitCompleter.prototype = {
    complete$1(_, value) {
      var t2, _this = this,
        t1 = _this.$ti;
      t1._eval$1("1/?")._as(value);
      if (value == null)
        t1._precomputed1._as(value);
      if (!_this.isSync)
        _this._future._asyncComplete$1(value);
      else {
        t2 = _this._future;
        if (t1._eval$1("Future<1>")._is(value))
          t2._chainFuture$1(value);
        else
          t2._completeWithValue$1(t1._precomputed1._as(value));
      }
    },
    completeError$2(e, st) {
      var t1 = this._future;
      if (this.isSync)
        t1._completeError$2(e, st);
      else
        t1._asyncCompleteError$2(e, st);
    },
    $isCompleter: 1
  };
  A._awaitOnObject_closure.prototype = {
    call$1(result) {
      return this.bodyFunction.call$2(0, result);
    },
    $signature: 4
  };
  A._awaitOnObject_closure0.prototype = {
    call$2(error, stackTrace) {
      this.bodyFunction.call$2(1, new A.ExceptionAndStackTrace(error, type$.StackTrace._as(stackTrace)));
    },
    $signature: 12
  };
  A._wrapJsFunctionForAsync_closure.prototype = {
    call$2(errorCode, result) {
      this.$protected(A._asInt(errorCode), result);
    },
    $signature: 13
  };
  A.AsyncError.prototype = {
    toString$0(_) {
      return A.S(this.error);
    },
    $isError: 1,
    get$stackTrace() {
      return this.stackTrace;
    }
  };
  A._Completer.prototype = {
    completeError$2(error, stackTrace) {
      var t1;
      A.checkNotNullable(error, "error", type$.Object);
      t1 = this.future;
      if ((t1._state & 30) !== 0)
        throw A.wrapException(A.StateError$("Future already completed"));
      if (stackTrace == null)
        stackTrace = A.AsyncError_defaultStackTrace(error);
      t1._asyncCompleteError$2(error, stackTrace);
    },
    completeError$1(error) {
      return this.completeError$2(error, null);
    },
    $isCompleter: 1
  };
  A._AsyncCompleter.prototype = {
    complete$1(_, value) {
      var t2,
        t1 = this.$ti;
      t1._eval$1("1/?")._as(value);
      t2 = this.future;
      if ((t2._state & 30) !== 0)
        throw A.wrapException(A.StateError$("Future already completed"));
      t2._asyncComplete$1(t1._eval$1("1/")._as(value));
    },
    complete$0($receiver) {
      return this.complete$1($receiver, null);
    }
  };
  A._FutureListener.prototype = {
    matchesErrorTest$1(asyncError) {
      if ((this.state & 15) !== 6)
        return true;
      return this.result._zone.runUnary$2$2(type$.bool_Function_Object._as(this.callback), asyncError.error, type$.bool, type$.Object);
    },
    handleError$1(asyncError) {
      var exception, _this = this,
        errorCallback = _this.errorCallback,
        result = null,
        t1 = type$.dynamic,
        t2 = type$.Object,
        t3 = asyncError.error,
        t4 = _this.result._zone;
      if (type$.dynamic_Function_Object_StackTrace._is(errorCallback))
        result = t4.runBinary$3$3(errorCallback, t3, asyncError.stackTrace, t1, t2, type$.StackTrace);
      else
        result = t4.runUnary$2$2(type$.dynamic_Function_Object._as(errorCallback), t3, t1, t2);
      try {
        t1 = _this.$ti._eval$1("2/")._as(result);
        return t1;
      } catch (exception) {
        if (type$.TypeError._is(A.unwrapException(exception))) {
          if ((_this.state & 1) !== 0)
            throw A.wrapException(A.ArgumentError$("The error handler of Future.then must return a value of the returned future's type", "onError"));
          throw A.wrapException(A.ArgumentError$("The error handler of Future.catchError must return a value of the future's type", "onError"));
        } else
          throw exception;
      }
    }
  };
  A._Future.prototype = {
    then$1$2$onError(f, onError, $R) {
      var currentZone, result, t2,
        t1 = this.$ti;
      t1._bind$1($R)._eval$1("1/(2)")._as(f);
      currentZone = $.Zone__current;
      if (currentZone === B.C__RootZone) {
        if (onError != null && !type$.dynamic_Function_Object_StackTrace._is(onError) && !type$.dynamic_Function_Object._is(onError))
          throw A.wrapException(A.ArgumentError$value(onError, "onError", string$.Error_));
      } else {
        $R._eval$1("@<0/>")._bind$1(t1._precomputed1)._eval$1("1(2)")._as(f);
        if (onError != null)
          onError = A._registerErrorHandler(onError, currentZone);
      }
      result = new A._Future(currentZone, $R._eval$1("_Future<0>"));
      t2 = onError == null ? 1 : 3;
      this._addListener$1(new A._FutureListener(result, t2, f, onError, t1._eval$1("@<1>")._bind$1($R)._eval$1("_FutureListener<1,2>")));
      return result;
    },
    then$1$1(f, $R) {
      return this.then$1$2$onError(f, null, $R);
    },
    _thenAwait$1$2(f, onError, $E) {
      var result,
        t1 = this.$ti;
      t1._bind$1($E)._eval$1("1/(2)")._as(f);
      result = new A._Future($.Zone__current, $E._eval$1("_Future<0>"));
      this._addListener$1(new A._FutureListener(result, 3, f, onError, t1._eval$1("@<1>")._bind$1($E)._eval$1("_FutureListener<1,2>")));
      return result;
    },
    _setErrorObject$1(error) {
      this._state = this._state & 1 | 16;
      this._resultOrListeners = error;
    },
    _cloneResult$1(source) {
      this._state = source._state & 30 | this._state & 1;
      this._resultOrListeners = source._resultOrListeners;
    },
    _addListener$1(listener) {
      var source, _this = this,
        t1 = _this._state;
      if (t1 <= 3) {
        listener._nextListener = type$.nullable__FutureListener_dynamic_dynamic._as(_this._resultOrListeners);
        _this._resultOrListeners = listener;
      } else {
        if ((t1 & 4) !== 0) {
          source = type$._Future_dynamic._as(_this._resultOrListeners);
          if ((source._state & 24) === 0) {
            source._addListener$1(listener);
            return;
          }
          _this._cloneResult$1(source);
        }
        A._rootScheduleMicrotask(null, null, _this._zone, type$.void_Function._as(new A._Future__addListener_closure(_this, listener)));
      }
    },
    _prependListeners$1(listeners) {
      var t1, existingListeners, next, cursor, next0, source, _this = this, _box_0 = {};
      _box_0.listeners = listeners;
      if (listeners == null)
        return;
      t1 = _this._state;
      if (t1 <= 3) {
        existingListeners = type$.nullable__FutureListener_dynamic_dynamic._as(_this._resultOrListeners);
        _this._resultOrListeners = listeners;
        if (existingListeners != null) {
          next = listeners._nextListener;
          for (cursor = listeners; next != null; cursor = next, next = next0)
            next0 = next._nextListener;
          cursor._nextListener = existingListeners;
        }
      } else {
        if ((t1 & 4) !== 0) {
          source = type$._Future_dynamic._as(_this._resultOrListeners);
          if ((source._state & 24) === 0) {
            source._prependListeners$1(listeners);
            return;
          }
          _this._cloneResult$1(source);
        }
        _box_0.listeners = _this._reverseListeners$1(listeners);
        A._rootScheduleMicrotask(null, null, _this._zone, type$.void_Function._as(new A._Future__prependListeners_closure(_box_0, _this)));
      }
    },
    _removeListeners$0() {
      var current = type$.nullable__FutureListener_dynamic_dynamic._as(this._resultOrListeners);
      this._resultOrListeners = null;
      return this._reverseListeners$1(current);
    },
    _reverseListeners$1(listeners) {
      var current, prev, next;
      for (current = listeners, prev = null; current != null; prev = current, current = next) {
        next = current._nextListener;
        current._nextListener = prev;
      }
      return prev;
    },
    _chainForeignFuture$1(source) {
      var e, s, exception, _this = this;
      _this._state ^= 2;
      try {
        source.then$1$2$onError(new A._Future__chainForeignFuture_closure(_this), new A._Future__chainForeignFuture_closure0(_this), type$.Null);
      } catch (exception) {
        e = A.unwrapException(exception);
        s = A.getTraceFromException(exception);
        A.scheduleMicrotask(new A._Future__chainForeignFuture_closure1(_this, e, s));
      }
    },
    _completeWithValue$1(value) {
      var listeners, _this = this;
      _this.$ti._precomputed1._as(value);
      listeners = _this._removeListeners$0();
      _this._state = 8;
      _this._resultOrListeners = value;
      A._Future__propagateToListeners(_this, listeners);
    },
    _completeError$2(error, stackTrace) {
      var listeners;
      type$.StackTrace._as(stackTrace);
      listeners = this._removeListeners$0();
      this._setErrorObject$1(A.AsyncError$(error, stackTrace));
      A._Future__propagateToListeners(this, listeners);
    },
    _asyncComplete$1(value) {
      var t1 = this.$ti;
      t1._eval$1("1/")._as(value);
      if (t1._eval$1("Future<1>")._is(value)) {
        this._chainFuture$1(value);
        return;
      }
      this._asyncCompleteWithValue$1(t1._precomputed1._as(value));
    },
    _asyncCompleteWithValue$1(value) {
      var _this = this;
      _this.$ti._precomputed1._as(value);
      _this._state ^= 2;
      A._rootScheduleMicrotask(null, null, _this._zone, type$.void_Function._as(new A._Future__asyncCompleteWithValue_closure(_this, value)));
    },
    _chainFuture$1(value) {
      var _this = this,
        t1 = _this.$ti;
      t1._eval$1("Future<1>")._as(value);
      if (t1._is(value)) {
        if ((value._state & 16) !== 0) {
          _this._state ^= 2;
          A._rootScheduleMicrotask(null, null, _this._zone, type$.void_Function._as(new A._Future__chainFuture_closure(_this, value)));
        } else
          A._Future__chainCoreFuture(value, _this);
        return;
      }
      _this._chainForeignFuture$1(value);
    },
    _asyncCompleteError$2(error, stackTrace) {
      this._state ^= 2;
      A._rootScheduleMicrotask(null, null, this._zone, type$.void_Function._as(new A._Future__asyncCompleteError_closure(this, error, stackTrace)));
    },
    $isFuture: 1
  };
  A._Future__addListener_closure.prototype = {
    call$0() {
      A._Future__propagateToListeners(this.$this, this.listener);
    },
    $signature: 0
  };
  A._Future__prependListeners_closure.prototype = {
    call$0() {
      A._Future__propagateToListeners(this.$this, this._box_0.listeners);
    },
    $signature: 0
  };
  A._Future__chainForeignFuture_closure.prototype = {
    call$1(value) {
      var error, stackTrace, exception,
        t1 = this.$this;
      t1._state ^= 2;
      try {
        t1._completeWithValue$1(t1.$ti._precomputed1._as(value));
      } catch (exception) {
        error = A.unwrapException(exception);
        stackTrace = A.getTraceFromException(exception);
        t1._completeError$2(error, stackTrace);
      }
    },
    $signature: 3
  };
  A._Future__chainForeignFuture_closure0.prototype = {
    call$2(error, stackTrace) {
      this.$this._completeError$2(type$.Object._as(error), type$.StackTrace._as(stackTrace));
    },
    $signature: 14
  };
  A._Future__chainForeignFuture_closure1.prototype = {
    call$0() {
      this.$this._completeError$2(this.e, this.s);
    },
    $signature: 0
  };
  A._Future__asyncCompleteWithValue_closure.prototype = {
    call$0() {
      this.$this._completeWithValue$1(this.value);
    },
    $signature: 0
  };
  A._Future__chainFuture_closure.prototype = {
    call$0() {
      A._Future__chainCoreFuture(this.value, this.$this);
    },
    $signature: 0
  };
  A._Future__asyncCompleteError_closure.prototype = {
    call$0() {
      this.$this._completeError$2(this.error, this.stackTrace);
    },
    $signature: 0
  };
  A._Future__propagateToListeners_handleWhenCompleteCallback.prototype = {
    call$0() {
      var e, s, t1, exception, t2, originalSource, _this = this, completeResult = null;
      try {
        t1 = _this._box_0.listener;
        completeResult = t1.result._zone.run$1$1(type$.dynamic_Function._as(t1.callback), type$.dynamic);
      } catch (exception) {
        e = A.unwrapException(exception);
        s = A.getTraceFromException(exception);
        t1 = _this.hasError && type$.AsyncError._as(_this._box_1.source._resultOrListeners).error === e;
        t2 = _this._box_0;
        if (t1)
          t2.listenerValueOrError = type$.AsyncError._as(_this._box_1.source._resultOrListeners);
        else
          t2.listenerValueOrError = A.AsyncError$(e, s);
        t2.listenerHasError = true;
        return;
      }
      if (completeResult instanceof A._Future && (completeResult._state & 24) !== 0) {
        if ((completeResult._state & 16) !== 0) {
          t1 = _this._box_0;
          t1.listenerValueOrError = type$.AsyncError._as(completeResult._resultOrListeners);
          t1.listenerHasError = true;
        }
        return;
      }
      if (type$.Future_dynamic._is(completeResult)) {
        originalSource = _this._box_1.source;
        t1 = _this._box_0;
        t1.listenerValueOrError = completeResult.then$1$1(new A._Future__propagateToListeners_handleWhenCompleteCallback_closure(originalSource), type$.dynamic);
        t1.listenerHasError = false;
      }
    },
    $signature: 0
  };
  A._Future__propagateToListeners_handleWhenCompleteCallback_closure.prototype = {
    call$1(_) {
      return this.originalSource;
    },
    $signature: 15
  };
  A._Future__propagateToListeners_handleValueCallback.prototype = {
    call$0() {
      var e, s, t1, t2, t3, t4, t5, exception;
      try {
        t1 = this._box_0;
        t2 = t1.listener;
        t3 = t2.$ti;
        t4 = t3._precomputed1;
        t5 = t4._as(this.sourceResult);
        t1.listenerValueOrError = t2.result._zone.runUnary$2$2(t3._eval$1("2/(1)")._as(t2.callback), t5, t3._eval$1("2/"), t4);
      } catch (exception) {
        e = A.unwrapException(exception);
        s = A.getTraceFromException(exception);
        t1 = this._box_0;
        t1.listenerValueOrError = A.AsyncError$(e, s);
        t1.listenerHasError = true;
      }
    },
    $signature: 0
  };
  A._Future__propagateToListeners_handleError.prototype = {
    call$0() {
      var asyncError, e, s, t1, exception, t2, _this = this;
      try {
        asyncError = type$.AsyncError._as(_this._box_1.source._resultOrListeners);
        t1 = _this._box_0;
        if (t1.listener.matchesErrorTest$1(asyncError) && t1.listener.errorCallback != null) {
          t1.listenerValueOrError = t1.listener.handleError$1(asyncError);
          t1.listenerHasError = false;
        }
      } catch (exception) {
        e = A.unwrapException(exception);
        s = A.getTraceFromException(exception);
        t1 = type$.AsyncError._as(_this._box_1.source._resultOrListeners);
        t2 = _this._box_0;
        if (t1.error === e)
          t2.listenerValueOrError = t1;
        else
          t2.listenerValueOrError = A.AsyncError$(e, s);
        t2.listenerHasError = true;
      }
    },
    $signature: 0
  };
  A._AsyncCallbackEntry.prototype = {};
  A.Stream.prototype = {
    get$length(_) {
      var t2, t3, _this = this, t1 = {},
        future = new A._Future($.Zone__current, type$._Future_int);
      t1.count = 0;
      t2 = _this.$ti;
      t3 = t2._eval$1("~(1)?")._as(new A.Stream_length_closure(t1, _this));
      type$.nullable_void_Function._as(new A.Stream_length_closure0(t1, future));
      A._EventStreamSubscription$(_this._target, _this._eventType, t3, false, t2._precomputed1);
      return future;
    }
  };
  A.Stream_length_closure.prototype = {
    call$1(_) {
      this.$this.$ti._precomputed1._as(_);
      ++this._box_0.count;
    },
    $signature() {
      return this.$this.$ti._eval$1("~(1)");
    }
  };
  A.Stream_length_closure0.prototype = {
    call$0() {
      var t1 = this.future,
        t2 = t1.$ti,
        t3 = t2._eval$1("1/")._as(this._box_0.count),
        listeners = t1._removeListeners$0();
      t2._precomputed1._as(t3);
      t1._state = 8;
      t1._resultOrListeners = t3;
      A._Future__propagateToListeners(t1, listeners);
    },
    $signature: 0
  };
  A.StreamSubscription.prototype = {};
  A.StreamTransformerBase.prototype = {};
  A._StreamIterator.prototype = {};
  A._Zone.prototype = {$isZone: 1};
  A._rootHandleError_closure.prototype = {
    call$0() {
      var t1 = this.error,
        t2 = this.stackTrace;
      A.checkNotNullable(t1, "error", type$.Object);
      A.checkNotNullable(t2, "stackTrace", type$.StackTrace);
      A.Error__throw(t1, t2);
    },
    $signature: 0
  };
  A._RootZone.prototype = {
    runGuarded$1(f) {
      var e, s, exception;
      type$.void_Function._as(f);
      try {
        if (B.C__RootZone === $.Zone__current) {
          f.call$0();
          return;
        }
        A._rootRun(null, null, this, f, type$.void);
      } catch (exception) {
        e = A.unwrapException(exception);
        s = A.getTraceFromException(exception);
        A._rootHandleError(type$.Object._as(e), type$.StackTrace._as(s));
      }
    },
    runUnaryGuarded$1$2(f, arg, $T) {
      var e, s, exception;
      $T._eval$1("~(0)")._as(f);
      $T._as(arg);
      try {
        if (B.C__RootZone === $.Zone__current) {
          f.call$1(arg);
          return;
        }
        A._rootRunUnary(null, null, this, f, arg, type$.void, $T);
      } catch (exception) {
        e = A.unwrapException(exception);
        s = A.getTraceFromException(exception);
        A._rootHandleError(type$.Object._as(e), type$.StackTrace._as(s));
      }
    },
    bindCallbackGuarded$1(f) {
      return new A._RootZone_bindCallbackGuarded_closure(this, type$.void_Function._as(f));
    },
    bindUnaryCallbackGuarded$1$1(f, $T) {
      return new A._RootZone_bindUnaryCallbackGuarded_closure(this, $T._eval$1("~(0)")._as(f), $T);
    },
    $index(_, key) {
      return null;
    },
    run$1$1(f, $R) {
      $R._eval$1("0()")._as(f);
      if ($.Zone__current === B.C__RootZone)
        return f.call$0();
      return A._rootRun(null, null, this, f, $R);
    },
    runUnary$2$2(f, arg, $R, $T) {
      $R._eval$1("@<0>")._bind$1($T)._eval$1("1(2)")._as(f);
      $T._as(arg);
      if ($.Zone__current === B.C__RootZone)
        return f.call$1(arg);
      return A._rootRunUnary(null, null, this, f, arg, $R, $T);
    },
    runBinary$3$3(f, arg1, arg2, $R, T1, T2) {
      $R._eval$1("@<0>")._bind$1(T1)._bind$1(T2)._eval$1("1(2,3)")._as(f);
      T1._as(arg1);
      T2._as(arg2);
      if ($.Zone__current === B.C__RootZone)
        return f.call$2(arg1, arg2);
      return A._rootRunBinary(null, null, this, f, arg1, arg2, $R, T1, T2);
    },
    registerBinaryCallback$3$1(f, $R, T1, T2) {
      return $R._eval$1("@<0>")._bind$1(T1)._bind$1(T2)._eval$1("1(2,3)")._as(f);
    }
  };
  A._RootZone_bindCallbackGuarded_closure.prototype = {
    call$0() {
      return this.$this.runGuarded$1(this.f);
    },
    $signature: 0
  };
  A._RootZone_bindUnaryCallbackGuarded_closure.prototype = {
    call$1(arg) {
      var t1 = this.T;
      return this.$this.runUnaryGuarded$1$2(this.f, t1._as(arg), t1);
    },
    $signature() {
      return this.T._eval$1("~(0)");
    }
  };
  A._HashMap.prototype = {
    get$length(_) {
      return this._collection$_length;
    },
    get$keys(_) {
      return new A._HashMapKeyIterable(this, this.$ti._eval$1("_HashMapKeyIterable<1>"));
    },
    containsKey$1(_, key) {
      var strings, nums;
      if (typeof key == "string" && key !== "__proto__") {
        strings = this._collection$_strings;
        return strings == null ? false : strings[key] != null;
      } else if (typeof key == "number" && (key & 1073741823) === key) {
        nums = this._collection$_nums;
        return nums == null ? false : nums[key] != null;
      } else
        return this._containsKey$1(key);
    },
    _containsKey$1(key) {
      var rest = this._collection$_rest;
      if (rest == null)
        return false;
      return this._findBucketIndex$2(this._getBucket$2(rest, key), key) >= 0;
    },
    $index(_, key) {
      var strings, t1, nums;
      if (typeof key == "string" && key !== "__proto__") {
        strings = this._collection$_strings;
        t1 = strings == null ? null : A._HashMap__getTableEntry(strings, key);
        return t1;
      } else if (typeof key == "number" && (key & 1073741823) === key) {
        nums = this._collection$_nums;
        t1 = nums == null ? null : A._HashMap__getTableEntry(nums, key);
        return t1;
      } else
        return this._get$1(0, key);
    },
    _get$1(_, key) {
      var bucket, index,
        rest = this._collection$_rest;
      if (rest == null)
        return null;
      bucket = this._getBucket$2(rest, key);
      index = this._findBucketIndex$2(bucket, key);
      return index < 0 ? null : bucket[index + 1];
    },
    $indexSet(_, key, value) {
      var rest, hash, bucket, index, _this = this,
        t1 = _this.$ti;
      t1._precomputed1._as(key);
      t1._rest[1]._as(value);
      rest = _this._collection$_rest;
      if (rest == null)
        rest = _this._collection$_rest = A._HashMap__newHashTable();
      hash = A.objectHashCode(key) & 1073741823;
      bucket = rest[hash];
      if (bucket == null) {
        A._HashMap__setTableEntry(rest, hash, [key, value]);
        ++_this._collection$_length;
        _this._keys = null;
      } else {
        index = _this._findBucketIndex$2(bucket, key);
        if (index >= 0)
          bucket[index + 1] = value;
        else {
          bucket.push(key, value);
          ++_this._collection$_length;
          _this._keys = null;
        }
      }
    },
    forEach$1(_, action) {
      var keys, $length, t2, i, key, t3, _this = this,
        t1 = _this.$ti;
      t1._eval$1("~(1,2)")._as(action);
      keys = _this._computeKeys$0();
      for ($length = keys.length, t2 = t1._precomputed1, t1 = t1._rest[1], i = 0; i < $length; ++i) {
        key = keys[i];
        t2._as(key);
        t3 = _this.$index(0, key);
        action.call$2(key, t3 == null ? t1._as(t3) : t3);
        if (keys !== _this._keys)
          throw A.wrapException(A.ConcurrentModificationError$(_this));
      }
    },
    _computeKeys$0() {
      var strings, names, entries, index, i, nums, rest, bucket, $length, i0, _this = this,
        result = _this._keys;
      if (result != null)
        return result;
      result = A.List_List$filled(_this._collection$_length, null, false, type$.dynamic);
      strings = _this._collection$_strings;
      if (strings != null) {
        names = Object.getOwnPropertyNames(strings);
        entries = names.length;
        for (index = 0, i = 0; i < entries; ++i) {
          result[index] = names[i];
          ++index;
        }
      } else
        index = 0;
      nums = _this._collection$_nums;
      if (nums != null) {
        names = Object.getOwnPropertyNames(nums);
        entries = names.length;
        for (i = 0; i < entries; ++i) {
          result[index] = +names[i];
          ++index;
        }
      }
      rest = _this._collection$_rest;
      if (rest != null) {
        names = Object.getOwnPropertyNames(rest);
        entries = names.length;
        for (i = 0; i < entries; ++i) {
          bucket = rest[names[i]];
          $length = bucket.length;
          for (i0 = 0; i0 < $length; i0 += 2) {
            result[index] = bucket[i0];
            ++index;
          }
        }
      }
      return _this._keys = result;
    },
    _getBucket$2(table, key) {
      return table[A.objectHashCode(key) & 1073741823];
    }
  };
  A._IdentityHashMap.prototype = {
    _findBucketIndex$2(bucket, key) {
      var $length, i, t1;
      if (bucket == null)
        return -1;
      $length = bucket.length;
      for (i = 0; i < $length; i += 2) {
        t1 = bucket[i];
        if (t1 == null ? key == null : t1 === key)
          return i;
      }
      return -1;
    }
  };
  A._HashMapKeyIterable.prototype = {
    get$length(_) {
      return this._collection$_map._collection$_length;
    },
    get$iterator(_) {
      var t1 = this._collection$_map;
      return new A._HashMapKeyIterator(t1, t1._computeKeys$0(), this.$ti._eval$1("_HashMapKeyIterator<1>"));
    }
  };
  A._HashMapKeyIterator.prototype = {
    get$current(_) {
      var t1 = this._collection$_current;
      return t1 == null ? this.$ti._precomputed1._as(t1) : t1;
    },
    moveNext$0() {
      var _this = this,
        keys = _this._keys,
        offset = _this._offset,
        t1 = _this._collection$_map;
      if (keys !== t1._keys)
        throw A.wrapException(A.ConcurrentModificationError$(t1));
      else if (offset >= keys.length) {
        _this.set$_collection$_current(null);
        return false;
      } else {
        _this.set$_collection$_current(keys[offset]);
        _this._offset = offset + 1;
        return true;
      }
    },
    set$_collection$_current(_current) {
      this._collection$_current = this.$ti._eval$1("1?")._as(_current);
    },
    $isIterator: 1
  };
  A.ListBase.prototype = {$isEfficientLengthIterable: 1, $isIterable: 1, $isList: 1};
  A.ListMixin.prototype = {
    get$iterator(receiver) {
      return new A.ListIterator(receiver, this.get$length(receiver), A.instanceType(receiver)._eval$1("ListIterator<ListMixin.E>"));
    },
    elementAt$1(receiver, index) {
      return this.$index(receiver, index);
    },
    map$1$1(receiver, f, $T) {
      var t1 = A.instanceType(receiver);
      return new A.MappedListIterable(receiver, t1._bind$1($T)._eval$1("1(ListMixin.E)")._as(f), t1._eval$1("@<ListMixin.E>")._bind$1($T)._eval$1("MappedListIterable<1,2>"));
    },
    skip$1(receiver, count) {
      return A.SubListIterable$(receiver, count, null, A.instanceType(receiver)._eval$1("ListMixin.E"));
    },
    setRange$4(receiver, start, end, iterable, skipCount) {
      var $length, otherStart, otherList, i,
        t1 = A.instanceType(receiver);
      t1._eval$1("Iterable<ListMixin.E>")._as(iterable);
      A.RangeError_checkValidRange(start, end, this.get$length(receiver));
      $length = end - start;
      if ($length === 0)
        return;
      A.RangeError_checkNotNegative(skipCount, "skipCount");
      if (t1._eval$1("List<ListMixin.E>")._is(iterable)) {
        otherStart = skipCount;
        otherList = iterable;
      } else {
        otherList = J.skip$1$ax(iterable, skipCount).toList$1$growable(0, false);
        otherStart = 0;
      }
      if (otherStart + $length > otherList.length)
        throw A.wrapException(A.StateError$("Too few elements"));
      if (otherStart < start)
        for (i = $length - 1; i >= 0; --i) {
          t1 = otherStart + i;
          if (!(t1 < otherList.length))
            return A.ioore(otherList, t1);
          this.$indexSet(receiver, start + i, otherList[t1]);
        }
      else
        for (i = 0; i < $length; ++i) {
          t1 = otherStart + i;
          if (!(t1 < otherList.length))
            return A.ioore(otherList, t1);
          this.$indexSet(receiver, start + i, otherList[t1]);
        }
    },
    toString$0(receiver) {
      return A.IterableBase_iterableToFullString(receiver, "[", "]");
    }
  };
  A.MapBase.prototype = {};
  A.MapBase_mapToString_closure.prototype = {
    call$2(k, v) {
      var t2,
        t1 = this._box_0;
      if (!t1.first)
        this.result._contents += ", ";
      t1.first = false;
      t1 = this.result;
      t2 = t1._contents += A.S(k);
      t1._contents = t2 + ": ";
      t1._contents += A.S(v);
    },
    $signature: 16
  };
  A.MapMixin.prototype = {
    forEach$1(receiver, action) {
      var t2, key, t3,
        t1 = A.instanceType(receiver);
      t1._eval$1("~(MapMixin.K,MapMixin.V)")._as(action);
      for (t2 = J.get$iterator$ax(this.get$keys(receiver)), t1 = t1._eval$1("MapMixin.V"); t2.moveNext$0();) {
        key = t2.get$current(t2);
        t3 = this.$index(receiver, key);
        action.call$2(key, t3 == null ? t1._as(t3) : t3);
      }
    },
    get$length(receiver) {
      return J.get$length$asx(this.get$keys(receiver));
    },
    toString$0(receiver) {
      return A.MapBase_mapToString(receiver);
    },
    $isMap: 1
  };
  A._UnmodifiableMapMixin.prototype = {};
  A.MapView.prototype = {
    $index(_, key) {
      return this._collection$_map.$index(0, key);
    },
    forEach$1(_, action) {
      this._collection$_map.forEach$1(0, this.$ti._eval$1("~(1,2)")._as(action));
    },
    get$length(_) {
      return this._collection$_map._length;
    },
    get$keys(_) {
      var t1 = this._collection$_map;
      return new A.LinkedHashMapKeyIterable(t1, t1.$ti._eval$1("LinkedHashMapKeyIterable<1>"));
    },
    toString$0(_) {
      return A.MapBase_mapToString(this._collection$_map);
    },
    $isMap: 1
  };
  A.UnmodifiableMapView.prototype = {};
  A._ListBase_Object_ListMixin.prototype = {};
  A._UnmodifiableMapView_MapView__UnmodifiableMapMixin.prototype = {};
  A.Base64Codec.prototype = {};
  A.Base64Encoder.prototype = {
    convert$1(input) {
      var t1;
      type$.List_int._as(input);
      t1 = input.length;
      if (t1 === 0)
        return "";
      t1 = new A._Base64Encoder("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/").encode$4(input, 0, t1, true);
      t1.toString;
      return A.String_String$fromCharCodes(t1);
    }
  };
  A._Base64Encoder.prototype = {
    encode$4(bytes, start, end, isLast) {
      var t1, byteCount, fullChunks, bufferLength, output;
      type$.List_int._as(bytes);
      t1 = this._convert$_state;
      byteCount = (t1 & 3) + (end - start);
      fullChunks = B.JSInt_methods._tdivFast$1(byteCount, 3);
      bufferLength = fullChunks * 4;
      if (byteCount - fullChunks * 3 > 0)
        bufferLength += 4;
      output = new Uint8Array(bufferLength);
      this._convert$_state = A._Base64Encoder_encodeChunk(this._alphabet, bytes, start, end, true, output, 0, t1);
      if (bufferLength > 0)
        return output;
      return null;
    }
  };
  A.Base64Decoder.prototype = {
    convert$1(input) {
      var decoder, t1, t2,
        end = A.RangeError_checkValidRange(0, null, input.length);
      if (0 === end)
        return new Uint8Array(0);
      decoder = new A._Base64Decoder();
      t1 = decoder.decode$3(0, input, 0, end);
      t1.toString;
      t2 = decoder._convert$_state;
      if (t2 < -1)
        A.throwExpression(A.FormatException$("Missing padding character", input, end));
      if (t2 > 0)
        A.throwExpression(A.FormatException$("Invalid length, must be multiple of four", input, end));
      decoder._convert$_state = -1;
      return t1;
    }
  };
  A._Base64Decoder.prototype = {
    decode$3(_, input, start, end) {
      var buffer, _this = this,
        t1 = _this._convert$_state;
      if (t1 < 0) {
        _this._convert$_state = A._Base64Decoder__checkPadding(input, start, end, t1);
        return null;
      }
      if (start === end)
        return new Uint8Array(0);
      buffer = A._Base64Decoder__allocateBuffer(input, start, end, t1);
      _this._convert$_state = A._Base64Decoder_decodeChunk(input, start, end, buffer, 0, _this._convert$_state);
      return buffer;
    }
  };
  A.Codec.prototype = {};
  A.Converter.prototype = {};
  A.NoSuchMethodError_toString_closure.prototype = {
    call$2(key, value) {
      var t1, t2, t3;
      type$.Symbol._as(key);
      t1 = this.sb;
      t2 = this._box_0;
      t3 = t1._contents += t2.comma;
      t3 += key.__internal$_name;
      t1._contents = t3;
      t1._contents = t3 + ": ";
      t1._contents += A.Error_safeToString(value);
      t2.comma = ", ";
    },
    $signature: 17
  };
  A.DateTime.prototype = {
    $eq(_, other) {
      if (other == null)
        return false;
      return other instanceof A.DateTime && this._value === other._value && true;
    },
    get$hashCode(_) {
      var t1 = this._value;
      return (t1 ^ B.JSInt_methods._shrOtherPositive$1(t1, 30)) & 1073741823;
    },
    toString$0(_) {
      var _this = this,
        y = A.DateTime__fourDigits(A.Primitives_getYear(_this)),
        m = A.DateTime__twoDigits(A.Primitives_getMonth(_this)),
        d = A.DateTime__twoDigits(A.Primitives_getDay(_this)),
        h = A.DateTime__twoDigits(A.Primitives_getHours(_this)),
        min = A.DateTime__twoDigits(A.Primitives_getMinutes(_this)),
        sec = A.DateTime__twoDigits(A.Primitives_getSeconds(_this)),
        ms = A.DateTime__threeDigits(A.Primitives_getMilliseconds(_this));
      return y + "-" + m + "-" + d + " " + h + ":" + min + ":" + sec + "." + ms + "Z";
    }
  };
  A._Enum.prototype = {
    toString$0(_) {
      return this._enumToString$0();
    }
  };
  A.Error.prototype = {
    get$stackTrace() {
      return A.getTraceFromException(this.$thrownJsError);
    }
  };
  A.AssertionError.prototype = {
    toString$0(_) {
      var t1 = this.message;
      if (t1 != null)
        return "Assertion failed: " + A.Error_safeToString(t1);
      return "Assertion failed";
    }
  };
  A.TypeError.prototype = {};
  A.NullThrownError.prototype = {
    toString$0(_) {
      return "Throw of null.";
    },
    $isTypeError: 1
  };
  A.ArgumentError.prototype = {
    get$_errorName() {
      return "Invalid argument" + (!this._hasValue ? "(s)" : "");
    },
    get$_errorExplanation() {
      return "";
    },
    toString$0(_) {
      var _this = this,
        $name = _this.name,
        nameString = $name == null ? "" : " (" + $name + ")",
        message = _this.message,
        messageString = message == null ? "" : ": " + A.S(message),
        prefix = _this.get$_errorName() + nameString + messageString;
      if (!_this._hasValue)
        return prefix;
      return prefix + _this.get$_errorExplanation() + ": " + A.Error_safeToString(_this.get$invalidValue());
    },
    get$invalidValue() {
      return this.invalidValue;
    }
  };
  A.RangeError.prototype = {
    get$invalidValue() {
      return A._asNumQ(this.invalidValue);
    },
    get$_errorName() {
      return "RangeError";
    },
    get$_errorExplanation() {
      var explanation,
        start = this.start,
        end = this.end;
      if (start == null)
        explanation = end != null ? ": Not less than or equal to " + A.S(end) : "";
      else if (end == null)
        explanation = ": Not greater than or equal to " + A.S(start);
      else if (end > start)
        explanation = ": Not in inclusive range " + A.S(start) + ".." + A.S(end);
      else
        explanation = end < start ? ": Valid value range is empty" : ": Only valid value is " + A.S(start);
      return explanation;
    }
  };
  A.IndexError.prototype = {
    get$invalidValue() {
      return A._asInt(this.invalidValue);
    },
    get$_errorName() {
      return "RangeError";
    },
    get$_errorExplanation() {
      if (A._asInt(this.invalidValue) < 0)
        return ": index must not be negative";
      var t1 = this.length;
      if (t1 === 0)
        return ": no indices are valid";
      return ": index should be less than " + t1;
    },
    get$length(receiver) {
      return this.length;
    }
  };
  A.NoSuchMethodError.prototype = {
    toString$0(_) {
      var $arguments, t1, _i, t2, t3, argument, receiverText, actualParameters, _this = this, _box_0 = {},
        sb = new A.StringBuffer("");
      _box_0.comma = "";
      $arguments = _this._core$_arguments;
      for (t1 = $arguments.length, _i = 0, t2 = "", t3 = ""; _i < t1; ++_i, t3 = ", ") {
        argument = $arguments[_i];
        sb._contents = t2 + t3;
        t2 = sb._contents += A.Error_safeToString(argument);
        _box_0.comma = ", ";
      }
      _this._namedArguments.forEach$1(0, new A.NoSuchMethodError_toString_closure(_box_0, sb));
      receiverText = A.Error_safeToString(_this._core$_receiver);
      actualParameters = sb.toString$0(0);
      return "NoSuchMethodError: method not found: '" + _this._core$_memberName.__internal$_name + "'\nReceiver: " + receiverText + "\nArguments: [" + actualParameters + "]";
    }
  };
  A.UnsupportedError.prototype = {
    toString$0(_) {
      return "Unsupported operation: " + this.message;
    }
  };
  A.UnimplementedError.prototype = {
    toString$0(_) {
      return "UnimplementedError: " + this.message;
    }
  };
  A.StateError.prototype = {
    toString$0(_) {
      return "Bad state: " + this.message;
    }
  };
  A.ConcurrentModificationError.prototype = {
    toString$0(_) {
      var t1 = this.modifiedObject;
      if (t1 == null)
        return "Concurrent modification during iteration.";
      return "Concurrent modification during iteration: " + A.Error_safeToString(t1) + ".";
    }
  };
  A.OutOfMemoryError.prototype = {
    toString$0(_) {
      return "Out of Memory";
    },
    get$stackTrace() {
      return null;
    },
    $isError: 1
  };
  A.StackOverflowError.prototype = {
    toString$0(_) {
      return "Stack Overflow";
    },
    get$stackTrace() {
      return null;
    },
    $isError: 1
  };
  A.CyclicInitializationError.prototype = {
    toString$0(_) {
      return "Reading static variable '" + this.variableName + "' during its initialization";
    }
  };
  A._Exception.prototype = {
    toString$0(_) {
      return "Exception: " + this.message;
    }
  };
  A.FormatException.prototype = {
    toString$0(_) {
      var lineNum, lineStart, previousCharWasCR, i, char, lineEnd, end, start, prefix, postfix,
        message = this.message,
        report = "" !== message ? "FormatException: " + message : "FormatException",
        offset = this.offset,
        source = this.source,
        t1 = offset < 0 || offset > source.length;
      if (t1)
        offset = null;
      if (offset == null) {
        if (source.length > 78)
          source = B.JSString_methods.substring$2(source, 0, 75) + "...";
        return report + "\n" + source;
      }
      for (lineNum = 1, lineStart = 0, previousCharWasCR = false, i = 0; i < offset; ++i) {
        char = B.JSString_methods._codeUnitAt$1(source, i);
        if (char === 10) {
          if (lineStart !== i || !previousCharWasCR)
            ++lineNum;
          lineStart = i + 1;
          previousCharWasCR = false;
        } else if (char === 13) {
          ++lineNum;
          lineStart = i + 1;
          previousCharWasCR = true;
        }
      }
      report = lineNum > 1 ? report + (" (at line " + lineNum + ", character " + (offset - lineStart + 1) + ")\n") : report + (" (at character " + (offset + 1) + ")\n");
      lineEnd = source.length;
      for (i = offset; i < lineEnd; ++i) {
        char = B.JSString_methods.codeUnitAt$1(source, i);
        if (char === 10 || char === 13) {
          lineEnd = i;
          break;
        }
      }
      if (lineEnd - lineStart > 78)
        if (offset - lineStart < 75) {
          end = lineStart + 75;
          start = lineStart;
          prefix = "";
          postfix = "...";
        } else {
          if (lineEnd - offset < 75) {
            start = lineEnd - 75;
            end = lineEnd;
            postfix = "";
          } else {
            start = offset - 36;
            end = offset + 36;
            postfix = "...";
          }
          prefix = "...";
        }
      else {
        end = lineEnd;
        start = lineStart;
        prefix = "";
        postfix = "";
      }
      return report + prefix + B.JSString_methods.substring$2(source, start, end) + postfix + "\n" + B.JSString_methods.$mul(" ", offset - start + prefix.length) + "^\n";
    }
  };
  A.Iterable.prototype = {
    map$1$1(_, toElement, $T) {
      var t1 = A._instanceType(this);
      return A.MappedIterable_MappedIterable(this, t1._bind$1($T)._eval$1("1(Iterable.E)")._as(toElement), t1._eval$1("Iterable.E"), $T);
    },
    get$length(_) {
      var count,
        it = this.get$iterator(this);
      for (count = 0; it.moveNext$0();)
        ++count;
      return count;
    },
    elementAt$1(_, index) {
      var t1, elementIndex, element;
      A.RangeError_checkNotNegative(index, "index");
      for (t1 = this.get$iterator(this), elementIndex = 0; t1.moveNext$0();) {
        element = t1.get$current(t1);
        if (index === elementIndex)
          return element;
        ++elementIndex;
      }
      throw A.wrapException(A.IndexError$withLength(index, elementIndex, this, "index"));
    },
    toString$0(_) {
      return A.IterableBase_iterableToShortString(this, "(", ")");
    }
  };
  A.Iterator.prototype = {};
  A.Null.prototype = {
    get$hashCode(_) {
      return A.Object.prototype.get$hashCode.call(this, this);
    },
    toString$0(_) {
      return "null";
    }
  };
  A.Object.prototype = {$isObject: 1,
    $eq(_, other) {
      return this === other;
    },
    get$hashCode(_) {
      return A.Primitives_objectHashCode(this);
    },
    toString$0(_) {
      return "Instance of '" + A.Primitives_objectTypeName(this) + "'";
    },
    noSuchMethod$1(_, invocation) {
      type$.Invocation._as(invocation);
      throw A.wrapException(A.NoSuchMethodError$_(this, invocation.get$memberName(), invocation.get$positionalArguments(), invocation.get$namedArguments(), null));
    },
    toString() {
      return this.toString$0(this);
    }
  };
  A._StringStackTrace.prototype = {
    toString$0(_) {
      return "";
    },
    $isStackTrace: 1
  };
  A.StringBuffer.prototype = {
    get$length(_) {
      return this._contents.length;
    },
    toString$0(_) {
      var t1 = this._contents;
      return t1.charCodeAt(0) == 0 ? t1 : t1;
    }
  };
  A.HtmlElement.prototype = {};
  A.AccessibleNodeList.prototype = {
    get$length(receiver) {
      return receiver.length;
    }
  };
  A.AnchorElement.prototype = {
    toString$0(receiver) {
      return String(receiver);
    }
  };
  A.AreaElement.prototype = {
    toString$0(receiver) {
      return String(receiver);
    }
  };
  A.Blob.prototype = {$isBlob: 1};
  A.CharacterData.prototype = {
    get$length(receiver) {
      return receiver.length;
    }
  };
  A.CryptoKey.prototype = {$isCryptoKey: 1};
  A.CssPerspective.prototype = {
    get$length(receiver) {
      return receiver.length;
    }
  };
  A.CssRule.prototype = {$isCssRule: 1};
  A.CssStyleDeclaration.prototype = {
    get$length(receiver) {
      return receiver.length;
    }
  };
  A.CssStyleDeclarationBase.prototype = {};
  A.CssStyleValue.prototype = {};
  A.CssTransformComponent.prototype = {};
  A.CssTransformValue.prototype = {
    get$length(receiver) {
      return receiver.length;
    }
  };
  A.CssUnparsedValue.prototype = {
    get$length(receiver) {
      return receiver.length;
    }
  };
  A.DataTransferItem.prototype = {
    get$kind(receiver) {
      return receiver.kind;
    }
  };
  A.DataTransferItemList.prototype = {
    get$length(receiver) {
      return receiver.length;
    },
    $index(receiver, index) {
      return receiver[A._asInt(index)];
    }
  };
  A.DedicatedWorkerGlobalScope.prototype = {
    postMessage$1(receiver, message) {
      receiver.postMessage(new A._StructuredCloneDart2Js([], []).walk$1(message));
      return;
    },
    $isDedicatedWorkerGlobalScope: 1
  };
  A.DomException.prototype = {
    toString$0(receiver) {
      return String(receiver);
    }
  };
  A.DomRectList.prototype = {
    get$length(receiver) {
      return receiver.length;
    },
    $index(receiver, index) {
      var t1;
      A._asInt(index);
      t1 = receiver.length;
      if (index >>> 0 !== index || index >= t1)
        throw A.wrapException(A.IndexError$withLength(index, t1, receiver, null));
      return receiver[index];
    },
    $indexSet(receiver, index, value) {
      type$.Rectangle_num._as(value);
      throw A.wrapException(A.UnsupportedError$("Cannot assign element of immutable List."));
    },
    elementAt$1(receiver, index) {
      if (!(index >= 0 && index < receiver.length))
        return A.ioore(receiver, index);
      return receiver[index];
    },
    $isJSIndexable: 1,
    $isEfficientLengthIterable: 1,
    $isJavaScriptIndexingBehavior: 1,
    $isIterable: 1,
    $isList: 1
  };
  A.DomRectReadOnly.prototype = {
    toString$0(receiver) {
      var t2,
        t1 = receiver.left;
      t1.toString;
      t2 = receiver.top;
      t2.toString;
      return "Rectangle (" + A.S(t1) + ", " + A.S(t2) + ") " + A.S(this.get$width(receiver)) + " x " + A.S(this.get$height(receiver));
    },
    $eq(receiver, other) {
      var t1, t2;
      if (other == null)
        return false;
      if (type$.Rectangle_num._is(other)) {
        t1 = receiver.left;
        t1.toString;
        t2 = other.left;
        t2.toString;
        if (t1 === t2) {
          t1 = receiver.top;
          t1.toString;
          t2 = other.top;
          t2.toString;
          if (t1 === t2) {
            t1 = J.getInterceptor$x(other);
            t1 = this.get$width(receiver) === t1.get$width(other) && this.get$height(receiver) === t1.get$height(other);
          } else
            t1 = false;
        } else
          t1 = false;
      } else
        t1 = false;
      return t1;
    },
    get$hashCode(receiver) {
      var t2,
        t1 = receiver.left;
      t1.toString;
      t2 = receiver.top;
      t2.toString;
      return A.Object_hash(t1, t2, this.get$width(receiver), this.get$height(receiver));
    },
    get$_height(receiver) {
      return receiver.height;
    },
    get$height(receiver) {
      var t1 = this.get$_height(receiver);
      t1.toString;
      return t1;
    },
    get$_width(receiver) {
      return receiver.width;
    },
    get$width(receiver) {
      var t1 = this.get$_width(receiver);
      t1.toString;
      return t1;
    },
    $isRectangle: 1
  };
  A.DomStringList.prototype = {
    get$length(receiver) {
      return receiver.length;
    },
    $index(receiver, index) {
      var t1;
      A._asInt(index);
      t1 = receiver.length;
      if (index >>> 0 !== index || index >= t1)
        throw A.wrapException(A.IndexError$withLength(index, t1, receiver, null));
      return receiver[index];
    },
    $indexSet(receiver, index, value) {
      A._asString(value);
      throw A.wrapException(A.UnsupportedError$("Cannot assign element of immutable List."));
    },
    elementAt$1(receiver, index) {
      if (!(index >= 0 && index < receiver.length))
        return A.ioore(receiver, index);
      return receiver[index];
    },
    $isJSIndexable: 1,
    $isEfficientLengthIterable: 1,
    $isJavaScriptIndexingBehavior: 1,
    $isIterable: 1,
    $isList: 1
  };
  A.DomTokenList.prototype = {
    get$length(receiver) {
      return receiver.length;
    }
  };
  A.Element.prototype = {
    toString$0(receiver) {
      return receiver.localName;
    }
  };
  A.Event.prototype = {$isEvent: 1};
  A.EventTarget.prototype = {
    addEventListener$3(receiver, type, listener, useCapture) {
      type$.nullable_dynamic_Function_Event._as(listener);
      if (listener != null)
        this._addEventListener$3(receiver, type, listener, false);
    },
    _addEventListener$3(receiver, type, listener, options) {
      return receiver.addEventListener(type, A.convertDartClosureToJS(type$.nullable_dynamic_Function_Event._as(listener), 1), false);
    },
    $isEventTarget: 1
  };
  A.File.prototype = {$isFile: 1};
  A.FileList.prototype = {
    get$length(receiver) {
      return receiver.length;
    },
    $index(receiver, index) {
      var t1;
      A._asInt(index);
      t1 = receiver.length;
      if (index >>> 0 !== index || index >= t1)
        throw A.wrapException(A.IndexError$withLength(index, t1, receiver, null));
      return receiver[index];
    },
    $indexSet(receiver, index, value) {
      type$.File._as(value);
      throw A.wrapException(A.UnsupportedError$("Cannot assign element of immutable List."));
    },
    elementAt$1(receiver, index) {
      if (!(index >= 0 && index < receiver.length))
        return A.ioore(receiver, index);
      return receiver[index];
    },
    $isJSIndexable: 1,
    $isEfficientLengthIterable: 1,
    $isJavaScriptIndexingBehavior: 1,
    $isIterable: 1,
    $isList: 1,
    $isFileList: 1
  };
  A.FileWriter.prototype = {
    get$length(receiver) {
      return receiver.length;
    }
  };
  A.FormElement.prototype = {
    get$length(receiver) {
      return receiver.length;
    }
  };
  A.Gamepad.prototype = {$isGamepad: 1};
  A.History.prototype = {
    get$length(receiver) {
      return receiver.length;
    }
  };
  A.HtmlCollection.prototype = {
    get$length(receiver) {
      return receiver.length;
    },
    $index(receiver, index) {
      var t1;
      A._asInt(index);
      t1 = receiver.length;
      if (index >>> 0 !== index || index >= t1)
        throw A.wrapException(A.IndexError$withLength(index, t1, receiver, null));
      return receiver[index];
    },
    $indexSet(receiver, index, value) {
      type$.Node._as(value);
      throw A.wrapException(A.UnsupportedError$("Cannot assign element of immutable List."));
    },
    elementAt$1(receiver, index) {
      if (!(index >= 0 && index < receiver.length))
        return A.ioore(receiver, index);
      return receiver[index];
    },
    $isJSIndexable: 1,
    $isEfficientLengthIterable: 1,
    $isJavaScriptIndexingBehavior: 1,
    $isIterable: 1,
    $isList: 1
  };
  A.ImageData.prototype = {$isImageData: 1};
  A.Location.prototype = {
    toString$0(receiver) {
      return String(receiver);
    }
  };
  A.MediaDeviceInfo.prototype = {
    get$kind(receiver) {
      return receiver.kind;
    }
  };
  A.MediaList.prototype = {
    get$length(receiver) {
      return receiver.length;
    }
  };
  A.MediaStreamTrack.prototype = {
    get$kind(receiver) {
      return receiver.kind;
    }
  };
  A.MessageEvent.prototype = {$isMessageEvent: 1};
  A.MessagePort.prototype = {$isMessagePort: 1};
  A.MidiInputMap.prototype = {
    $index(receiver, key) {
      return A.convertNativeToDart_Dictionary(receiver.get(A._asString(key)));
    },
    forEach$1(receiver, f) {
      var entries, entry;
      type$.void_Function_String_dynamic._as(f);
      entries = receiver.entries();
      for (; true;) {
        entry = entries.next();
        if (entry.done)
          return;
        f.call$2(entry.value[0], A.convertNativeToDart_Dictionary(entry.value[1]));
      }
    },
    get$keys(receiver) {
      var keys = A._setArrayType([], type$.JSArray_String);
      this.forEach$1(receiver, new A.MidiInputMap_keys_closure(keys));
      return keys;
    },
    get$length(receiver) {
      return receiver.size;
    },
    $isMap: 1
  };
  A.MidiInputMap_keys_closure.prototype = {
    call$2(k, v) {
      return B.JSArray_methods.add$1(this.keys, k);
    },
    $signature: 2
  };
  A.MidiOutputMap.prototype = {
    $index(receiver, key) {
      return A.convertNativeToDart_Dictionary(receiver.get(A._asString(key)));
    },
    forEach$1(receiver, f) {
      var entries, entry;
      type$.void_Function_String_dynamic._as(f);
      entries = receiver.entries();
      for (; true;) {
        entry = entries.next();
        if (entry.done)
          return;
        f.call$2(entry.value[0], A.convertNativeToDart_Dictionary(entry.value[1]));
      }
    },
    get$keys(receiver) {
      var keys = A._setArrayType([], type$.JSArray_String);
      this.forEach$1(receiver, new A.MidiOutputMap_keys_closure(keys));
      return keys;
    },
    get$length(receiver) {
      return receiver.size;
    },
    $isMap: 1
  };
  A.MidiOutputMap_keys_closure.prototype = {
    call$2(k, v) {
      return B.JSArray_methods.add$1(this.keys, k);
    },
    $signature: 2
  };
  A.MimeType.prototype = {$isMimeType: 1};
  A.MimeTypeArray.prototype = {
    get$length(receiver) {
      return receiver.length;
    },
    $index(receiver, index) {
      var t1;
      A._asInt(index);
      t1 = receiver.length;
      if (index >>> 0 !== index || index >= t1)
        throw A.wrapException(A.IndexError$withLength(index, t1, receiver, null));
      return receiver[index];
    },
    $indexSet(receiver, index, value) {
      type$.MimeType._as(value);
      throw A.wrapException(A.UnsupportedError$("Cannot assign element of immutable List."));
    },
    elementAt$1(receiver, index) {
      if (!(index >= 0 && index < receiver.length))
        return A.ioore(receiver, index);
      return receiver[index];
    },
    $isJSIndexable: 1,
    $isEfficientLengthIterable: 1,
    $isJavaScriptIndexingBehavior: 1,
    $isIterable: 1,
    $isList: 1
  };
  A.Node.prototype = {
    toString$0(receiver) {
      var value = receiver.nodeValue;
      return value == null ? this.super$Interceptor$toString(receiver) : value;
    },
    $isNode: 1
  };
  A.NodeList.prototype = {
    get$length(receiver) {
      return receiver.length;
    },
    $index(receiver, index) {
      var t1;
      A._asInt(index);
      t1 = receiver.length;
      if (index >>> 0 !== index || index >= t1)
        throw A.wrapException(A.IndexError$withLength(index, t1, receiver, null));
      return receiver[index];
    },
    $indexSet(receiver, index, value) {
      type$.Node._as(value);
      throw A.wrapException(A.UnsupportedError$("Cannot assign element of immutable List."));
    },
    elementAt$1(receiver, index) {
      if (!(index >= 0 && index < receiver.length))
        return A.ioore(receiver, index);
      return receiver[index];
    },
    $isJSIndexable: 1,
    $isEfficientLengthIterable: 1,
    $isJavaScriptIndexingBehavior: 1,
    $isIterable: 1,
    $isList: 1
  };
  A.Plugin.prototype = {
    get$length(receiver) {
      return receiver.length;
    },
    $isPlugin: 1
  };
  A.PluginArray.prototype = {
    get$length(receiver) {
      return receiver.length;
    },
    $index(receiver, index) {
      var t1;
      A._asInt(index);
      t1 = receiver.length;
      if (index >>> 0 !== index || index >= t1)
        throw A.wrapException(A.IndexError$withLength(index, t1, receiver, null));
      return receiver[index];
    },
    $indexSet(receiver, index, value) {
      type$.Plugin._as(value);
      throw A.wrapException(A.UnsupportedError$("Cannot assign element of immutable List."));
    },
    elementAt$1(receiver, index) {
      if (!(index >= 0 && index < receiver.length))
        return A.ioore(receiver, index);
      return receiver[index];
    },
    $isJSIndexable: 1,
    $isEfficientLengthIterable: 1,
    $isJavaScriptIndexingBehavior: 1,
    $isIterable: 1,
    $isList: 1
  };
  A.RtcStatsReport.prototype = {
    $index(receiver, key) {
      return A.convertNativeToDart_Dictionary(receiver.get(A._asString(key)));
    },
    forEach$1(receiver, f) {
      var entries, entry;
      type$.void_Function_String_dynamic._as(f);
      entries = receiver.entries();
      for (; true;) {
        entry = entries.next();
        if (entry.done)
          return;
        f.call$2(entry.value[0], A.convertNativeToDart_Dictionary(entry.value[1]));
      }
    },
    get$keys(receiver) {
      var keys = A._setArrayType([], type$.JSArray_String);
      this.forEach$1(receiver, new A.RtcStatsReport_keys_closure(keys));
      return keys;
    },
    get$length(receiver) {
      return receiver.size;
    },
    $isMap: 1
  };
  A.RtcStatsReport_keys_closure.prototype = {
    call$2(k, v) {
      return B.JSArray_methods.add$1(this.keys, k);
    },
    $signature: 2
  };
  A.SelectElement.prototype = {
    get$length(receiver) {
      return receiver.length;
    }
  };
  A.SharedArrayBuffer.prototype = {$isSharedArrayBuffer: 1};
  A.SourceBuffer.prototype = {$isSourceBuffer: 1};
  A.SourceBufferList.prototype = {
    get$length(receiver) {
      return receiver.length;
    },
    $index(receiver, index) {
      var t1;
      A._asInt(index);
      t1 = receiver.length;
      if (index >>> 0 !== index || index >= t1)
        throw A.wrapException(A.IndexError$withLength(index, t1, receiver, null));
      return receiver[index];
    },
    $indexSet(receiver, index, value) {
      type$.SourceBuffer._as(value);
      throw A.wrapException(A.UnsupportedError$("Cannot assign element of immutable List."));
    },
    elementAt$1(receiver, index) {
      if (!(index >= 0 && index < receiver.length))
        return A.ioore(receiver, index);
      return receiver[index];
    },
    $isJSIndexable: 1,
    $isEfficientLengthIterable: 1,
    $isJavaScriptIndexingBehavior: 1,
    $isIterable: 1,
    $isList: 1
  };
  A.SpeechGrammar.prototype = {$isSpeechGrammar: 1};
  A.SpeechGrammarList.prototype = {
    get$length(receiver) {
      return receiver.length;
    },
    $index(receiver, index) {
      var t1;
      A._asInt(index);
      t1 = receiver.length;
      if (index >>> 0 !== index || index >= t1)
        throw A.wrapException(A.IndexError$withLength(index, t1, receiver, null));
      return receiver[index];
    },
    $indexSet(receiver, index, value) {
      type$.SpeechGrammar._as(value);
      throw A.wrapException(A.UnsupportedError$("Cannot assign element of immutable List."));
    },
    elementAt$1(receiver, index) {
      if (!(index >= 0 && index < receiver.length))
        return A.ioore(receiver, index);
      return receiver[index];
    },
    $isJSIndexable: 1,
    $isEfficientLengthIterable: 1,
    $isJavaScriptIndexingBehavior: 1,
    $isIterable: 1,
    $isList: 1
  };
  A.SpeechRecognitionResult.prototype = {
    get$length(receiver) {
      return receiver.length;
    },
    $isSpeechRecognitionResult: 1
  };
  A.Storage.prototype = {
    $index(receiver, key) {
      return receiver.getItem(A._asString(key));
    },
    forEach$1(receiver, f) {
      var i, key, t1;
      type$.void_Function_String_String._as(f);
      for (i = 0; true; ++i) {
        key = receiver.key(i);
        if (key == null)
          return;
        t1 = receiver.getItem(key);
        t1.toString;
        f.call$2(key, t1);
      }
    },
    get$keys(receiver) {
      var keys = A._setArrayType([], type$.JSArray_String);
      this.forEach$1(receiver, new A.Storage_keys_closure(keys));
      return keys;
    },
    get$length(receiver) {
      return receiver.length;
    },
    $isMap: 1
  };
  A.Storage_keys_closure.prototype = {
    call$2(k, v) {
      return B.JSArray_methods.add$1(this.keys, k);
    },
    $signature: 18
  };
  A.StyleSheet.prototype = {$isStyleSheet: 1};
  A.TextTrack.prototype = {
    get$kind(receiver) {
      return receiver.kind;
    },
    $isTextTrack: 1
  };
  A.TextTrackCue.prototype = {$isTextTrackCue: 1};
  A.TextTrackCueList.prototype = {
    get$length(receiver) {
      return receiver.length;
    },
    $index(receiver, index) {
      var t1;
      A._asInt(index);
      t1 = receiver.length;
      if (index >>> 0 !== index || index >= t1)
        throw A.wrapException(A.IndexError$withLength(index, t1, receiver, null));
      return receiver[index];
    },
    $indexSet(receiver, index, value) {
      type$.TextTrackCue._as(value);
      throw A.wrapException(A.UnsupportedError$("Cannot assign element of immutable List."));
    },
    elementAt$1(receiver, index) {
      if (!(index >= 0 && index < receiver.length))
        return A.ioore(receiver, index);
      return receiver[index];
    },
    $isJSIndexable: 1,
    $isEfficientLengthIterable: 1,
    $isJavaScriptIndexingBehavior: 1,
    $isIterable: 1,
    $isList: 1
  };
  A.TextTrackList.prototype = {
    get$length(receiver) {
      return receiver.length;
    },
    $index(receiver, index) {
      var t1;
      A._asInt(index);
      t1 = receiver.length;
      if (index >>> 0 !== index || index >= t1)
        throw A.wrapException(A.IndexError$withLength(index, t1, receiver, null));
      return receiver[index];
    },
    $indexSet(receiver, index, value) {
      type$.TextTrack._as(value);
      throw A.wrapException(A.UnsupportedError$("Cannot assign element of immutable List."));
    },
    elementAt$1(receiver, index) {
      if (!(index >= 0 && index < receiver.length))
        return A.ioore(receiver, index);
      return receiver[index];
    },
    $isJSIndexable: 1,
    $isEfficientLengthIterable: 1,
    $isJavaScriptIndexingBehavior: 1,
    $isIterable: 1,
    $isList: 1
  };
  A.TimeRanges.prototype = {
    get$length(receiver) {
      return receiver.length;
    }
  };
  A.Touch.prototype = {$isTouch: 1};
  A.TouchList.prototype = {
    get$length(receiver) {
      return receiver.length;
    },
    $index(receiver, index) {
      var t1;
      A._asInt(index);
      t1 = receiver.length;
      if (index >>> 0 !== index || index >= t1)
        throw A.wrapException(A.IndexError$withLength(index, t1, receiver, null));
      return receiver[index];
    },
    $indexSet(receiver, index, value) {
      type$.Touch._as(value);
      throw A.wrapException(A.UnsupportedError$("Cannot assign element of immutable List."));
    },
    elementAt$1(receiver, index) {
      if (!(index >= 0 && index < receiver.length))
        return A.ioore(receiver, index);
      return receiver[index];
    },
    $isJSIndexable: 1,
    $isEfficientLengthIterable: 1,
    $isJavaScriptIndexingBehavior: 1,
    $isIterable: 1,
    $isList: 1
  };
  A.TrackDefaultList.prototype = {
    get$length(receiver) {
      return receiver.length;
    }
  };
  A.TrackElement.prototype = {
    get$kind(receiver) {
      return receiver.kind;
    }
  };
  A.Url.prototype = {
    toString$0(receiver) {
      return String(receiver);
    }
  };
  A.VideoTrack.prototype = {
    get$kind(receiver) {
      return receiver.kind;
    }
  };
  A.VideoTrackList.prototype = {
    get$length(receiver) {
      return receiver.length;
    }
  };
  A.WorkerGlobalScope.prototype = {};
  A._CssRuleList.prototype = {
    get$length(receiver) {
      return receiver.length;
    },
    $index(receiver, index) {
      var t1;
      A._asInt(index);
      t1 = receiver.length;
      if (index >>> 0 !== index || index >= t1)
        throw A.wrapException(A.IndexError$withLength(index, t1, receiver, null));
      return receiver[index];
    },
    $indexSet(receiver, index, value) {
      type$.CssRule._as(value);
      throw A.wrapException(A.UnsupportedError$("Cannot assign element of immutable List."));
    },
    elementAt$1(receiver, index) {
      if (!(index >= 0 && index < receiver.length))
        return A.ioore(receiver, index);
      return receiver[index];
    },
    $isJSIndexable: 1,
    $isEfficientLengthIterable: 1,
    $isJavaScriptIndexingBehavior: 1,
    $isIterable: 1,
    $isList: 1
  };
  A._DomRect.prototype = {
    toString$0(receiver) {
      var t2, t3, t4,
        t1 = receiver.left;
      t1.toString;
      t2 = receiver.top;
      t2.toString;
      t3 = receiver.width;
      t3.toString;
      t4 = receiver.height;
      t4.toString;
      return "Rectangle (" + A.S(t1) + ", " + A.S(t2) + ") " + A.S(t3) + " x " + A.S(t4);
    },
    $eq(receiver, other) {
      var t1, t2;
      if (other == null)
        return false;
      if (type$.Rectangle_num._is(other)) {
        t1 = receiver.left;
        t1.toString;
        t2 = other.left;
        t2.toString;
        if (t1 === t2) {
          t1 = receiver.top;
          t1.toString;
          t2 = other.top;
          t2.toString;
          if (t1 === t2) {
            t1 = receiver.width;
            t1.toString;
            t2 = J.getInterceptor$x(other);
            if (t1 === t2.get$width(other)) {
              t1 = receiver.height;
              t1.toString;
              t2 = t1 === t2.get$height(other);
              t1 = t2;
            } else
              t1 = false;
          } else
            t1 = false;
        } else
          t1 = false;
      } else
        t1 = false;
      return t1;
    },
    get$hashCode(receiver) {
      var t2, t3, t4,
        t1 = receiver.left;
      t1.toString;
      t2 = receiver.top;
      t2.toString;
      t3 = receiver.width;
      t3.toString;
      t4 = receiver.height;
      t4.toString;
      return A.Object_hash(t1, t2, t3, t4);
    },
    get$_height(receiver) {
      return receiver.height;
    },
    get$height(receiver) {
      var t1 = receiver.height;
      t1.toString;
      return t1;
    },
    get$_width(receiver) {
      return receiver.width;
    },
    get$width(receiver) {
      var t1 = receiver.width;
      t1.toString;
      return t1;
    }
  };
  A._GamepadList.prototype = {
    get$length(receiver) {
      return receiver.length;
    },
    $index(receiver, index) {
      var t1;
      A._asInt(index);
      t1 = receiver.length;
      if (index >>> 0 !== index || index >= t1)
        throw A.wrapException(A.IndexError$withLength(index, t1, receiver, null));
      return receiver[index];
    },
    $indexSet(receiver, index, value) {
      type$.nullable_Gamepad._as(value);
      throw A.wrapException(A.UnsupportedError$("Cannot assign element of immutable List."));
    },
    elementAt$1(receiver, index) {
      if (!(index >= 0 && index < receiver.length))
        return A.ioore(receiver, index);
      return receiver[index];
    },
    $isJSIndexable: 1,
    $isEfficientLengthIterable: 1,
    $isJavaScriptIndexingBehavior: 1,
    $isIterable: 1,
    $isList: 1
  };
  A._NamedNodeMap.prototype = {
    get$length(receiver) {
      return receiver.length;
    },
    $index(receiver, index) {
      var t1;
      A._asInt(index);
      t1 = receiver.length;
      if (index >>> 0 !== index || index >= t1)
        throw A.wrapException(A.IndexError$withLength(index, t1, receiver, null));
      return receiver[index];
    },
    $indexSet(receiver, index, value) {
      type$.Node._as(value);
      throw A.wrapException(A.UnsupportedError$("Cannot assign element of immutable List."));
    },
    elementAt$1(receiver, index) {
      if (!(index >= 0 && index < receiver.length))
        return A.ioore(receiver, index);
      return receiver[index];
    },
    $isJSIndexable: 1,
    $isEfficientLengthIterable: 1,
    $isJavaScriptIndexingBehavior: 1,
    $isIterable: 1,
    $isList: 1
  };
  A._SpeechRecognitionResultList.prototype = {
    get$length(receiver) {
      return receiver.length;
    },
    $index(receiver, index) {
      var t1;
      A._asInt(index);
      t1 = receiver.length;
      if (index >>> 0 !== index || index >= t1)
        throw A.wrapException(A.IndexError$withLength(index, t1, receiver, null));
      return receiver[index];
    },
    $indexSet(receiver, index, value) {
      type$.SpeechRecognitionResult._as(value);
      throw A.wrapException(A.UnsupportedError$("Cannot assign element of immutable List."));
    },
    elementAt$1(receiver, index) {
      if (!(index >= 0 && index < receiver.length))
        return A.ioore(receiver, index);
      return receiver[index];
    },
    $isJSIndexable: 1,
    $isEfficientLengthIterable: 1,
    $isJavaScriptIndexingBehavior: 1,
    $isIterable: 1,
    $isList: 1
  };
  A._StyleSheetList.prototype = {
    get$length(receiver) {
      return receiver.length;
    },
    $index(receiver, index) {
      var t1;
      A._asInt(index);
      t1 = receiver.length;
      if (index >>> 0 !== index || index >= t1)
        throw A.wrapException(A.IndexError$withLength(index, t1, receiver, null));
      return receiver[index];
    },
    $indexSet(receiver, index, value) {
      type$.StyleSheet._as(value);
      throw A.wrapException(A.UnsupportedError$("Cannot assign element of immutable List."));
    },
    elementAt$1(receiver, index) {
      if (!(index >= 0 && index < receiver.length))
        return A.ioore(receiver, index);
      return receiver[index];
    },
    $isJSIndexable: 1,
    $isEfficientLengthIterable: 1,
    $isJavaScriptIndexingBehavior: 1,
    $isIterable: 1,
    $isList: 1
  };
  A.EventStreamProvider.prototype = {};
  A._EventStream.prototype = {};
  A._EventStreamSubscription.prototype = {};
  A._EventStreamSubscription_closure.prototype = {
    call$1(e) {
      return this.onData.call$1(type$.Event._as(e));
    },
    $signature: 19
  };
  A.ImmutableListMixin.prototype = {
    get$iterator(receiver) {
      return new A.FixedSizeListIterator(receiver, this.get$length(receiver), A.instanceType(receiver)._eval$1("FixedSizeListIterator<ImmutableListMixin.E>"));
    }
  };
  A.FixedSizeListIterator.prototype = {
    moveNext$0() {
      var _this = this,
        nextPosition = _this._position + 1,
        t1 = _this._html$_length;
      if (nextPosition < t1) {
        _this.set$_html$_current(J.$index$asx(_this._array, nextPosition));
        _this._position = nextPosition;
        return true;
      }
      _this.set$_html$_current(null);
      _this._position = t1;
      return false;
    },
    get$current(_) {
      var t1 = this._html$_current;
      return t1 == null ? this.$ti._precomputed1._as(t1) : t1;
    },
    set$_html$_current(_current) {
      this._html$_current = this.$ti._eval$1("1?")._as(_current);
    },
    $isIterator: 1
  };
  A._CssStyleDeclaration_JavaScriptObject_CssStyleDeclarationBase.prototype = {};
  A._DomRectList_JavaScriptObject_ListMixin.prototype = {};
  A._DomRectList_JavaScriptObject_ListMixin_ImmutableListMixin.prototype = {};
  A._DomStringList_JavaScriptObject_ListMixin.prototype = {};
  A._DomStringList_JavaScriptObject_ListMixin_ImmutableListMixin.prototype = {};
  A._FileList_JavaScriptObject_ListMixin.prototype = {};
  A._FileList_JavaScriptObject_ListMixin_ImmutableListMixin.prototype = {};
  A._HtmlCollection_JavaScriptObject_ListMixin.prototype = {};
  A._HtmlCollection_JavaScriptObject_ListMixin_ImmutableListMixin.prototype = {};
  A._MidiInputMap_JavaScriptObject_MapMixin.prototype = {};
  A._MidiOutputMap_JavaScriptObject_MapMixin.prototype = {};
  A._MimeTypeArray_JavaScriptObject_ListMixin.prototype = {};
  A._MimeTypeArray_JavaScriptObject_ListMixin_ImmutableListMixin.prototype = {};
  A._NodeList_JavaScriptObject_ListMixin.prototype = {};
  A._NodeList_JavaScriptObject_ListMixin_ImmutableListMixin.prototype = {};
  A._PluginArray_JavaScriptObject_ListMixin.prototype = {};
  A._PluginArray_JavaScriptObject_ListMixin_ImmutableListMixin.prototype = {};
  A._RtcStatsReport_JavaScriptObject_MapMixin.prototype = {};
  A._SourceBufferList_EventTarget_ListMixin.prototype = {};
  A._SourceBufferList_EventTarget_ListMixin_ImmutableListMixin.prototype = {};
  A._SpeechGrammarList_JavaScriptObject_ListMixin.prototype = {};
  A._SpeechGrammarList_JavaScriptObject_ListMixin_ImmutableListMixin.prototype = {};
  A._Storage_JavaScriptObject_MapMixin.prototype = {};
  A._TextTrackCueList_JavaScriptObject_ListMixin.prototype = {};
  A._TextTrackCueList_JavaScriptObject_ListMixin_ImmutableListMixin.prototype = {};
  A._TextTrackList_EventTarget_ListMixin.prototype = {};
  A._TextTrackList_EventTarget_ListMixin_ImmutableListMixin.prototype = {};
  A._TouchList_JavaScriptObject_ListMixin.prototype = {};
  A._TouchList_JavaScriptObject_ListMixin_ImmutableListMixin.prototype = {};
  A.__CssRuleList_JavaScriptObject_ListMixin.prototype = {};
  A.__CssRuleList_JavaScriptObject_ListMixin_ImmutableListMixin.prototype = {};
  A.__GamepadList_JavaScriptObject_ListMixin.prototype = {};
  A.__GamepadList_JavaScriptObject_ListMixin_ImmutableListMixin.prototype = {};
  A.__NamedNodeMap_JavaScriptObject_ListMixin.prototype = {};
  A.__NamedNodeMap_JavaScriptObject_ListMixin_ImmutableListMixin.prototype = {};
  A.__SpeechRecognitionResultList_JavaScriptObject_ListMixin.prototype = {};
  A.__SpeechRecognitionResultList_JavaScriptObject_ListMixin_ImmutableListMixin.prototype = {};
  A.__StyleSheetList_JavaScriptObject_ListMixin.prototype = {};
  A.__StyleSheetList_JavaScriptObject_ListMixin_ImmutableListMixin.prototype = {};
  A._StructuredClone.prototype = {
    findSlot$1(value) {
      var i,
        t1 = this.values,
        $length = t1.length;
      for (i = 0; i < $length; ++i)
        if (t1[i] === value)
          return i;
      B.JSArray_methods.add$1(t1, value);
      B.JSArray_methods.add$1(this.copies, null);
      return $length;
    },
    walk$1(e) {
      var slot, t2, copy, _this = this, t1 = {};
      if (e == null)
        return e;
      if (A._isBool(e))
        return e;
      if (typeof e == "number")
        return e;
      if (typeof e == "string")
        return e;
      if (e instanceof A.DateTime)
        return new Date(e._value);
      if (type$.File._is(e))
        return e;
      if (type$.Blob._is(e))
        return e;
      if (type$.FileList._is(e))
        return e;
      if (type$.ImageData._is(e))
        return e;
      if (type$.NativeByteBuffer._is(e) || type$.NativeTypedData._is(e) || type$.MessagePort._is(e) || type$.SharedArrayBuffer._is(e))
        return e;
      if (type$.Map_dynamic_dynamic._is(e)) {
        slot = _this.findSlot$1(e);
        t2 = _this.copies;
        if (!(slot < t2.length))
          return A.ioore(t2, slot);
        copy = t1.copy = t2[slot];
        if (copy != null)
          return copy;
        copy = {};
        t1.copy = copy;
        B.JSArray_methods.$indexSet(t2, slot, copy);
        J.forEach$1$x(e, new A._StructuredClone_walk_closure(t1, _this));
        return t1.copy;
      }
      if (type$.List_dynamic._is(e)) {
        slot = _this.findSlot$1(e);
        t1 = _this.copies;
        if (!(slot < t1.length))
          return A.ioore(t1, slot);
        copy = t1[slot];
        if (copy != null)
          return copy;
        return _this.copyList$2(e, slot);
      }
      if (type$.JSObject._is(e)) {
        slot = _this.findSlot$1(e);
        t2 = _this.copies;
        if (!(slot < t2.length))
          return A.ioore(t2, slot);
        copy = t1.copy = t2[slot];
        if (copy != null)
          return copy;
        copy = {};
        t1.copy = copy;
        B.JSArray_methods.$indexSet(t2, slot, copy);
        _this.forEachObjectKey$2(e, new A._StructuredClone_walk_closure0(t1, _this));
        return t1.copy;
      }
      throw A.wrapException(A.UnimplementedError$("structured clone of other type"));
    },
    copyList$2(e, slot) {
      var i,
        t1 = J.getInterceptor$asx(e),
        $length = t1.get$length(e),
        copy = new Array($length);
      B.JSArray_methods.$indexSet(this.copies, slot, copy);
      for (i = 0; i < $length; ++i)
        B.JSArray_methods.$indexSet(copy, i, this.walk$1(t1.$index(e, i)));
      return copy;
    }
  };
  A._StructuredClone_walk_closure.prototype = {
    call$2(key, value) {
      this._box_0.copy[key] = this.$this.walk$1(value);
    },
    $signature: 20
  };
  A._StructuredClone_walk_closure0.prototype = {
    call$2(key, value) {
      this._box_0.copy[key] = this.$this.walk$1(value);
    },
    $signature: 21
  };
  A._AcceptStructuredClone.prototype = {
    findSlot$1(value) {
      var i,
        t1 = this.values,
        $length = t1.length;
      for (i = 0; i < $length; ++i)
        if (t1[i] === value)
          return i;
      B.JSArray_methods.add$1(t1, value);
      B.JSArray_methods.add$1(this.copies, null);
      return $length;
    },
    walk$1(e) {
      var millisSinceEpoch, t1, slot, copy, t2, map, l, $length, i, _this = this;
      if (e == null)
        return e;
      if (A._isBool(e))
        return e;
      if (typeof e == "number")
        return e;
      if (typeof e == "string")
        return e;
      if (e instanceof Date) {
        millisSinceEpoch = e.getTime();
        if (Math.abs(millisSinceEpoch) <= 864e13)
          t1 = false;
        else
          t1 = true;
        if (t1)
          A.throwExpression(A.ArgumentError$("DateTime is outside valid range: " + millisSinceEpoch, null));
        A.checkNotNullable(true, "isUtc", type$.bool);
        return new A.DateTime(millisSinceEpoch, true);
      }
      if (e instanceof RegExp)
        throw A.wrapException(A.UnimplementedError$("structured clone of RegExp"));
      if (typeof Promise != "undefined" && e instanceof Promise)
        return A.promiseToFuture(e, type$.dynamic);
      if (A.isJavaScriptSimpleObject(e)) {
        slot = _this.findSlot$1(e);
        t1 = _this.copies;
        if (!(slot < t1.length))
          return A.ioore(t1, slot);
        copy = t1[slot];
        if (copy != null)
          return copy;
        t2 = type$.dynamic;
        map = A.LinkedHashMap_LinkedHashMap$_empty(t2, t2);
        B.JSArray_methods.$indexSet(t1, slot, map);
        _this.forEachJsField$2(e, new A._AcceptStructuredClone_walk_closure(_this, map));
        return map;
      }
      if (e instanceof Array) {
        l = e;
        slot = _this.findSlot$1(l);
        t1 = _this.copies;
        if (!(slot < t1.length))
          return A.ioore(t1, slot);
        copy = t1[slot];
        if (copy != null)
          return copy;
        t2 = J.getInterceptor$asx(l);
        $length = t2.get$length(l);
        copy = _this.mustCopy ? new Array($length) : l;
        B.JSArray_methods.$indexSet(t1, slot, copy);
        for (t1 = J.getInterceptor$ax(copy), i = 0; i < $length; ++i)
          t1.$indexSet(copy, i, _this.walk$1(t2.$index(l, i)));
        return copy;
      }
      return e;
    }
  };
  A._AcceptStructuredClone_walk_closure.prototype = {
    call$2(key, value) {
      var t1 = this.$this.walk$1(value);
      this.map.$indexSet(0, key, t1);
      return t1;
    },
    $signature: 22
  };
  A._StructuredCloneDart2Js.prototype = {
    forEachObjectKey$2(object, action) {
      var t1, t2, _i, key;
      type$.dynamic_Function_dynamic_dynamic._as(action);
      for (t1 = Object.keys(object), t2 = t1.length, _i = 0; _i < t2; ++_i) {
        key = t1[_i];
        action.call$2(key, object[key]);
      }
    }
  };
  A._AcceptStructuredCloneDart2Js.prototype = {
    forEachJsField$2(object, action) {
      var t1, t2, _i, key;
      type$.dynamic_Function_dynamic_dynamic._as(action);
      for (t1 = Object.keys(object), t2 = t1.length, _i = 0; _i < t1.length; t1.length === t2 || (0, A.throwConcurrentModificationError)(t1), ++_i) {
        key = t1[_i];
        action.call$2(key, object[key]);
      }
    }
  };
  A._convertDataTree__convert.prototype = {
    call$1(o) {
      var convertedMap, t2, key, convertedList,
        t1 = this._convertedObjects;
      if (t1.containsKey$1(0, o))
        return t1.$index(0, o);
      if (type$.Map_dynamic_dynamic._is(o)) {
        convertedMap = {};
        t1.$indexSet(0, o, convertedMap);
        for (t1 = J.getInterceptor$x(o), t2 = J.get$iterator$ax(t1.get$keys(o)); t2.moveNext$0();) {
          key = t2.get$current(t2);
          convertedMap[key] = this.call$1(t1.$index(o, key));
        }
        return convertedMap;
      } else if (type$.Iterable_dynamic._is(o)) {
        convertedList = [];
        t1.$indexSet(0, o, convertedList);
        B.JSArray_methods.addAll$1(convertedList, J.map$1$1$ax(o, this, type$.dynamic));
        return convertedList;
      } else
        return o;
    },
    $signature: 23
  };
  A.promiseToFuture_closure.prototype = {
    call$1(r) {
      return this.completer.complete$1(0, this.T._eval$1("0/?")._as(r));
    },
    $signature: 4
  };
  A.promiseToFuture_closure0.prototype = {
    call$1(e) {
      if (e == null)
        return this.completer.completeError$1(new A.NullRejectionException(e === undefined));
      return this.completer.completeError$1(e);
    },
    $signature: 4
  };
  A.NullRejectionException.prototype = {
    toString$0(_) {
      return "Promise was rejected with a value of `" + (this.isUndefined ? "undefined" : "null") + "`.";
    }
  };
  A._JSSecureRandom.prototype = {
    _JSSecureRandom$0() {
      var $crypto = self.crypto;
      if ($crypto != null)
        if ($crypto.getRandomValues != null)
          return;
      throw A.wrapException(A.UnsupportedError$("No source of cryptographically secure random numbers available."));
    },
    nextInt$1(max) {
      var byteCount, t1, start, randomLimit, t2, t3, t4, random, result, _null = null;
      if (max <= 0 || max > 4294967296)
        throw A.wrapException(new A.RangeError(_null, _null, false, _null, _null, "max must be in range 0 < max \u2264 2^32, was " + max));
      if (max > 255)
        if (max > 65535)
          byteCount = max > 16777215 ? 4 : 3;
        else
          byteCount = 2;
      else
        byteCount = 1;
      t1 = this._math$_buffer;
      B.NativeByteData_methods._setUint32$3(t1, 0, 0, false);
      start = 4 - byteCount;
      randomLimit = A._asInt(Math.pow(256, byteCount));
      for (t2 = max - 1, t3 = (max & t2) === 0; true;) {
        t4 = t1.buffer;
        t4 = new Uint8Array(t4, start, byteCount);
        crypto.getRandomValues(t4);
        random = B.NativeByteData_methods._getUint32$2(t1, 0, false);
        if (t3)
          return (random & t2) >>> 0;
        result = random % max;
        if (random - result + max < randomLimit)
          return result;
      }
    }
  };
  A.Length.prototype = {$isLength: 1};
  A.LengthList.prototype = {
    get$length(receiver) {
      return receiver.length;
    },
    $index(receiver, index) {
      A._asInt(index);
      if (index >>> 0 !== index || index >= receiver.length)
        throw A.wrapException(A.IndexError$withLength(index, this.get$length(receiver), receiver, null));
      return receiver.getItem(index);
    },
    $indexSet(receiver, index, value) {
      type$.Length._as(value);
      throw A.wrapException(A.UnsupportedError$("Cannot assign element of immutable List."));
    },
    elementAt$1(receiver, index) {
      return this.$index(receiver, index);
    },
    $isEfficientLengthIterable: 1,
    $isIterable: 1,
    $isList: 1
  };
  A.Number.prototype = {$isNumber: 1};
  A.NumberList.prototype = {
    get$length(receiver) {
      return receiver.length;
    },
    $index(receiver, index) {
      A._asInt(index);
      if (index >>> 0 !== index || index >= receiver.length)
        throw A.wrapException(A.IndexError$withLength(index, this.get$length(receiver), receiver, null));
      return receiver.getItem(index);
    },
    $indexSet(receiver, index, value) {
      type$.Number._as(value);
      throw A.wrapException(A.UnsupportedError$("Cannot assign element of immutable List."));
    },
    elementAt$1(receiver, index) {
      return this.$index(receiver, index);
    },
    $isEfficientLengthIterable: 1,
    $isIterable: 1,
    $isList: 1
  };
  A.PointList.prototype = {
    get$length(receiver) {
      return receiver.length;
    }
  };
  A.StringList.prototype = {
    get$length(receiver) {
      return receiver.length;
    },
    $index(receiver, index) {
      A._asInt(index);
      if (index >>> 0 !== index || index >= receiver.length)
        throw A.wrapException(A.IndexError$withLength(index, this.get$length(receiver), receiver, null));
      return receiver.getItem(index);
    },
    $indexSet(receiver, index, value) {
      A._asString(value);
      throw A.wrapException(A.UnsupportedError$("Cannot assign element of immutable List."));
    },
    elementAt$1(receiver, index) {
      return this.$index(receiver, index);
    },
    $isEfficientLengthIterable: 1,
    $isIterable: 1,
    $isList: 1
  };
  A.Transform.prototype = {$isTransform: 1};
  A.TransformList.prototype = {
    get$length(receiver) {
      return receiver.length;
    },
    $index(receiver, index) {
      A._asInt(index);
      if (index >>> 0 !== index || index >= receiver.length)
        throw A.wrapException(A.IndexError$withLength(index, this.get$length(receiver), receiver, null));
      return receiver.getItem(index);
    },
    $indexSet(receiver, index, value) {
      type$.Transform._as(value);
      throw A.wrapException(A.UnsupportedError$("Cannot assign element of immutable List."));
    },
    elementAt$1(receiver, index) {
      return this.$index(receiver, index);
    },
    $isEfficientLengthIterable: 1,
    $isIterable: 1,
    $isList: 1
  };
  A._LengthList_JavaScriptObject_ListMixin.prototype = {};
  A._LengthList_JavaScriptObject_ListMixin_ImmutableListMixin.prototype = {};
  A._NumberList_JavaScriptObject_ListMixin.prototype = {};
  A._NumberList_JavaScriptObject_ListMixin_ImmutableListMixin.prototype = {};
  A._StringList_JavaScriptObject_ListMixin.prototype = {};
  A._StringList_JavaScriptObject_ListMixin_ImmutableListMixin.prototype = {};
  A._TransformList_JavaScriptObject_ListMixin.prototype = {};
  A._TransformList_JavaScriptObject_ListMixin_ImmutableListMixin.prototype = {};
  A.AudioBuffer.prototype = {
    get$length(receiver) {
      return receiver.length;
    }
  };
  A.AudioParamMap.prototype = {
    $index(receiver, key) {
      return A.convertNativeToDart_Dictionary(receiver.get(A._asString(key)));
    },
    forEach$1(receiver, f) {
      var entries, entry;
      type$.void_Function_String_dynamic._as(f);
      entries = receiver.entries();
      for (; true;) {
        entry = entries.next();
        if (entry.done)
          return;
        f.call$2(entry.value[0], A.convertNativeToDart_Dictionary(entry.value[1]));
      }
    },
    get$keys(receiver) {
      var keys = A._setArrayType([], type$.JSArray_String);
      this.forEach$1(receiver, new A.AudioParamMap_keys_closure(keys));
      return keys;
    },
    get$length(receiver) {
      return receiver.size;
    },
    $isMap: 1
  };
  A.AudioParamMap_keys_closure.prototype = {
    call$2(k, v) {
      return B.JSArray_methods.add$1(this.keys, k);
    },
    $signature: 2
  };
  A.AudioTrack.prototype = {
    get$kind(receiver) {
      return receiver.kind;
    }
  };
  A.AudioTrackList.prototype = {
    get$length(receiver) {
      return receiver.length;
    }
  };
  A.BaseAudioContext.prototype = {};
  A.OfflineAudioContext.prototype = {
    get$length(receiver) {
      return receiver.length;
    }
  };
  A._AudioParamMap_JavaScriptObject_MapMixin.prototype = {};
  A.WritableStream.prototype = {};
  A.ReadableStream.prototype = {};
  A.TransformStream.prototype = {};
  A.TransformStreamDefaultController.prototype = {};
  A.EncodedStreams.prototype = {};
  A.RTCEncodedFrame.prototype = {};
  A.RTCEncodedAudioFrame.prototype = {};
  A.RTCEncodedVideoFrame.prototype = {};
  A.RTCEncodedFrameMetadata.prototype = {};
  A.RTCEncodedAudioFrameMetadata.prototype = {};
  A.RTCEncodedVideoFrameMetadata.prototype = {};
  A.RTCTransformEvent.prototype = {};
  A.RTCRtpScriptTransformer.prototype = {};
  A.RTCRtpScriptTransform.prototype = {};
  A.Promise.prototype = {};
  A.Algorithm.prototype = {};
  A.AesGcmParams.prototype = {};
  A.KeyOptions.prototype = {
    toString$0(_) {
      return "KeyOptions{sharedKey: " + this.sharedKey + ", ratchetWindowSize: " + this.ratchetWindowSize + "}";
    }
  };
  A.CryptorError.prototype = {
    _enumToString$0() {
      return "CryptorError." + this._name;
    }
  };
  A.KeySet.prototype = {};
  A.Cryptor.prototype = {
    get$kind(_) {
      var t1 = this.__Cryptor_kind_A;
      t1 === $ && A.throwLateFieldNI("kind");
      return t1;
    },
    ratchetKey$1(keyIndex) {
      var $async$goto = 0,
        $async$completer = A._makeAsyncAwaitCompleter(type$.void),
        $async$returnValue, $async$self = this, t1, currentMaterial;
      var $async$ratchetKey$1 = A._wrapJsFunctionForAsync(function($async$errorCode, $async$result) {
        if ($async$errorCode === 1)
          return A._asyncRethrow($async$result, $async$completer);
        while (true)
          switch ($async$goto) {
            case 0:
              // Function start
              if ($async$self._ratchetCompleter == null) {
                $async$self._ratchetCompleter = new A._AsyncCompleter(new A._Future($.Zone__current, type$._Future_void), type$._AsyncCompleter_void);
                t1 = $async$self.getKeySet$1(keyIndex);
                currentMaterial = t1 == null ? null : t1.material;
                if (currentMaterial == null) {
                  $async$self._ratchetCompleter.complete$0(0);
                  $async$self._ratchetCompleter = null;
                  // goto return
                  $async$goto = 1;
                  break;
                }
                $async$self.ratchetMaterial$1(currentMaterial).then$1$1(new A.Cryptor_ratchetKey_closure($async$self, keyIndex), type$.Null);
              }
              $async$returnValue = $async$self._ratchetCompleter.future;
              // goto return
              $async$goto = 1;
              break;
            case 1:
              // return
              return A._asyncReturn($async$returnValue, $async$completer);
          }
      });
      return A._asyncStartSync($async$ratchetKey$1, $async$completer);
    },
    ratchetMaterial$1(currentMaterial) {
      var $async$goto = 0,
        $async$completer = A._makeAsyncAwaitCompleter(type$.CryptoKey),
        $async$returnValue, $async$self = this, $async$temp1, $async$temp2, $async$temp3, $async$temp4;
      var $async$ratchetMaterial$1 = A._wrapJsFunctionForAsync(function($async$errorCode, $async$result) {
        if ($async$errorCode === 1)
          return A._asyncRethrow($async$result, $async$completer);
        while (true)
          switch ($async$goto) {
            case 0:
              // Function start
              $async$temp1 = type$.FutureOr_CryptoKey;
              $async$temp2 = A;
              $async$temp3 = self.crypto.subtle;
              $async$temp4 = A;
              $async$goto = 4;
              return A._asyncAwait($async$self.ratchet$2(currentMaterial, $async$self.keyOptions.ratchetSalt), $async$ratchetMaterial$1);
            case 4:
              // returning from await.
              $async$goto = 3;
              return A._asyncAwait($async$temp2.promiseToFuture($async$temp3.importKey("raw", $async$temp4.jsArrayBufferFrom($async$result), J.get$name$x(type$.Algorithm._as(currentMaterial.algorithm)), false, A._setArrayType(["deriveBits", "deriveKey"], type$.JSArray_String)), type$.dynamic), $async$ratchetMaterial$1);
            case 3:
              // returning from await.
              $async$returnValue = $async$temp1._as($async$result);
              // goto return
              $async$goto = 1;
              break;
            case 1:
              // return
              return A._asyncReturn($async$returnValue, $async$completer);
          }
      });
      return A._asyncStartSync($async$ratchetMaterial$1, $async$completer);
    },
    getKeySet$1(keyIndex) {
      var t1 = this.cryptoKeyRing,
        t2 = keyIndex == null ? this.currentKeyIndex : keyIndex;
      if (!(t2 >= 0 && t2 < 16))
        return A.ioore(t1, t2);
      return t1[t2];
    },
    setKey$2(keyIndex, key) {
      var $async$goto = 0,
        $async$completer = A._makeAsyncAwaitCompleter(type$.void),
        $async$self = this;
      var $async$setKey$2 = A._wrapJsFunctionForAsync(function($async$errorCode, $async$result) {
        if ($async$errorCode === 1)
          return A._asyncRethrow($async$result, $async$completer);
        while (true)
          switch ($async$goto) {
            case 0:
              // Function start
              if ($async$self.lastError !== B.CryptorError_1) {
                A.print("setKey: lastError != CryptorError.kOk, reset state to kNew");
                $async$self.lastError = B.CryptorError_0;
              }
              $async$goto = 4;
              return A._asyncAwait(A.impportKeyFromRawData(key, A._setArrayType(["deriveBits", "deriveKey"], type$.JSArray_String), "PBKDF2"), $async$setKey$2);
            case 4:
              // returning from await.
              $async$goto = 3;
              return A._asyncAwait($async$self.deriveKeys$2($async$result, $async$self.keyOptions.ratchetSalt), $async$setKey$2);
            case 3:
              // returning from await.
              $async$goto = 2;
              return A._asyncAwait($async$self.setKeySetFromMaterial$2($async$result, keyIndex), $async$setKey$2);
            case 2:
              // returning from await.
              // implicit return
              return A._asyncReturn(null, $async$completer);
          }
      });
      return A._asyncStartSync($async$setKey$2, $async$completer);
    },
    setKeySetFromMaterial$2(keySet, keyIndex) {
      var $async$goto = 0,
        $async$completer = A._makeAsyncAwaitCompleter(type$.void),
        $async$self = this;
      var $async$setKeySetFromMaterial$2 = A._wrapJsFunctionForAsync(function($async$errorCode, $async$result) {
        if ($async$errorCode === 1)
          return A._asyncRethrow($async$result, $async$completer);
        while (true)
          switch ($async$goto) {
            case 0:
              // Function start
              A.print("setting new key");
              if (keyIndex >= 0)
                $async$self.currentKeyIndex = B.JSInt_methods.$mod(keyIndex, 16);
              B.JSArray_methods.$indexSet($async$self.cryptoKeyRing, $async$self.currentKeyIndex, keySet);
              // implicit return
              return A._asyncReturn(null, $async$completer);
          }
      });
      return A._asyncStartSync($async$setKeySetFromMaterial$2, $async$completer);
    },
    deriveKeys$2(material, salt) {
      var $async$goto = 0,
        $async$completer = A._makeAsyncAwaitCompleter(type$.KeySet),
        $async$returnValue, algorithmOptions, $async$temp1, $async$temp2;
      var $async$deriveKeys$2 = A._wrapJsFunctionForAsync(function($async$errorCode, $async$result) {
        if ($async$errorCode === 1)
          return A._asyncRethrow($async$result, $async$completer);
        while (true)
          switch ($async$goto) {
            case 0:
              // Function start
              algorithmOptions = A.getAlgoOptions(J.get$name$x(type$.Algorithm._as(material.algorithm)), salt);
              $async$temp1 = A;
              $async$temp2 = material;
              $async$goto = 3;
              return A._asyncAwait(A.promiseToFuture(self.crypto.subtle.deriveKey(A.jsify(algorithmOptions), material, A.jsify(A.LinkedHashMap_LinkedHashMap$_literal(["name", "AES-GCM", "length", 128], type$.String, type$.Object)), false, A._setArrayType(["encrypt", "decrypt"], type$.JSArray_String)), type$.CryptoKey), $async$deriveKeys$2);
            case 3:
              // returning from await.
              $async$returnValue = new $async$temp1.KeySet($async$temp2, $async$result);
              // goto return
              $async$goto = 1;
              break;
            case 1:
              // return
              return A._asyncReturn($async$returnValue, $async$completer);
          }
      });
      return A._asyncStartSync($async$deriveKeys$2, $async$completer);
    },
    ratchet$2(material, salt) {
      var $async$goto = 0,
        $async$completer = A._makeAsyncAwaitCompleter(type$.Uint8List),
        $async$returnValue, algorithmOptions, $async$temp1;
      var $async$ratchet$2 = A._wrapJsFunctionForAsync(function($async$errorCode, $async$result) {
        if ($async$errorCode === 1)
          return A._asyncRethrow($async$result, $async$completer);
        while (true)
          switch ($async$goto) {
            case 0:
              // Function start
              algorithmOptions = A.getAlgoOptions("PBKDF2", salt);
              $async$temp1 = A;
              $async$goto = 3;
              return A._asyncAwait(A.promiseToFuture(self.crypto.subtle.deriveBits(A.jsify(algorithmOptions), material, 256), type$.ByteBuffer), $async$ratchet$2);
            case 3:
              // returning from await.
              $async$returnValue = $async$temp1.NativeUint8List_NativeUint8List$view($async$result, 0, null);
              // goto return
              $async$goto = 1;
              break;
            case 1:
              // return
              return A._asyncReturn($async$returnValue, $async$completer);
          }
      });
      return A._asyncStartSync($async$ratchet$2, $async$completer);
    },
    setupTransform$6$codec$kind$operation$readable$trackId$writable(codec, kind, operation, readable, trackId, writable) {
      return this.setupTransform$body$Cryptor(codec, kind, operation, readable, trackId, writable);
    },
    setupTransform$5$kind$operation$readable$trackId$writable(kind, operation, readable, trackId, writable) {
      return this.setupTransform$6$codec$kind$operation$readable$trackId$writable(null, kind, operation, readable, trackId, writable);
    },
    setupTransform$body$Cryptor(codec, kind, operation, readable, trackId, writable) {
      var $async$goto = 0,
        $async$completer = A._makeAsyncAwaitCompleter(type$.void),
        $async$self = this, transformer, e, t1, t2, t3, exception;
      var $async$setupTransform$6$codec$kind$operation$readable$trackId$writable = A._wrapJsFunctionForAsync(function($async$errorCode, $async$result) {
        if ($async$errorCode === 1)
          return A._asyncRethrow($async$result, $async$completer);
        while (true)
          switch ($async$goto) {
            case 0:
              // Function start
              A.print("setupTransform " + operation);
              $async$self.__Cryptor_kind_A = kind;
              if (codec != null) {
                A.print("setting codec on cryptor to " + codec);
                $async$self.codec = codec;
              }
              t1 = operation === "encode" ? $async$self.get$encodeFunction() : $async$self.get$decodeFunction();
              t2 = type$.Future_void_Function_RTCEncodedFrame_TransformStreamDefaultController;
              t3 = type$.String;
              transformer = new self.TransformStream(A.jsify(A.LinkedHashMap_LinkedHashMap$_literal(["transform", A.allowInterop(t1, t2)], t3, t2)));
              try {
                J.pipeTo$1$x(J.pipeThrough$1$x(readable, transformer), writable);
              } catch (exception) {
                e = A.unwrapException(exception);
                A.print("e " + J.toString$0$(e));
                if ($async$self.lastError !== B.CryptorError_7) {
                  $async$self.lastError = B.CryptorError_7;
                  B.DedicatedWorkerGlobalScope_methods.postMessage$1($async$self.worker, A.LinkedHashMap_LinkedHashMap$_literal(["type", "cryptorState", "participantId", $async$self.participantId, "state", "internalError", "error", "Internal error: " + J.toString$0$(e)], t3, type$.nullable_String));
                }
              }
              $async$self.trackId = trackId;
              // implicit return
              return A._asyncReturn(null, $async$completer);
          }
      });
      return A._asyncStartSync($async$setupTransform$6$codec$kind$operation$readable$trackId$writable, $async$completer);
    },
    getUnencryptedBytes$2(frame, codec) {
      var data, naluIndices, t1, t2, _i, index;
      if (codec != null && codec.toLowerCase() === "h264") {
        data = A.NativeUint8List_NativeUint8List$view(J.get$data$x(frame), 0, null);
        naluIndices = A.findNALUIndices(data);
        for (t1 = naluIndices.length, t2 = data.length, _i = 0; _i < t1; ++_i) {
          index = naluIndices[_i];
          if (!(index < t2))
            return A.ioore(data, index);
          switch (data[index] & 31) {
            case 5:
            case 1:
              return index + 2;
            default:
              break;
          }
        }
        throw A.wrapException(A.Exception_Exception("Could not find NALU"));
      }
      switch (J.get$type$x(frame)) {
        case "key":
          return 10;
        case "delta":
          return 3;
        case "audio":
          return 1;
        default:
          return 0;
      }
    },
    encodeFunction$2(frame, controller) {
      return this.encodeFunction$body$Cryptor(type$.RTCEncodedFrame._as(frame), type$.TransformStreamDefaultController._as(controller));
    },
    encodeFunction$body$Cryptor(frame, controller) {
      var $async$goto = 0,
        $async$completer = A._makeAsyncAwaitCompleter(type$.void),
        $async$returnValue, $async$handler = 2, $async$currentError, $async$self = this, secretKey, keyIndex, headerLength, metaData, iv, frameTrailer, cipherText, finalBuffer, e, t2, t3, t4, iv0, sendCount, exception, t1, buffer, $async$exception;
      var $async$encodeFunction$2 = A._wrapJsFunctionForAsync(function($async$errorCode, $async$result) {
        if ($async$errorCode === 1) {
          $async$currentError = $async$result;
          $async$goto = $async$handler;
        }
        while (true)
          switch ($async$goto) {
            case 0:
              // Function start
              t1 = J.getInterceptor$x(frame);
              buffer = A.NativeUint8List_NativeUint8List$view(t1.get$data(frame), 0, null);
              if (!$async$self.enabled || J.get$length$asx(buffer) === 0) {
                J.enqueue$1$x(controller, frame);
                // goto return
                $async$goto = 1;
                break;
              }
              t2 = $async$self.getKeySet$1($async$self.currentKeyIndex);
              secretKey = t2 == null ? null : t2.encryptionKey;
              keyIndex = $async$self.currentKeyIndex;
              if (secretKey == null) {
                if ($async$self.lastError !== B.CryptorError_5) {
                  $async$self.lastError = B.CryptorError_5;
                  t1 = $async$self.participantId;
                  t2 = $async$self.trackId;
                  t3 = $async$self.__Cryptor_kind_A;
                  t3 === $ && A.throwLateFieldNI("kind");
                  B.DedicatedWorkerGlobalScope_methods.postMessage$1($async$self.worker, A.LinkedHashMap_LinkedHashMap$_literal(["type", "cryptorState", "participantId", t1, "trackId", t2, "kind", t3, "state", "missingKey", "error", "Missing key for track " + A.S(t2)], type$.String, type$.nullable_String));
                }
                // goto return
                $async$goto = 1;
                break;
              }
              $async$handler = 4;
              t2 = $async$self.__Cryptor_kind_A;
              t2 === $ && A.throwLateFieldNI("kind");
              headerLength = t2 === "video" ? $async$self.getUnencryptedBytes$2(frame, $async$self.codec) : 1;
              metaData = t1.getMetadata$0(frame);
              t3 = J.get$synchronizationSource$x(metaData);
              t4 = t1.get$timestamp(frame);
              A._asInt(t3);
              A._asInt(t4);
              iv0 = new DataView(new ArrayBuffer(12));
              t2 = $async$self.sendCounts;
              if (t2.$index(0, t3) == null)
                t2.$indexSet(0, t3, $.$get$Random__secureRandom().nextInt$1(65535));
              sendCount = t2.$index(0, t3);
              if (sendCount == null)
                sendCount = 0;
              B.NativeByteData_methods._setUint32$3(iv0, 0, t3, false);
              B.NativeByteData_methods._setUint32$3(iv0, 4, t4, false);
              B.NativeByteData_methods._setUint32$3(iv0, 8, t4 - B.JSInt_methods.$mod(sendCount, 65535), false);
              t2.$indexSet(0, t3, sendCount + 1);
              iv = A.NativeUint8List_NativeUint8List$view(iv0.buffer, 0, null);
              frameTrailer = new DataView(new ArrayBuffer(2));
              J.setInt8$2$x(frameTrailer, 0, 12);
              J.setInt8$2$x(frameTrailer, 1, keyIndex);
              $async$goto = 7;
              return A._asyncAwait(A.promiseToFuture(self.crypto.subtle.encrypt({name: "AES-GCM", iv: A.jsArrayBufferFrom(iv), additionalData: A.jsArrayBufferFrom(J.sublist$2$x(buffer, 0, headerLength))}, secretKey, A.jsArrayBufferFrom(J.sublist$2$x(buffer, headerLength, J.get$length$asx(buffer)))), type$.ByteBuffer), $async$encodeFunction$2);
            case 7:
              // returning from await.
              cipherText = $async$result;
              t2 = $.$get$_CopyingBytesBuilder__emptyList();
              finalBuffer = new A._CopyingBytesBuilder(t2);
              J.add$1$ax(finalBuffer, new Uint8Array(A._ensureNativeList(J.sublist$2$x(buffer, 0, headerLength))));
              J.add$1$ax(finalBuffer, A.NativeUint8List_NativeUint8List$view(cipherText, 0, null));
              J.add$1$ax(finalBuffer, iv);
              J.add$1$ax(finalBuffer, A.NativeUint8List_NativeUint8List$view(frameTrailer.buffer, 0, null));
              t1.set$data(frame, A.jsArrayBufferFrom(finalBuffer.toBytes$0()));
              J.enqueue$1$x(controller, frame);
              if ($async$self.lastError !== B.CryptorError_1) {
                $async$self.lastError = B.CryptorError_1;
                B.DedicatedWorkerGlobalScope_methods.postMessage$1($async$self.worker, A.LinkedHashMap_LinkedHashMap$_literal(["type", "cryptorState", "participantId", $async$self.participantId, "trackId", $async$self.trackId, "kind", $async$self.__Cryptor_kind_A, "state", "ok", "error", "encryption ok"], type$.String, type$.nullable_String));
              }
              $async$handler = 2;
              // goto after finally
              $async$goto = 6;
              break;
            case 4:
              // catch
              $async$handler = 3;
              $async$exception = $async$currentError;
              e = A.unwrapException($async$exception);
              if ($async$self.lastError !== B.CryptorError_3) {
                $async$self.lastError = B.CryptorError_3;
                t1 = $async$self.participantId;
                t2 = $async$self.trackId;
                t3 = $async$self.__Cryptor_kind_A;
                t3 === $ && A.throwLateFieldNI("kind");
                B.DedicatedWorkerGlobalScope_methods.postMessage$1($async$self.worker, A.LinkedHashMap_LinkedHashMap$_literal(["type", "cryptorState", "participantId", t1, "trackId", t2, "kind", t3, "state", "encryptError", "error", J.toString$0$(e)], type$.String, type$.nullable_String));
              }
              // goto after finally
              $async$goto = 6;
              break;
            case 3:
              // uncaught
              // goto rethrow
              $async$goto = 2;
              break;
            case 6:
              // after finally
            case 1:
              // return
              return A._asyncReturn($async$returnValue, $async$completer);
            case 2:
              // rethrow
              return A._asyncRethrow($async$currentError, $async$completer);
          }
      });
      return A._asyncStartSync($async$encodeFunction$2, $async$completer);
    },
    decodeFunction$2(frame, controller) {
      return this.decodeFunction$body$Cryptor(type$.RTCEncodedFrame._as(frame), type$.TransformStreamDefaultController._as(controller));
    },
    decodeFunction$body$Cryptor(frame, controller) {
      var $async$goto = 0,
        $async$completer = A._makeAsyncAwaitCompleter(type$.void),
        $async$returnValue, $async$handler = 2, $async$currentError, $async$self = this, headerLength, metaData, frameTrailer, ivLength, keyIndex, iv, currentkeySet, endDecLoop, finalBuffer, e, t2, t3, t4, t5, t6, t7, endDecLoop0, t8, t9, t10, t11, t12, t13, exception, ratchetCount, t1, buffer, decrypted, initialKeySet, initialKeyIndex, $async$exception, $async$exception1;
      var $async$decodeFunction$2 = A._wrapJsFunctionForAsync(function($async$errorCode, $async$result) {
        if ($async$errorCode === 1) {
          $async$currentError = $async$result;
          $async$goto = $async$handler;
        }
        while (true)
          switch ($async$goto) {
            case 0:
              // Function start
              ratchetCount = 0;
              t1 = J.getInterceptor$x(frame);
              buffer = A.NativeUint8List_NativeUint8List$view(t1.get$data(frame), 0, null);
              decrypted = null;
              initialKeySet = null;
              initialKeyIndex = $async$self.currentKeyIndex;
              if (!$async$self.enabled || J.get$length$asx(buffer) === 0) {
                J.enqueue$1$x(controller, frame);
                // goto return
                $async$goto = 1;
                break;
              }
              $async$handler = 4;
              t2 = $async$self.__Cryptor_kind_A;
              t2 === $ && A.throwLateFieldNI("kind");
              headerLength = t2 === "video" ? $async$self.getUnencryptedBytes$2(frame, $async$self.codec) : 1;
              metaData = t1.getMetadata$0(frame);
              frameTrailer = J.sublist$1$x(buffer, J.get$length$asx(buffer) - 2);
              ivLength = J.$index$asx(frameTrailer, 0);
              keyIndex = J.$index$asx(frameTrailer, 1);
              t3 = J.get$length$asx(buffer);
              t4 = ivLength;
              if (typeof t4 !== "number") {
                $async$returnValue = A.iae(t4);
                // goto return
                $async$goto = 1;
                break;
              }
              iv = J.sublist$2$x(buffer, t3 - t4 - 2, J.get$length$asx(buffer) - 2);
              currentkeySet = $async$self.getKeySet$1(keyIndex);
              initialKeySet = currentkeySet;
              initialKeyIndex = keyIndex;
              if (currentkeySet == null) {
                if ($async$self.lastError !== B.CryptorError_5) {
                  $async$self.lastError = B.CryptorError_5;
                  t1 = $async$self.participantId;
                  t2 = $async$self.trackId;
                  B.DedicatedWorkerGlobalScope_methods.postMessage$1($async$self.worker, A.LinkedHashMap_LinkedHashMap$_literal(["type", "cryptorState", "participantId", t1, "trackId", t2, "kind", $async$self.__Cryptor_kind_A, "state", "missingKey", "error", "Missing key for track " + A.S(t2)], type$.String, type$.nullable_String));
                }
                J.enqueue$1$x(controller, frame);
                // goto return
                $async$goto = 1;
                break;
              }
              endDecLoop = false;
              t2 = $async$self.cryptoKeyRing, t3 = $async$self.keyOptions.ratchetWindowSize, t4 = type$.ByteBuffer, t5 = type$.String, t6 = type$.nullable_String, t7 = $async$self.worker, endDecLoop0 = t3 <= 0;
            case 7:
              // for condition
              if (!!A.boolConversionCheck(endDecLoop)) {
                // goto after for
                $async$goto = 8;
                break;
              }
              $async$handler = 10;
              t8 = buffer;
              t8 = {name: "AES-GCM", iv: A.jsArrayBufferFrom(iv), additionalData: A.jsArrayBufferFrom(new Uint8Array(t8.subarray(0, A._checkValidRange(0, A._asIntQ(headerLength), J.get$length$asx(t8)))))};
              t9 = currentkeySet.encryptionKey;
              t10 = buffer;
              t11 = J.get$length$asx(buffer);
              t12 = ivLength;
              if (typeof t12 !== "number") {
                $async$returnValue = A.iae(t12);
                // goto return
                $async$goto = 1;
                break;
              }
              t13 = A._asInt(headerLength);
              $async$goto = 13;
              return A._asyncAwait(A.promiseToFuture(self.crypto.subtle.decrypt(t8, t9, A.jsArrayBufferFrom(new Uint8Array(t10.subarray(t13, A._checkValidRange(t13, A._asIntQ(t11 - t12 - 2), J.get$length$asx(t10)))))), t4), $async$decodeFunction$2);
            case 13:
              // returning from await.
              decrypted = $async$result;
              endDecLoop = true;
              t8 = $async$self.lastError;
              if (t8 !== B.CryptorError_1)
                if (t8 !== B.CryptorError_6) {
                  t8 = ratchetCount;
                  if (typeof t8 !== "number") {
                    $async$returnValue = t8.$gt();
                    // goto return
                    $async$goto = 1;
                    break;
                  }
                  t8 = t8 > 0;
                } else
                  t8 = false;
              else
                t8 = false;
              if (t8) {
                A.printString("KeyRatcheted: ssrc " + A.S(J.get$synchronizationSource$x(metaData)) + " timestamp " + A.S(t1.get$timestamp(frame)) + " ratchetCount " + A.S(ratchetCount) + "  participantId: " + A.S($async$self.participantId));
                A.printString("ratchetKey: lastError != CryptorError.kKeyRatcheted, reset state to kKeyRatcheted");
                $async$self.lastError = B.CryptorError_6;
                B.DedicatedWorkerGlobalScope_methods.postMessage$1(t7, A.LinkedHashMap_LinkedHashMap$_literal(["type", "cryptorState", "participantId", $async$self.participantId, "trackId", $async$self.trackId, "kind", $async$self.__Cryptor_kind_A, "state", "keyRatcheted", "error", "Key ratcheted ok"], t5, t6));
              }
              $async$handler = 4;
              // goto after finally
              $async$goto = 12;
              break;
            case 10:
              // catch
              $async$handler = 9;
              $async$exception = $async$currentError;
              $async$self.lastError = B.CryptorError_7;
              t8 = ratchetCount;
              if (typeof t8 !== "number") {
                $async$returnValue = t8.$ge();
                // goto return
                $async$goto = 1;
                break;
              }
              endDecLoop = t8 >= t3 || endDecLoop0;
              if (A.boolConversionCheck(endDecLoop))
                throw $async$exception;
              t8 = currentkeySet;
              t9 = A._asIntQ(keyIndex);
              if (t9 == null)
                t9 = $async$self.currentKeyIndex;
              if (!(t9 >= 0 && t9 < 16)) {
                $async$returnValue = A.ioore(t2, t9);
                // goto return
                $async$goto = 1;
                break;
              }
              $async$goto = J.$eq$(t8, t2[t9]) ? 14 : 15;
              break;
            case 14:
              // then
              t8 = ratchetCount;
              if (typeof t8 !== "number") {
                $async$returnValue = t8.$add();
                // goto return
                $async$goto = 1;
                break;
              }
              ratchetCount = t8 + 1;
              $async$goto = 16;
              return A._asyncAwait($async$self.ratchetKey$1(keyIndex), $async$decodeFunction$2);
            case 16:
              // returning from await.
            case 15:
              // join
              t8 = A._asIntQ(keyIndex);
              if (t8 == null)
                t8 = $async$self.currentKeyIndex;
              if (!(t8 >= 0 && t8 < 16)) {
                $async$returnValue = A.ioore(t2, t8);
                // goto return
                $async$goto = 1;
                break;
              }
              currentkeySet = t2[t8];
              // goto after finally
              $async$goto = 12;
              break;
            case 9:
              // uncaught
              // goto catch
              $async$goto = 4;
              break;
            case 12:
              // after finally
              // goto for condition
              $async$goto = 7;
              break;
            case 8:
              // after for
              t2 = $.$get$_CopyingBytesBuilder__emptyList();
              finalBuffer = new A._CopyingBytesBuilder(t2);
              J.add$1$ax(finalBuffer, new Uint8Array(A._ensureNativeList(J.sublist$2$x(buffer, 0, headerLength))));
              t2 = decrypted;
              t2.toString;
              J.add$1$ax(finalBuffer, A.NativeUint8List_NativeUint8List$view(t2, 0, null));
              t1.set$data(frame, A.jsArrayBufferFrom(finalBuffer.toBytes$0()));
              J.enqueue$1$x(controller, frame);
              if ($async$self.lastError !== B.CryptorError_1) {
                $async$self.lastError = B.CryptorError_1;
                B.DedicatedWorkerGlobalScope_methods.postMessage$1(t7, A.LinkedHashMap_LinkedHashMap$_literal(["type", "cryptorState", "participantId", $async$self.participantId, "trackId", $async$self.trackId, "kind", $async$self.__Cryptor_kind_A, "state", "ok", "error", "decryption ok"], t5, t6));
              }
              $async$handler = 2;
              // goto after finally
              $async$goto = 6;
              break;
            case 4:
              // catch
              $async$handler = 3;
              $async$exception1 = $async$currentError;
              e = A.unwrapException($async$exception1);
              if ($async$self.lastError !== B.CryptorError_2) {
                $async$self.lastError = B.CryptorError_2;
                t1 = $async$self.participantId;
                t2 = $async$self.trackId;
                t3 = $async$self.__Cryptor_kind_A;
                t3 === $ && A.throwLateFieldNI("kind");
                B.DedicatedWorkerGlobalScope_methods.postMessage$1($async$self.worker, A.LinkedHashMap_LinkedHashMap$_literal(["type", "cryptorState", "participantId", t1, "trackId", t2, "kind", t3, "state", "decryptError", "error", J.toString$0$(e)], type$.String, type$.nullable_String));
              }
              $async$goto = initialKeySet != null ? 17 : 18;
              break;
            case 17:
              // then
              $async$goto = 19;
              return A._asyncAwait($async$self.setKeySetFromMaterial$2(initialKeySet, initialKeyIndex), $async$decodeFunction$2);
            case 19:
              // returning from await.
            case 18:
              // join
              // goto after finally
              $async$goto = 6;
              break;
            case 3:
              // uncaught
              // goto rethrow
              $async$goto = 2;
              break;
            case 6:
              // after finally
            case 1:
              // return
              return A._asyncReturn($async$returnValue, $async$completer);
            case 2:
              // rethrow
              return A._asyncRethrow($async$currentError, $async$completer);
          }
      });
      return A._asyncStartSync($async$decodeFunction$2, $async$completer);
    },
    get$participantId(receiver) {
      return this.participantId;
    },
    get$trackId(receiver) {
      return this.trackId;
    },
    get$codec(receiver) {
      return this.codec;
    }
  };
  A.Cryptor_ratchetKey_closure.prototype = {
    call$1(newMaterial) {
      var t1 = this.$this;
      t1.deriveKeys$2(type$.CryptoKey._as(newMaterial), t1.keyOptions.ratchetSalt).then$1$1(new A.Cryptor_ratchetKey__closure(t1, this.keyIndex), type$.Null);
    },
    $signature: 24
  };
  A.Cryptor_ratchetKey__closure.prototype = {
    call$1(newKeySet) {
      var t1, t2;
      type$.KeySet._as(newKeySet);
      t1 = this.$this;
      t2 = this.keyIndex;
      t1.setKeySetFromMaterial$2(newKeySet, t2 == null ? t1.currentKeyIndex : t2).then$1$1(new A.Cryptor_ratchetKey___closure(t1), type$.Null);
    },
    $signature: 25
  };
  A.Cryptor_ratchetKey___closure.prototype = {
    call$1(_) {
      var t1 = this.$this;
      t1._ratchetCompleter.complete$0(0);
      t1._ratchetCompleter = null;
    },
    $signature: 26
  };
  A.TransformMessage.prototype = {};
  A.EnableTransformMessage.prototype = {};
  A.RemoveTransformMessage.prototype = {};
  A.main_closure.prototype = {
    call$1($event) {
      var t1, options, t2, kind, participantId, trackId, codec, msgType, cryptor;
      A.print("got transform event");
      t1 = type$.JavaScriptObject._as(type$.RTCTransformEvent._as($event).transformer);
      t1.handled = true;
      options = t1.options;
      t2 = J.getInterceptor$x(options);
      kind = t2.get$kind(options);
      participantId = t2.get$participantId(options);
      trackId = t2.get$trackId(options);
      codec = t2.get$codec(options);
      msgType = t2.get$msgType(options);
      cryptor = A.IterableExtension_firstWhereOrNull($.participantCryptors, new A.main__closure8(trackId), type$.Cryptor);
      if (cryptor == null) {
        t2 = self.self;
        A._asStringQ(participantId);
        A._asStringQ(trackId);
        cryptor = A.Cryptor$($.$get$keyProviderOptions(), participantId, trackId, t2);
        B.JSArray_methods.add$1($.participantCryptors, cryptor);
      }
      A._asString(msgType);
      t2 = type$.ReadableStream._as(t1.readable);
      t1 = type$.WritableStream._as(t1.writable);
      A._asString(trackId);
      A._asString(kind);
      cryptor.setupTransform$6$codec$kind$operation$readable$trackId$writable(A._asStringQ(codec), kind, msgType, t2, trackId, t1);
    },
    $signature: 3
  };
  A.main__closure8.prototype = {
    call$1(c) {
      return type$.Cryptor._as(c).trackId == this.trackId;
    },
    $signature: 1
  };
  A.main_closure0.prototype = {
    call$1(e) {
      var copy, msgType, options, t3, enabled, participantId, t4, cryptors, line, _i, cryptor, kind, exist, trackId, readable, writable, key, keyIndex, c, keySet, codec,
        _s13_ = "participantId",
        _s53_ = "]: lastError != CryptorError.kOk, reset state to kNew",
        _s7_ = "trackId",
        _s8_ = "keyIndex",
        t1 = type$.MessageEvent._as(e).data,
        t2 = new A._AcceptStructuredCloneDart2Js([], []);
      t2.mustCopy = true;
      copy = t2.walk$1(t1);
      t1 = J.getInterceptor$asx(copy);
      msgType = t1.$index(copy, "msgType");
      switch (msgType) {
        case "init":
          options = t1.$index(copy, "keyOptions");
          t1 = J.getInterceptor$asx(options);
          t2 = A._asBool(t1.$index(options, "sharedKey"));
          t3 = new Uint8Array(A._ensureNativeList(B.C_Base64Decoder.convert$1(A._asString(t1.$index(options, "ratchetSalt")))));
          t1 = A._asInt(t1.$index(options, "ratchetWindowSize"));
          $.keyProviderOptions = new A.KeyOptions(t2, t3, t1);
          A.print("worker: init with keyOptions " + ("KeyOptions{sharedKey: " + t2 + ", ratchetWindowSize: " + t1 + "}"));
          break;
        case "enable":
          enabled = A._asBool(t1.$index(copy, "enabled"));
          participantId = A._asString(t1.$index(copy, _s13_));
          t1 = "" + enabled;
          A.print("worker: set enable " + t1 + " for participantId " + participantId);
          t2 = $.participantCryptors;
          t3 = A._arrayInstanceType(t2);
          t4 = t3._eval$1("WhereIterable<1>");
          cryptors = A.List_List$of(new A.WhereIterable(t2, t3._eval$1("bool(1)")._as(new A.main__closure(participantId)), t4), true, t4._eval$1("Iterable.E"));
          for (t2 = cryptors.length, line = "setEnabled[" + t1 + _s53_, _i = 0; _i < t2; ++_i) {
            cryptor = cryptors[_i];
            if (cryptor.lastError !== B.CryptorError_1) {
              A.printString(line);
              cryptor.lastError = B.CryptorError_0;
            }
            cryptor.enabled = enabled;
          }
          J.postMessage$1$x(self.self, A.LinkedHashMap_LinkedHashMap$_literal(["type", "cryptorEnabled", "participantId", participantId, "enable", enabled], type$.String, type$.Object));
          break;
        case "decode":
        case "encode":
          kind = t1.$index(copy, "kind");
          exist = A._asBool(t1.$index(copy, "exist"));
          participantId = A._asString(t1.$index(copy, _s13_));
          trackId = t1.$index(copy, _s7_);
          readable = type$.ReadableStream._as(t1.$index(copy, "readableStream"));
          writable = type$.WritableStream._as(t1.$index(copy, "writableStream"));
          A.print("worker: got " + A.S(msgType) + ", kind " + A.S(kind) + ", trackId " + A.S(trackId) + ", participantId " + participantId + ", " + B.Type_JSObject_8k0.toString$0(0) + " " + B.Type_JSObject_8k0.toString$0(0) + "}");
          cryptor = A.IterableExtension_firstWhereOrNull($.participantCryptors, new A.main__closure0(trackId), type$.Cryptor);
          if (cryptor == null) {
            t1 = self.self;
            A._asStringQ(trackId);
            cryptor = A.Cryptor$($.$get$keyProviderOptions(), participantId, trackId, t1);
            B.JSArray_methods.add$1($.participantCryptors, cryptor);
          }
          if (!exist) {
            A._asString(msgType);
            A._asString(trackId);
            cryptor.setupTransform$5$kind$operation$readable$trackId$writable(A._asString(kind), msgType, readable, trackId, writable);
          }
          if (cryptor.lastError !== B.CryptorError_1) {
            A.print("setParticipantId: lastError != CryptorError.kOk, reset state to kNew");
            cryptor.lastError = B.CryptorError_0;
          }
          cryptor.participantId = participantId;
          J.postMessage$1$x(self.self, A.LinkedHashMap_LinkedHashMap$_literal(["type", "cryptorSetup", "participantId", participantId, "trackId", trackId, "exist", exist, "operation", msgType], type$.String, type$.dynamic));
          cryptor.lastError = B.CryptorError_0;
          break;
        case "removeTransform":
          trackId = A._asString(t1.$index(copy, _s7_));
          A.print("worker: removing trackId " + trackId);
          t1 = $.participantCryptors;
          t2 = A._arrayInstanceType(t1)._eval$1("bool(1)")._as(new A.main__closure1(trackId));
          if (!!t1.fixed$length)
            A.throwExpression(A.UnsupportedError$("removeWhere"));
          B.JSArray_methods._removeWhere$2(t1, t2, true);
          break;
        case "setKey":
          key = new Uint8Array(A._ensureNativeList(B.C_Base64Decoder.convert$1(A._asString(t1.$index(copy, "key")))));
          keyIndex = t1.$index(copy, _s8_);
          participantId = A._asString(t1.$index(copy, _s13_));
          A.print("worker: setup key for participant " + participantId);
          if ($.$get$keyProviderOptions().sharedKey) {
            for (t1 = $.participantCryptors, t2 = t1.length, _i = 0; _i < t1.length; t1.length === t2 || (0, A.throwConcurrentModificationError)(t1), ++_i)
              t1[_i].setKey$2(A._asInt(keyIndex), key);
            return;
          }
          t1 = $.participantCryptors;
          t2 = A._arrayInstanceType(t1);
          t3 = t2._eval$1("WhereIterable<1>");
          cryptors = A.List_List$of(new A.WhereIterable(t1, t2._eval$1("bool(1)")._as(new A.main__closure2(participantId)), t3), true, t3._eval$1("Iterable.E"));
          for (t1 = cryptors.length, _i = 0; _i < t1; ++_i)
            cryptors[_i].setKey$2(A._asInt(keyIndex), key);
          break;
        case "ratchetKey":
          keyIndex = t1.$index(copy, _s8_);
          participantId = A._asString(t1.$index(copy, _s13_));
          A.print("worker: ratchetKey for participant " + participantId + ", keyIndex " + A.S(keyIndex));
          t1 = $.participantCryptors;
          t2 = A._arrayInstanceType(t1);
          t3 = t2._eval$1("WhereIterable<1>");
          cryptors = A.List_List$of(new A.WhereIterable(t1, t2._eval$1("bool(1)")._as(new A.main__closure3(participantId)), t3), true, t3._eval$1("Iterable.E"));
          for (t1 = cryptors.length, t2 = type$.Null, _i = 0; _i < t1; ++_i) {
            c = cryptors[_i];
            A._asIntQ(keyIndex);
            t3 = c.cryptoKeyRing;
            t4 = keyIndex == null ? c.currentKeyIndex : keyIndex;
            if (!(t4 >= 0 && t4 < 16))
              return A.ioore(t3, t4);
            keySet = t3[t4];
            c.ratchetKey$1(keyIndex).then$1$1(new A.main__closure4(c, keySet, participantId), t2);
          }
          break;
        case "setKeyIndex":
          keyIndex = t1.$index(copy, "index");
          participantId = A._asString(t1.$index(copy, _s13_));
          A.print("worker: setup key index for participant " + participantId);
          t1 = $.participantCryptors;
          t2 = A._arrayInstanceType(t1);
          t3 = t2._eval$1("WhereIterable<1>");
          cryptors = A.List_List$of(new A.WhereIterable(t1, t2._eval$1("bool(1)")._as(new A.main__closure5(participantId)), t3), true, t3._eval$1("Iterable.E"));
          for (t1 = cryptors.length, _i = 0; _i < t1; ++_i) {
            c = cryptors[_i];
            A._asInt(keyIndex);
            if (c.lastError !== B.CryptorError_1) {
              A.printString("setKeyIndex: lastError != CryptorError.kOk, reset state to kNew");
              c.lastError = B.CryptorError_0;
            }
            c.currentKeyIndex = keyIndex;
          }
          break;
        case "updateCodec":
          codec = A._asString(t1.$index(copy, "codec"));
          trackId = A._asString(t1.$index(copy, _s7_));
          A.print("worker: update codec for trackId " + trackId + ", codec " + codec);
          cryptor = A.IterableExtension_firstWhereOrNull($.participantCryptors, new A.main__closure6(trackId), type$.Cryptor);
          if (cryptor != null) {
            if (cryptor.lastError !== B.CryptorError_1) {
              A.print("updateCodec[" + codec + _s53_);
              cryptor.lastError = B.CryptorError_0;
            }
            cryptor.codec = codec;
          }
          break;
        case "dispose":
          trackId = A._asString(t1.$index(copy, _s7_));
          A.print("worker: dispose trackId " + trackId);
          cryptor = A.IterableExtension_firstWhereOrNull($.participantCryptors, new A.main__closure7(trackId), type$.Cryptor);
          if (cryptor != null) {
            cryptor.lastError = B.CryptorError_8;
            J.postMessage$1$x(self.self, A.LinkedHashMap_LinkedHashMap$_literal(["type", "cryptorDispose", "participantId", cryptor.participantId, "trackId", trackId], type$.String, type$.nullable_String));
          }
          break;
        default:
          A.print("worker: unknown message kind " + A.S(copy));
      }
    },
    $signature: 27
  };
  A.main__closure.prototype = {
    call$1(c) {
      return type$.Cryptor._as(c).participantId === this.participantId;
    },
    $signature: 1
  };
  A.main__closure0.prototype = {
    call$1(c) {
      var t1 = type$.Cryptor._as(c).trackId,
        t2 = this.trackId;
      return t1 == null ? t2 == null : t1 === t2;
    },
    $signature: 1
  };
  A.main__closure1.prototype = {
    call$1(c) {
      return type$.Cryptor._as(c).trackId === this.trackId;
    },
    $signature: 1
  };
  A.main__closure2.prototype = {
    call$1(c) {
      return type$.Cryptor._as(c).participantId === this.participantId;
    },
    $signature: 1
  };
  A.main__closure3.prototype = {
    call$1(c) {
      return type$.Cryptor._as(c).participantId === this.participantId;
    },
    $signature: 1
  };
  A.main__closure4.prototype = {
    call$1(_) {
      var $async$goto = 0,
        $async$completer = A._makeAsyncAwaitCompleter(type$.Null),
        $async$self = this, t1, newKey;
      var $async$call$1 = A._wrapJsFunctionForAsync(function($async$errorCode, $async$result) {
        if ($async$errorCode === 1)
          return A._asyncRethrow($async$result, $async$completer);
        while (true)
          switch ($async$goto) {
            case 0:
              // Function start
              t1 = $async$self.c;
              $async$goto = 2;
              return A._asyncAwait(t1.ratchet$2($async$self.keySet.material, $.$get$keyProviderOptions().ratchetSalt), $async$call$1);
            case 2:
              // returning from await.
              newKey = $async$result;
              J.postMessage$1$x(self.self, A.LinkedHashMap_LinkedHashMap$_literal(["type", "ratchetKey", "participantId", $async$self.participantId, "trackId", t1.trackId, "key", B.C_Base64Encoder.convert$1(type$.Base64Codec._eval$1("Codec.S")._as(newKey))], type$.String, type$.nullable_String));
              // implicit return
              return A._asyncReturn(null, $async$completer);
          }
      });
      return A._asyncStartSync($async$call$1, $async$completer);
    },
    $signature: 28
  };
  A.main__closure5.prototype = {
    call$1(c) {
      return type$.Cryptor._as(c).participantId === this.participantId;
    },
    $signature: 1
  };
  A.main__closure6.prototype = {
    call$1(c) {
      return type$.Cryptor._as(c).trackId === this.trackId;
    },
    $signature: 1
  };
  A.main__closure7.prototype = {
    call$1(c) {
      return type$.Cryptor._as(c).trackId === this.trackId;
    },
    $signature: 1
  };
  (function aliases() {
    var _ = J.Interceptor.prototype;
    _.super$Interceptor$toString = _.toString$0;
    _ = J.LegacyJavaScriptObject.prototype;
    _.super$LegacyJavaScriptObject$toString = _.toString$0;
    _ = A.ListMixin.prototype;
    _.super$ListMixin$setRange = _.setRange$4;
  })();
  (function installTearOffs() {
    var _static_1 = hunkHelpers._static_1,
      _static_0 = hunkHelpers._static_0,
      _instance_2_u = hunkHelpers._instance_2u;
    _static_1(A, "async__AsyncRun__scheduleImmediateJsOverride$closure", "_AsyncRun__scheduleImmediateJsOverride", 5);
    _static_1(A, "async__AsyncRun__scheduleImmediateWithSetImmediate$closure", "_AsyncRun__scheduleImmediateWithSetImmediate", 5);
    _static_1(A, "async__AsyncRun__scheduleImmediateWithTimer$closure", "_AsyncRun__scheduleImmediateWithTimer", 5);
    _static_0(A, "async___startMicrotaskLoop$closure", "_startMicrotaskLoop", 0);
    var _;
    _instance_2_u(_ = A.Cryptor.prototype, "get$encodeFunction", "encodeFunction$2", 7);
    _instance_2_u(_, "get$decodeFunction", "decodeFunction$2", 7);
  })();
  (function inheritance() {
    var _mixin = hunkHelpers.mixin,
      _inherit = hunkHelpers.inherit,
      _inheritMany = hunkHelpers.inheritMany;
    _inherit(A.Object, null);
    _inheritMany(A.Object, [A.JS_CONST, J.Interceptor, J.ArrayIterator, A._CopyingBytesBuilder, A.Error, A._ListBase_Object_ListMixin, A.SentinelValue, A.Iterable, A.ListIterator, A.Iterator, A.FixedLengthListMixin, A.UnmodifiableListMixin, A.Symbol, A.MapView, A.ConstantMap, A.JSInvocationMirror, A.Closure, A.TypeErrorDecoder, A.NullThrownFromJavaScriptException, A.ExceptionAndStackTrace, A._StackTrace, A._Required, A.MapMixin, A.LinkedHashMapCell, A.LinkedHashMapKeyIterator, A.Rti, A._FunctionParameters, A._Type, A._TimerImpl, A._AsyncAwaitCompleter, A.AsyncError, A._Completer, A._FutureListener, A._Future, A._AsyncCallbackEntry, A.Stream, A.StreamSubscription, A.StreamTransformerBase, A._StreamIterator, A._Zone, A._HashMapKeyIterator, A.ListMixin, A._UnmodifiableMapMixin, A.Codec, A._Base64Encoder, A._Base64Decoder, A.DateTime, A._Enum, A.OutOfMemoryError, A.StackOverflowError, A._Exception, A.FormatException, A.Null, A._StringStackTrace, A.StringBuffer, A.CssStyleDeclarationBase, A.EventStreamProvider, A.ImmutableListMixin, A.FixedSizeListIterator, A._StructuredClone, A._AcceptStructuredClone, A.NullRejectionException, A._JSSecureRandom, A.KeyOptions, A.KeySet, A.Cryptor]);
    _inheritMany(J.Interceptor, [J.JSBool, J.JSNull, J.JavaScriptObject, J.JSArray, J.JSNumber, J.JSString, A.NativeByteBuffer, A.NativeTypedData]);
    _inheritMany(J.JavaScriptObject, [J.LegacyJavaScriptObject, A.EventTarget, A.AccessibleNodeList, A.Blob, A.CryptoKey, A.CssTransformComponent, A.CssRule, A._CssStyleDeclaration_JavaScriptObject_CssStyleDeclarationBase, A.CssStyleValue, A.DataTransferItem, A.DataTransferItemList, A.DomException, A._DomRectList_JavaScriptObject_ListMixin, A.DomRectReadOnly, A._DomStringList_JavaScriptObject_ListMixin, A.DomTokenList, A.Event, A._FileList_JavaScriptObject_ListMixin, A.Gamepad, A.History, A._HtmlCollection_JavaScriptObject_ListMixin, A.ImageData, A.Location, A.MediaDeviceInfo, A.MediaList, A._MidiInputMap_JavaScriptObject_MapMixin, A._MidiOutputMap_JavaScriptObject_MapMixin, A.MimeType, A._MimeTypeArray_JavaScriptObject_ListMixin, A._NodeList_JavaScriptObject_ListMixin, A.Plugin, A._PluginArray_JavaScriptObject_ListMixin, A._RtcStatsReport_JavaScriptObject_MapMixin, A.SharedArrayBuffer, A.SpeechGrammar, A._SpeechGrammarList_JavaScriptObject_ListMixin, A.SpeechRecognitionResult, A._Storage_JavaScriptObject_MapMixin, A.StyleSheet, A._TextTrackCueList_JavaScriptObject_ListMixin, A.TimeRanges, A.Touch, A._TouchList_JavaScriptObject_ListMixin, A.TrackDefaultList, A.Url, A.VideoTrack, A.__CssRuleList_JavaScriptObject_ListMixin, A.__GamepadList_JavaScriptObject_ListMixin, A.__NamedNodeMap_JavaScriptObject_ListMixin, A.__SpeechRecognitionResultList_JavaScriptObject_ListMixin, A.__StyleSheetList_JavaScriptObject_ListMixin, A.Length, A._LengthList_JavaScriptObject_ListMixin, A.Number, A._NumberList_JavaScriptObject_ListMixin, A.PointList, A._StringList_JavaScriptObject_ListMixin, A.Transform, A._TransformList_JavaScriptObject_ListMixin, A.AudioBuffer, A._AudioParamMap_JavaScriptObject_MapMixin, A.AudioTrack]);
    _inheritMany(J.LegacyJavaScriptObject, [J.PlainJavaScriptObject, J.UnknownJavaScriptObject, J.JavaScriptFunction, A.WritableStream, A.ReadableStream, A.TransformStream, A.TransformStreamDefaultController, A.EncodedStreams, A.RTCEncodedFrame, A.RTCEncodedAudioFrame, A.RTCEncodedVideoFrame, A.RTCEncodedFrameMetadata, A.RTCEncodedAudioFrameMetadata, A.RTCEncodedVideoFrameMetadata, A.RTCTransformEvent, A.RTCRtpScriptTransformer, A.RTCRtpScriptTransform, A.Promise, A.Algorithm, A.AesGcmParams, A.TransformMessage, A.EnableTransformMessage, A.RemoveTransformMessage]);
    _inherit(J.JSUnmodifiableArray, J.JSArray);
    _inheritMany(J.JSNumber, [J.JSInt, J.JSNumNotInt]);
    _inheritMany(A.Error, [A.LateError, A.TypeError, A.JsNoSuchMethodError, A.UnknownJsTypeError, A.RuntimeError, A.AssertionError, A._Error, A.NullThrownError, A.ArgumentError, A.NoSuchMethodError, A.UnsupportedError, A.UnimplementedError, A.StateError, A.ConcurrentModificationError, A.CyclicInitializationError]);
    _inherit(A.ListBase, A._ListBase_Object_ListMixin);
    _inherit(A.UnmodifiableListBase, A.ListBase);
    _inherit(A.CodeUnits, A.UnmodifiableListBase);
    _inheritMany(A.Iterable, [A.EfficientLengthIterable, A.MappedIterable, A.WhereIterable, A._ConstantMapKeyIterable]);
    _inheritMany(A.EfficientLengthIterable, [A.ListIterable, A.LinkedHashMapKeyIterable, A._HashMapKeyIterable]);
    _inheritMany(A.ListIterable, [A.SubListIterable, A.MappedListIterable]);
    _inherit(A.EfficientLengthMappedIterable, A.MappedIterable);
    _inheritMany(A.Iterator, [A.MappedIterator, A.WhereIterator]);
    _inherit(A._UnmodifiableMapView_MapView__UnmodifiableMapMixin, A.MapView);
    _inherit(A.UnmodifiableMapView, A._UnmodifiableMapView_MapView__UnmodifiableMapMixin);
    _inherit(A.ConstantMapView, A.UnmodifiableMapView);
    _inherit(A.ConstantStringMap, A.ConstantMap);
    _inheritMany(A.Closure, [A.Closure2Args, A.Closure0Args, A.TearOffClosure, A.initHooks_closure, A.initHooks_closure1, A._AsyncRun__initializeScheduleImmediate_internalCallback, A._AsyncRun__initializeScheduleImmediate_closure, A._awaitOnObject_closure, A._Future__chainForeignFuture_closure, A._Future__propagateToListeners_handleWhenCompleteCallback_closure, A.Stream_length_closure, A._RootZone_bindUnaryCallbackGuarded_closure, A._EventStreamSubscription_closure, A._convertDataTree__convert, A.promiseToFuture_closure, A.promiseToFuture_closure0, A.Cryptor_ratchetKey_closure, A.Cryptor_ratchetKey__closure, A.Cryptor_ratchetKey___closure, A.main_closure, A.main__closure8, A.main_closure0, A.main__closure, A.main__closure0, A.main__closure1, A.main__closure2, A.main__closure3, A.main__closure4, A.main__closure5, A.main__closure6, A.main__closure7]);
    _inheritMany(A.Closure2Args, [A.Primitives_functionNoSuchMethod_closure, A.initHooks_closure0, A._awaitOnObject_closure0, A._wrapJsFunctionForAsync_closure, A._Future__chainForeignFuture_closure0, A.MapBase_mapToString_closure, A.NoSuchMethodError_toString_closure, A.MidiInputMap_keys_closure, A.MidiOutputMap_keys_closure, A.RtcStatsReport_keys_closure, A.Storage_keys_closure, A._StructuredClone_walk_closure, A._StructuredClone_walk_closure0, A._AcceptStructuredClone_walk_closure, A.AudioParamMap_keys_closure]);
    _inherit(A.NullError, A.TypeError);
    _inheritMany(A.TearOffClosure, [A.StaticClosure, A.BoundClosure]);
    _inherit(A._AssertionError, A.AssertionError);
    _inherit(A.MapBase, A.MapMixin);
    _inheritMany(A.MapBase, [A.JsLinkedHashMap, A._HashMap]);
    _inheritMany(A.NativeTypedData, [A.NativeByteData, A.NativeTypedArray]);
    _inheritMany(A.NativeTypedArray, [A._NativeTypedArrayOfDouble_NativeTypedArray_ListMixin, A._NativeTypedArrayOfInt_NativeTypedArray_ListMixin]);
    _inherit(A._NativeTypedArrayOfDouble_NativeTypedArray_ListMixin_FixedLengthListMixin, A._NativeTypedArrayOfDouble_NativeTypedArray_ListMixin);
    _inherit(A.NativeTypedArrayOfDouble, A._NativeTypedArrayOfDouble_NativeTypedArray_ListMixin_FixedLengthListMixin);
    _inherit(A._NativeTypedArrayOfInt_NativeTypedArray_ListMixin_FixedLengthListMixin, A._NativeTypedArrayOfInt_NativeTypedArray_ListMixin);
    _inherit(A.NativeTypedArrayOfInt, A._NativeTypedArrayOfInt_NativeTypedArray_ListMixin_FixedLengthListMixin);
    _inheritMany(A.NativeTypedArrayOfInt, [A.NativeInt16List, A.NativeInt32List, A.NativeInt8List, A.NativeUint16List, A.NativeUint32List, A.NativeUint8ClampedList, A.NativeUint8List]);
    _inherit(A._TypeError, A._Error);
    _inheritMany(A.Closure0Args, [A._AsyncRun__scheduleImmediateJsOverride_internalCallback, A._AsyncRun__scheduleImmediateWithSetImmediate_internalCallback, A._TimerImpl_internalCallback, A._Future__addListener_closure, A._Future__prependListeners_closure, A._Future__chainForeignFuture_closure1, A._Future__asyncCompleteWithValue_closure, A._Future__chainFuture_closure, A._Future__asyncCompleteError_closure, A._Future__propagateToListeners_handleWhenCompleteCallback, A._Future__propagateToListeners_handleValueCallback, A._Future__propagateToListeners_handleError, A.Stream_length_closure0, A._rootHandleError_closure, A._RootZone_bindCallbackGuarded_closure]);
    _inherit(A._AsyncCompleter, A._Completer);
    _inherit(A._RootZone, A._Zone);
    _inherit(A._IdentityHashMap, A._HashMap);
    _inherit(A.Base64Codec, A.Codec);
    _inherit(A.Converter, A.StreamTransformerBase);
    _inheritMany(A.Converter, [A.Base64Encoder, A.Base64Decoder]);
    _inheritMany(A.ArgumentError, [A.RangeError, A.IndexError]);
    _inheritMany(A.EventTarget, [A.Node, A.WorkerGlobalScope, A.FileWriter, A.MediaStreamTrack, A.MessagePort, A.SourceBuffer, A._SourceBufferList_EventTarget_ListMixin, A.TextTrack, A.TextTrackCue, A._TextTrackList_EventTarget_ListMixin, A.VideoTrackList, A.AudioTrackList, A.BaseAudioContext]);
    _inheritMany(A.Node, [A.Element, A.CharacterData]);
    _inherit(A.HtmlElement, A.Element);
    _inheritMany(A.HtmlElement, [A.AnchorElement, A.AreaElement, A.FormElement, A.SelectElement, A.TrackElement]);
    _inherit(A.CssPerspective, A.CssTransformComponent);
    _inherit(A.CssStyleDeclaration, A._CssStyleDeclaration_JavaScriptObject_CssStyleDeclarationBase);
    _inheritMany(A.CssStyleValue, [A.CssTransformValue, A.CssUnparsedValue]);
    _inherit(A.DedicatedWorkerGlobalScope, A.WorkerGlobalScope);
    _inherit(A._DomRectList_JavaScriptObject_ListMixin_ImmutableListMixin, A._DomRectList_JavaScriptObject_ListMixin);
    _inherit(A.DomRectList, A._DomRectList_JavaScriptObject_ListMixin_ImmutableListMixin);
    _inherit(A._DomStringList_JavaScriptObject_ListMixin_ImmutableListMixin, A._DomStringList_JavaScriptObject_ListMixin);
    _inherit(A.DomStringList, A._DomStringList_JavaScriptObject_ListMixin_ImmutableListMixin);
    _inherit(A.File, A.Blob);
    _inherit(A._FileList_JavaScriptObject_ListMixin_ImmutableListMixin, A._FileList_JavaScriptObject_ListMixin);
    _inherit(A.FileList, A._FileList_JavaScriptObject_ListMixin_ImmutableListMixin);
    _inherit(A._HtmlCollection_JavaScriptObject_ListMixin_ImmutableListMixin, A._HtmlCollection_JavaScriptObject_ListMixin);
    _inherit(A.HtmlCollection, A._HtmlCollection_JavaScriptObject_ListMixin_ImmutableListMixin);
    _inherit(A.MessageEvent, A.Event);
    _inherit(A.MidiInputMap, A._MidiInputMap_JavaScriptObject_MapMixin);
    _inherit(A.MidiOutputMap, A._MidiOutputMap_JavaScriptObject_MapMixin);
    _inherit(A._MimeTypeArray_JavaScriptObject_ListMixin_ImmutableListMixin, A._MimeTypeArray_JavaScriptObject_ListMixin);
    _inherit(A.MimeTypeArray, A._MimeTypeArray_JavaScriptObject_ListMixin_ImmutableListMixin);
    _inherit(A._NodeList_JavaScriptObject_ListMixin_ImmutableListMixin, A._NodeList_JavaScriptObject_ListMixin);
    _inherit(A.NodeList, A._NodeList_JavaScriptObject_ListMixin_ImmutableListMixin);
    _inherit(A._PluginArray_JavaScriptObject_ListMixin_ImmutableListMixin, A._PluginArray_JavaScriptObject_ListMixin);
    _inherit(A.PluginArray, A._PluginArray_JavaScriptObject_ListMixin_ImmutableListMixin);
    _inherit(A.RtcStatsReport, A._RtcStatsReport_JavaScriptObject_MapMixin);
    _inherit(A._SourceBufferList_EventTarget_ListMixin_ImmutableListMixin, A._SourceBufferList_EventTarget_ListMixin);
    _inherit(A.SourceBufferList, A._SourceBufferList_EventTarget_ListMixin_ImmutableListMixin);
    _inherit(A._SpeechGrammarList_JavaScriptObject_ListMixin_ImmutableListMixin, A._SpeechGrammarList_JavaScriptObject_ListMixin);
    _inherit(A.SpeechGrammarList, A._SpeechGrammarList_JavaScriptObject_ListMixin_ImmutableListMixin);
    _inherit(A.Storage, A._Storage_JavaScriptObject_MapMixin);
    _inherit(A._TextTrackCueList_JavaScriptObject_ListMixin_ImmutableListMixin, A._TextTrackCueList_JavaScriptObject_ListMixin);
    _inherit(A.TextTrackCueList, A._TextTrackCueList_JavaScriptObject_ListMixin_ImmutableListMixin);
    _inherit(A._TextTrackList_EventTarget_ListMixin_ImmutableListMixin, A._TextTrackList_EventTarget_ListMixin);
    _inherit(A.TextTrackList, A._TextTrackList_EventTarget_ListMixin_ImmutableListMixin);
    _inherit(A._TouchList_JavaScriptObject_ListMixin_ImmutableListMixin, A._TouchList_JavaScriptObject_ListMixin);
    _inherit(A.TouchList, A._TouchList_JavaScriptObject_ListMixin_ImmutableListMixin);
    _inherit(A.__CssRuleList_JavaScriptObject_ListMixin_ImmutableListMixin, A.__CssRuleList_JavaScriptObject_ListMixin);
    _inherit(A._CssRuleList, A.__CssRuleList_JavaScriptObject_ListMixin_ImmutableListMixin);
    _inherit(A._DomRect, A.DomRectReadOnly);
    _inherit(A.__GamepadList_JavaScriptObject_ListMixin_ImmutableListMixin, A.__GamepadList_JavaScriptObject_ListMixin);
    _inherit(A._GamepadList, A.__GamepadList_JavaScriptObject_ListMixin_ImmutableListMixin);
    _inherit(A.__NamedNodeMap_JavaScriptObject_ListMixin_ImmutableListMixin, A.__NamedNodeMap_JavaScriptObject_ListMixin);
    _inherit(A._NamedNodeMap, A.__NamedNodeMap_JavaScriptObject_ListMixin_ImmutableListMixin);
    _inherit(A.__SpeechRecognitionResultList_JavaScriptObject_ListMixin_ImmutableListMixin, A.__SpeechRecognitionResultList_JavaScriptObject_ListMixin);
    _inherit(A._SpeechRecognitionResultList, A.__SpeechRecognitionResultList_JavaScriptObject_ListMixin_ImmutableListMixin);
    _inherit(A.__StyleSheetList_JavaScriptObject_ListMixin_ImmutableListMixin, A.__StyleSheetList_JavaScriptObject_ListMixin);
    _inherit(A._StyleSheetList, A.__StyleSheetList_JavaScriptObject_ListMixin_ImmutableListMixin);
    _inherit(A._EventStream, A.Stream);
    _inherit(A._EventStreamSubscription, A.StreamSubscription);
    _inherit(A._StructuredCloneDart2Js, A._StructuredClone);
    _inherit(A._AcceptStructuredCloneDart2Js, A._AcceptStructuredClone);
    _inherit(A._LengthList_JavaScriptObject_ListMixin_ImmutableListMixin, A._LengthList_JavaScriptObject_ListMixin);
    _inherit(A.LengthList, A._LengthList_JavaScriptObject_ListMixin_ImmutableListMixin);
    _inherit(A._NumberList_JavaScriptObject_ListMixin_ImmutableListMixin, A._NumberList_JavaScriptObject_ListMixin);
    _inherit(A.NumberList, A._NumberList_JavaScriptObject_ListMixin_ImmutableListMixin);
    _inherit(A._StringList_JavaScriptObject_ListMixin_ImmutableListMixin, A._StringList_JavaScriptObject_ListMixin);
    _inherit(A.StringList, A._StringList_JavaScriptObject_ListMixin_ImmutableListMixin);
    _inherit(A._TransformList_JavaScriptObject_ListMixin_ImmutableListMixin, A._TransformList_JavaScriptObject_ListMixin);
    _inherit(A.TransformList, A._TransformList_JavaScriptObject_ListMixin_ImmutableListMixin);
    _inherit(A.AudioParamMap, A._AudioParamMap_JavaScriptObject_MapMixin);
    _inherit(A.OfflineAudioContext, A.BaseAudioContext);
    _inherit(A.CryptorError, A._Enum);
    _mixin(A.UnmodifiableListBase, A.UnmodifiableListMixin);
    _mixin(A._NativeTypedArrayOfDouble_NativeTypedArray_ListMixin, A.ListMixin);
    _mixin(A._NativeTypedArrayOfDouble_NativeTypedArray_ListMixin_FixedLengthListMixin, A.FixedLengthListMixin);
    _mixin(A._NativeTypedArrayOfInt_NativeTypedArray_ListMixin, A.ListMixin);
    _mixin(A._NativeTypedArrayOfInt_NativeTypedArray_ListMixin_FixedLengthListMixin, A.FixedLengthListMixin);
    _mixin(A._ListBase_Object_ListMixin, A.ListMixin);
    _mixin(A._UnmodifiableMapView_MapView__UnmodifiableMapMixin, A._UnmodifiableMapMixin);
    _mixin(A._CssStyleDeclaration_JavaScriptObject_CssStyleDeclarationBase, A.CssStyleDeclarationBase);
    _mixin(A._DomRectList_JavaScriptObject_ListMixin, A.ListMixin);
    _mixin(A._DomRectList_JavaScriptObject_ListMixin_ImmutableListMixin, A.ImmutableListMixin);
    _mixin(A._DomStringList_JavaScriptObject_ListMixin, A.ListMixin);
    _mixin(A._DomStringList_JavaScriptObject_ListMixin_ImmutableListMixin, A.ImmutableListMixin);
    _mixin(A._FileList_JavaScriptObject_ListMixin, A.ListMixin);
    _mixin(A._FileList_JavaScriptObject_ListMixin_ImmutableListMixin, A.ImmutableListMixin);
    _mixin(A._HtmlCollection_JavaScriptObject_ListMixin, A.ListMixin);
    _mixin(A._HtmlCollection_JavaScriptObject_ListMixin_ImmutableListMixin, A.ImmutableListMixin);
    _mixin(A._MidiInputMap_JavaScriptObject_MapMixin, A.MapMixin);
    _mixin(A._MidiOutputMap_JavaScriptObject_MapMixin, A.MapMixin);
    _mixin(A._MimeTypeArray_JavaScriptObject_ListMixin, A.ListMixin);
    _mixin(A._MimeTypeArray_JavaScriptObject_ListMixin_ImmutableListMixin, A.ImmutableListMixin);
    _mixin(A._NodeList_JavaScriptObject_ListMixin, A.ListMixin);
    _mixin(A._NodeList_JavaScriptObject_ListMixin_ImmutableListMixin, A.ImmutableListMixin);
    _mixin(A._PluginArray_JavaScriptObject_ListMixin, A.ListMixin);
    _mixin(A._PluginArray_JavaScriptObject_ListMixin_ImmutableListMixin, A.ImmutableListMixin);
    _mixin(A._RtcStatsReport_JavaScriptObject_MapMixin, A.MapMixin);
    _mixin(A._SourceBufferList_EventTarget_ListMixin, A.ListMixin);
    _mixin(A._SourceBufferList_EventTarget_ListMixin_ImmutableListMixin, A.ImmutableListMixin);
    _mixin(A._SpeechGrammarList_JavaScriptObject_ListMixin, A.ListMixin);
    _mixin(A._SpeechGrammarList_JavaScriptObject_ListMixin_ImmutableListMixin, A.ImmutableListMixin);
    _mixin(A._Storage_JavaScriptObject_MapMixin, A.MapMixin);
    _mixin(A._TextTrackCueList_JavaScriptObject_ListMixin, A.ListMixin);
    _mixin(A._TextTrackCueList_JavaScriptObject_ListMixin_ImmutableListMixin, A.ImmutableListMixin);
    _mixin(A._TextTrackList_EventTarget_ListMixin, A.ListMixin);
    _mixin(A._TextTrackList_EventTarget_ListMixin_ImmutableListMixin, A.ImmutableListMixin);
    _mixin(A._TouchList_JavaScriptObject_ListMixin, A.ListMixin);
    _mixin(A._TouchList_JavaScriptObject_ListMixin_ImmutableListMixin, A.ImmutableListMixin);
    _mixin(A.__CssRuleList_JavaScriptObject_ListMixin, A.ListMixin);
    _mixin(A.__CssRuleList_JavaScriptObject_ListMixin_ImmutableListMixin, A.ImmutableListMixin);
    _mixin(A.__GamepadList_JavaScriptObject_ListMixin, A.ListMixin);
    _mixin(A.__GamepadList_JavaScriptObject_ListMixin_ImmutableListMixin, A.ImmutableListMixin);
    _mixin(A.__NamedNodeMap_JavaScriptObject_ListMixin, A.ListMixin);
    _mixin(A.__NamedNodeMap_JavaScriptObject_ListMixin_ImmutableListMixin, A.ImmutableListMixin);
    _mixin(A.__SpeechRecognitionResultList_JavaScriptObject_ListMixin, A.ListMixin);
    _mixin(A.__SpeechRecognitionResultList_JavaScriptObject_ListMixin_ImmutableListMixin, A.ImmutableListMixin);
    _mixin(A.__StyleSheetList_JavaScriptObject_ListMixin, A.ListMixin);
    _mixin(A.__StyleSheetList_JavaScriptObject_ListMixin_ImmutableListMixin, A.ImmutableListMixin);
    _mixin(A._LengthList_JavaScriptObject_ListMixin, A.ListMixin);
    _mixin(A._LengthList_JavaScriptObject_ListMixin_ImmutableListMixin, A.ImmutableListMixin);
    _mixin(A._NumberList_JavaScriptObject_ListMixin, A.ListMixin);
    _mixin(A._NumberList_JavaScriptObject_ListMixin_ImmutableListMixin, A.ImmutableListMixin);
    _mixin(A._StringList_JavaScriptObject_ListMixin, A.ListMixin);
    _mixin(A._StringList_JavaScriptObject_ListMixin_ImmutableListMixin, A.ImmutableListMixin);
    _mixin(A._TransformList_JavaScriptObject_ListMixin, A.ListMixin);
    _mixin(A._TransformList_JavaScriptObject_ListMixin_ImmutableListMixin, A.ImmutableListMixin);
    _mixin(A._AudioParamMap_JavaScriptObject_MapMixin, A.MapMixin);
  })();
  var init = {
    typeUniverse: {eC: new Map(), tR: {}, eT: {}, tPV: {}, sEA: []},
    mangledGlobalNames: {int: "int", double: "double", num: "num", String: "String", bool: "bool", Null: "Null", List: "List"},
    mangledNames: {},
    types: ["~()", "bool(Cryptor)", "~(String,@)", "Null(@)", "~(@)", "~(~())", "Null()", "Future<~>(RTCEncodedFrame,TransformStreamDefaultController)", "@(@)", "@(@,String)", "@(String)", "Null(~())", "Null(@,StackTrace)", "~(int,@)", "Null(Object,StackTrace)", "_Future<@>(@)", "~(Object?,Object?)", "~(Symbol0,@)", "~(String,String)", "~(Event)", "~(@,@)", "Null(@,@)", "@(@,@)", "Object?(Object?)", "Null(CryptoKey)", "Null(KeySet)", "Null(~)", "~(MessageEvent)", "Future<Null>(~)"],
    interceptorsByTag: null,
    leafTags: null,
    arrayRti: Symbol("$ti")
  };
  A._Universe_addRules(init.typeUniverse, JSON.parse('{"PlainJavaScriptObject":"LegacyJavaScriptObject","UnknownJavaScriptObject":"LegacyJavaScriptObject","JavaScriptFunction":"LegacyJavaScriptObject","WritableStream":"LegacyJavaScriptObject","ReadableStream":"LegacyJavaScriptObject","TransformStreamDefaultController":"LegacyJavaScriptObject","RTCEncodedFrame":"LegacyJavaScriptObject","TransformStream":"LegacyJavaScriptObject","EncodedStreams":"LegacyJavaScriptObject","RTCEncodedAudioFrame":"LegacyJavaScriptObject","RTCEncodedVideoFrame":"LegacyJavaScriptObject","RTCEncodedFrameMetadata":"LegacyJavaScriptObject","RTCEncodedAudioFrameMetadata":"LegacyJavaScriptObject","RTCEncodedVideoFrameMetadata":"LegacyJavaScriptObject","RTCTransformEvent":"LegacyJavaScriptObject","RTCRtpScriptTransformer":"LegacyJavaScriptObject","RTCRtpScriptTransform":"LegacyJavaScriptObject","Promise":"LegacyJavaScriptObject","Algorithm":"LegacyJavaScriptObject","AesGcmParams":"LegacyJavaScriptObject","TransformMessage":"LegacyJavaScriptObject","EnableTransformMessage":"LegacyJavaScriptObject","RemoveTransformMessage":"LegacyJavaScriptObject","KeyframeEffect":"JavaScriptObject","KeyframeEffectReadOnly":"JavaScriptObject","AnimationEffectReadOnly":"JavaScriptObject","AbortPaymentEvent":"Event","ExtendableEvent":"Event","AudioContext":"BaseAudioContext","AbsoluteOrientationSensor":"EventTarget","OrientationSensor":"EventTarget","Sensor":"EventTarget","MathMLElement":"Element","AudioElement":"HtmlElement","MediaElement":"HtmlElement","HtmlDocument":"Node","Document":"Node","VttCue":"TextTrackCue","ServiceWorkerGlobalScope":"WorkerGlobalScope","CDataSection":"CharacterData","Text":"CharacterData","HtmlFormControlsCollection":"HtmlCollection","CssCharsetRule":"CssRule","CssMatrixComponent":"CssTransformComponent","CssStyleSheet":"StyleSheet","CssurlImageValue":"CssStyleValue","CssImageValue":"CssStyleValue","CssResourceValue":"CssStyleValue","CanvasCaptureMediaStreamTrack":"MediaStreamTrack","NativeFloat32List":"NativeTypedArrayOfDouble","JSBool":{"bool":[]},"JSNull":{"Null":[]},"LegacyJavaScriptObject":{"JavaScriptObject":[],"JSObject":[],"WritableStream":[],"ReadableStream":[],"TransformStreamDefaultController":[],"RTCEncodedFrame":[],"RTCTransformEvent":[],"Algorithm":[]},"JSArray":{"List":["1"],"EfficientLengthIterable":["1"],"Iterable":["1"],"JSIndexable":["1"]},"JSUnmodifiableArray":{"JSArray":["1"],"List":["1"],"EfficientLengthIterable":["1"],"Iterable":["1"],"JSIndexable":["1"]},"ArrayIterator":{"Iterator":["1"]},"JSNumber":{"double":[],"num":[]},"JSInt":{"double":[],"int":[],"num":[]},"JSNumNotInt":{"double":[],"num":[]},"JSString":{"String":[],"JSIndexable":["@"]},"LateError":{"Error":[]},"CodeUnits":{"ListMixin":["int"],"UnmodifiableListMixin":["int"],"List":["int"],"EfficientLengthIterable":["int"],"Iterable":["int"],"ListMixin.E":"int","UnmodifiableListMixin.E":"int"},"EfficientLengthIterable":{"Iterable":["1"]},"ListIterable":{"EfficientLengthIterable":["1"],"Iterable":["1"]},"SubListIterable":{"ListIterable":["1"],"EfficientLengthIterable":["1"],"Iterable":["1"],"Iterable.E":"1","ListIterable.E":"1"},"ListIterator":{"Iterator":["1"]},"MappedIterable":{"Iterable":["2"],"Iterable.E":"2"},"EfficientLengthMappedIterable":{"MappedIterable":["1","2"],"EfficientLengthIterable":["2"],"Iterable":["2"],"Iterable.E":"2"},"MappedIterator":{"Iterator":["2"]},"MappedListIterable":{"ListIterable":["2"],"EfficientLengthIterable":["2"],"Iterable":["2"],"Iterable.E":"2","ListIterable.E":"2"},"WhereIterable":{"Iterable":["1"],"Iterable.E":"1"},"WhereIterator":{"Iterator":["1"]},"UnmodifiableListBase":{"ListMixin":["1"],"UnmodifiableListMixin":["1"],"List":["1"],"EfficientLengthIterable":["1"],"Iterable":["1"]},"Symbol":{"Symbol0":[]},"ConstantMapView":{"UnmodifiableMapView":["1","2"],"_UnmodifiableMapView_MapView__UnmodifiableMapMixin":["1","2"],"MapView":["1","2"],"_UnmodifiableMapMixin":["1","2"],"Map":["1","2"]},"ConstantMap":{"Map":["1","2"]},"ConstantStringMap":{"ConstantMap":["1","2"],"Map":["1","2"]},"_ConstantMapKeyIterable":{"Iterable":["1"],"Iterable.E":"1"},"JSInvocationMirror":{"Invocation":[]},"NullError":{"TypeError":[],"Error":[]},"JsNoSuchMethodError":{"Error":[]},"UnknownJsTypeError":{"Error":[]},"_StackTrace":{"StackTrace":[]},"Closure":{"Function":[]},"Closure0Args":{"Function":[]},"Closure2Args":{"Function":[]},"TearOffClosure":{"Function":[]},"StaticClosure":{"Function":[]},"BoundClosure":{"Function":[]},"RuntimeError":{"Error":[]},"_AssertionError":{"Error":[]},"JsLinkedHashMap":{"MapMixin":["1","2"],"LinkedHashMap":["1","2"],"Map":["1","2"],"MapMixin.K":"1","MapMixin.V":"2"},"LinkedHashMapKeyIterable":{"EfficientLengthIterable":["1"],"Iterable":["1"],"Iterable.E":"1"},"LinkedHashMapKeyIterator":{"Iterator":["1"]},"NativeByteBuffer":{"ByteBuffer":[]},"NativeByteData":{"NativeTypedData":[],"ByteData":[]},"NativeTypedArray":{"JavaScriptIndexingBehavior":["1"],"NativeTypedData":[],"JSIndexable":["1"]},"NativeTypedArrayOfDouble":{"NativeTypedArray":["double"],"ListMixin":["double"],"JavaScriptIndexingBehavior":["double"],"List":["double"],"NativeTypedData":[],"EfficientLengthIterable":["double"],"JSIndexable":["double"],"Iterable":["double"],"FixedLengthListMixin":["double"],"ListMixin.E":"double"},"NativeTypedArrayOfInt":{"NativeTypedArray":["int"],"ListMixin":["int"],"JavaScriptIndexingBehavior":["int"],"List":["int"],"NativeTypedData":[],"EfficientLengthIterable":["int"],"JSIndexable":["int"],"Iterable":["int"],"FixedLengthListMixin":["int"]},"NativeInt16List":{"NativeTypedArrayOfInt":[],"NativeTypedArray":["int"],"ListMixin":["int"],"JavaScriptIndexingBehavior":["int"],"List":["int"],"NativeTypedData":[],"EfficientLengthIterable":["int"],"JSIndexable":["int"],"Iterable":["int"],"FixedLengthListMixin":["int"],"ListMixin.E":"int"},"NativeInt32List":{"NativeTypedArrayOfInt":[],"NativeTypedArray":["int"],"ListMixin":["int"],"JavaScriptIndexingBehavior":["int"],"List":["int"],"NativeTypedData":[],"EfficientLengthIterable":["int"],"JSIndexable":["int"],"Iterable":["int"],"FixedLengthListMixin":["int"],"ListMixin.E":"int"},"NativeInt8List":{"NativeTypedArrayOfInt":[],"NativeTypedArray":["int"],"ListMixin":["int"],"JavaScriptIndexingBehavior":["int"],"List":["int"],"NativeTypedData":[],"EfficientLengthIterable":["int"],"JSIndexable":["int"],"Iterable":["int"],"FixedLengthListMixin":["int"],"ListMixin.E":"int"},"NativeUint16List":{"NativeTypedArrayOfInt":[],"NativeTypedArray":["int"],"ListMixin":["int"],"JavaScriptIndexingBehavior":["int"],"List":["int"],"NativeTypedData":[],"EfficientLengthIterable":["int"],"JSIndexable":["int"],"Iterable":["int"],"FixedLengthListMixin":["int"],"ListMixin.E":"int"},"NativeUint32List":{"NativeTypedArrayOfInt":[],"NativeTypedArray":["int"],"ListMixin":["int"],"JavaScriptIndexingBehavior":["int"],"List":["int"],"NativeTypedData":[],"EfficientLengthIterable":["int"],"JSIndexable":["int"],"Iterable":["int"],"FixedLengthListMixin":["int"],"ListMixin.E":"int"},"NativeUint8ClampedList":{"NativeTypedArrayOfInt":[],"NativeTypedArray":["int"],"ListMixin":["int"],"JavaScriptIndexingBehavior":["int"],"List":["int"],"NativeTypedData":[],"EfficientLengthIterable":["int"],"JSIndexable":["int"],"Iterable":["int"],"FixedLengthListMixin":["int"],"ListMixin.E":"int"},"NativeUint8List":{"NativeTypedArrayOfInt":[],"NativeTypedArray":["int"],"ListMixin":["int"],"Uint8List":[],"JavaScriptIndexingBehavior":["int"],"List":["int"],"NativeTypedData":[],"EfficientLengthIterable":["int"],"JSIndexable":["int"],"Iterable":["int"],"FixedLengthListMixin":["int"],"ListMixin.E":"int"},"_Error":{"Error":[]},"_TypeError":{"TypeError":[],"Error":[]},"_Future":{"Future":["1"]},"_AsyncAwaitCompleter":{"Completer":["1"]},"AsyncError":{"Error":[]},"_Completer":{"Completer":["1"]},"_AsyncCompleter":{"_Completer":["1"],"Completer":["1"]},"_Zone":{"Zone":[]},"_RootZone":{"_Zone":[],"Zone":[]},"_HashMap":{"MapMixin":["1","2"],"Map":["1","2"]},"_IdentityHashMap":{"_HashMap":["1","2"],"MapMixin":["1","2"],"Map":["1","2"],"MapMixin.K":"1","MapMixin.V":"2"},"_HashMapKeyIterable":{"EfficientLengthIterable":["1"],"Iterable":["1"],"Iterable.E":"1"},"_HashMapKeyIterator":{"Iterator":["1"]},"ListBase":{"ListMixin":["1"],"List":["1"],"EfficientLengthIterable":["1"],"Iterable":["1"]},"MapBase":{"MapMixin":["1","2"],"Map":["1","2"]},"MapMixin":{"Map":["1","2"]},"MapView":{"Map":["1","2"]},"UnmodifiableMapView":{"_UnmodifiableMapView_MapView__UnmodifiableMapMixin":["1","2"],"MapView":["1","2"],"_UnmodifiableMapMixin":["1","2"],"Map":["1","2"]},"Base64Codec":{"Codec":["List<int>","String"],"Codec.S":"List<int>"},"double":{"num":[]},"int":{"num":[]},"List":{"EfficientLengthIterable":["1"],"Iterable":["1"]},"AssertionError":{"Error":[]},"TypeError":{"Error":[]},"NullThrownError":{"TypeError":[],"Error":[]},"ArgumentError":{"Error":[]},"RangeError":{"Error":[]},"IndexError":{"Error":[]},"NoSuchMethodError":{"Error":[]},"UnsupportedError":{"Error":[]},"UnimplementedError":{"Error":[]},"StateError":{"Error":[]},"ConcurrentModificationError":{"Error":[]},"OutOfMemoryError":{"Error":[]},"StackOverflowError":{"Error":[]},"CyclicInitializationError":{"Error":[]},"_StringStackTrace":{"StackTrace":[]},"CryptoKey":{"JavaScriptObject":[]},"CssRule":{"JavaScriptObject":[]},"Event":{"JavaScriptObject":[]},"File":{"Blob":[],"JavaScriptObject":[]},"Gamepad":{"JavaScriptObject":[]},"MessageEvent":{"Event":[],"JavaScriptObject":[]},"MimeType":{"JavaScriptObject":[]},"Node":{"EventTarget":[],"JavaScriptObject":[]},"Plugin":{"JavaScriptObject":[]},"SourceBuffer":{"EventTarget":[],"JavaScriptObject":[]},"SpeechGrammar":{"JavaScriptObject":[]},"SpeechRecognitionResult":{"JavaScriptObject":[]},"StyleSheet":{"JavaScriptObject":[]},"TextTrack":{"EventTarget":[],"JavaScriptObject":[]},"TextTrackCue":{"EventTarget":[],"JavaScriptObject":[]},"Touch":{"JavaScriptObject":[]},"HtmlElement":{"Node":[],"EventTarget":[],"JavaScriptObject":[]},"AccessibleNodeList":{"JavaScriptObject":[]},"AnchorElement":{"Node":[],"EventTarget":[],"JavaScriptObject":[]},"AreaElement":{"Node":[],"EventTarget":[],"JavaScriptObject":[]},"Blob":{"JavaScriptObject":[]},"CharacterData":{"Node":[],"EventTarget":[],"JavaScriptObject":[]},"CssPerspective":{"JavaScriptObject":[]},"CssStyleDeclaration":{"JavaScriptObject":[]},"CssStyleValue":{"JavaScriptObject":[]},"CssTransformComponent":{"JavaScriptObject":[]},"CssTransformValue":{"JavaScriptObject":[]},"CssUnparsedValue":{"JavaScriptObject":[]},"DataTransferItem":{"JavaScriptObject":[]},"DataTransferItemList":{"JavaScriptObject":[]},"DedicatedWorkerGlobalScope":{"EventTarget":[],"JavaScriptObject":[]},"DomException":{"JavaScriptObject":[]},"DomRectList":{"ListMixin":["Rectangle<num>"],"ImmutableListMixin":["Rectangle<num>"],"List":["Rectangle<num>"],"JavaScriptIndexingBehavior":["Rectangle<num>"],"JavaScriptObject":[],"EfficientLengthIterable":["Rectangle<num>"],"Iterable":["Rectangle<num>"],"JSIndexable":["Rectangle<num>"],"ImmutableListMixin.E":"Rectangle<num>","ListMixin.E":"Rectangle<num>"},"DomRectReadOnly":{"JavaScriptObject":[],"Rectangle":["num"]},"DomStringList":{"ListMixin":["String"],"ImmutableListMixin":["String"],"List":["String"],"JavaScriptIndexingBehavior":["String"],"JavaScriptObject":[],"EfficientLengthIterable":["String"],"Iterable":["String"],"JSIndexable":["String"],"ImmutableListMixin.E":"String","ListMixin.E":"String"},"DomTokenList":{"JavaScriptObject":[]},"Element":{"Node":[],"EventTarget":[],"JavaScriptObject":[]},"EventTarget":{"JavaScriptObject":[]},"FileList":{"ListMixin":["File"],"ImmutableListMixin":["File"],"List":["File"],"JavaScriptIndexingBehavior":["File"],"JavaScriptObject":[],"EfficientLengthIterable":["File"],"Iterable":["File"],"JSIndexable":["File"],"ImmutableListMixin.E":"File","ListMixin.E":"File"},"FileWriter":{"EventTarget":[],"JavaScriptObject":[]},"FormElement":{"Node":[],"EventTarget":[],"JavaScriptObject":[]},"History":{"JavaScriptObject":[]},"HtmlCollection":{"ListMixin":["Node"],"ImmutableListMixin":["Node"],"List":["Node"],"JavaScriptIndexingBehavior":["Node"],"JavaScriptObject":[],"EfficientLengthIterable":["Node"],"Iterable":["Node"],"JSIndexable":["Node"],"ImmutableListMixin.E":"Node","ListMixin.E":"Node"},"ImageData":{"JavaScriptObject":[]},"Location":{"JavaScriptObject":[]},"MediaDeviceInfo":{"JavaScriptObject":[]},"MediaList":{"JavaScriptObject":[]},"MediaStreamTrack":{"EventTarget":[],"JavaScriptObject":[]},"MessagePort":{"EventTarget":[],"JavaScriptObject":[]},"MidiInputMap":{"JavaScriptObject":[],"MapMixin":["String","@"],"Map":["String","@"],"MapMixin.K":"String","MapMixin.V":"@"},"MidiOutputMap":{"JavaScriptObject":[],"MapMixin":["String","@"],"Map":["String","@"],"MapMixin.K":"String","MapMixin.V":"@"},"MimeTypeArray":{"ListMixin":["MimeType"],"ImmutableListMixin":["MimeType"],"List":["MimeType"],"JavaScriptIndexingBehavior":["MimeType"],"JavaScriptObject":[],"EfficientLengthIterable":["MimeType"],"Iterable":["MimeType"],"JSIndexable":["MimeType"],"ImmutableListMixin.E":"MimeType","ListMixin.E":"MimeType"},"NodeList":{"ListMixin":["Node"],"ImmutableListMixin":["Node"],"List":["Node"],"JavaScriptIndexingBehavior":["Node"],"JavaScriptObject":[],"EfficientLengthIterable":["Node"],"Iterable":["Node"],"JSIndexable":["Node"],"ImmutableListMixin.E":"Node","ListMixin.E":"Node"},"PluginArray":{"ListMixin":["Plugin"],"ImmutableListMixin":["Plugin"],"List":["Plugin"],"JavaScriptIndexingBehavior":["Plugin"],"JavaScriptObject":[],"EfficientLengthIterable":["Plugin"],"Iterable":["Plugin"],"JSIndexable":["Plugin"],"ImmutableListMixin.E":"Plugin","ListMixin.E":"Plugin"},"RtcStatsReport":{"JavaScriptObject":[],"MapMixin":["String","@"],"Map":["String","@"],"MapMixin.K":"String","MapMixin.V":"@"},"SelectElement":{"Node":[],"EventTarget":[],"JavaScriptObject":[]},"SharedArrayBuffer":{"JavaScriptObject":[]},"SourceBufferList":{"ListMixin":["SourceBuffer"],"ImmutableListMixin":["SourceBuffer"],"EventTarget":[],"List":["SourceBuffer"],"JavaScriptIndexingBehavior":["SourceBuffer"],"JavaScriptObject":[],"EfficientLengthIterable":["SourceBuffer"],"Iterable":["SourceBuffer"],"JSIndexable":["SourceBuffer"],"ImmutableListMixin.E":"SourceBuffer","ListMixin.E":"SourceBuffer"},"SpeechGrammarList":{"ListMixin":["SpeechGrammar"],"ImmutableListMixin":["SpeechGrammar"],"List":["SpeechGrammar"],"JavaScriptIndexingBehavior":["SpeechGrammar"],"JavaScriptObject":[],"EfficientLengthIterable":["SpeechGrammar"],"Iterable":["SpeechGrammar"],"JSIndexable":["SpeechGrammar"],"ImmutableListMixin.E":"SpeechGrammar","ListMixin.E":"SpeechGrammar"},"Storage":{"JavaScriptObject":[],"MapMixin":["String","String"],"Map":["String","String"],"MapMixin.K":"String","MapMixin.V":"String"},"TextTrackCueList":{"ListMixin":["TextTrackCue"],"ImmutableListMixin":["TextTrackCue"],"List":["TextTrackCue"],"JavaScriptIndexingBehavior":["TextTrackCue"],"JavaScriptObject":[],"EfficientLengthIterable":["TextTrackCue"],"Iterable":["TextTrackCue"],"JSIndexable":["TextTrackCue"],"ImmutableListMixin.E":"TextTrackCue","ListMixin.E":"TextTrackCue"},"TextTrackList":{"ListMixin":["TextTrack"],"ImmutableListMixin":["TextTrack"],"EventTarget":[],"List":["TextTrack"],"JavaScriptIndexingBehavior":["TextTrack"],"JavaScriptObject":[],"EfficientLengthIterable":["TextTrack"],"Iterable":["TextTrack"],"JSIndexable":["TextTrack"],"ImmutableListMixin.E":"TextTrack","ListMixin.E":"TextTrack"},"TimeRanges":{"JavaScriptObject":[]},"TouchList":{"ListMixin":["Touch"],"ImmutableListMixin":["Touch"],"List":["Touch"],"JavaScriptIndexingBehavior":["Touch"],"JavaScriptObject":[],"EfficientLengthIterable":["Touch"],"Iterable":["Touch"],"JSIndexable":["Touch"],"ImmutableListMixin.E":"Touch","ListMixin.E":"Touch"},"TrackDefaultList":{"JavaScriptObject":[]},"TrackElement":{"Node":[],"EventTarget":[],"JavaScriptObject":[]},"Url":{"JavaScriptObject":[]},"VideoTrack":{"JavaScriptObject":[]},"VideoTrackList":{"EventTarget":[],"JavaScriptObject":[]},"WorkerGlobalScope":{"EventTarget":[],"JavaScriptObject":[]},"_CssRuleList":{"ListMixin":["CssRule"],"ImmutableListMixin":["CssRule"],"List":["CssRule"],"JavaScriptIndexingBehavior":["CssRule"],"JavaScriptObject":[],"EfficientLengthIterable":["CssRule"],"Iterable":["CssRule"],"JSIndexable":["CssRule"],"ImmutableListMixin.E":"CssRule","ListMixin.E":"CssRule"},"_DomRect":{"JavaScriptObject":[],"Rectangle":["num"]},"_GamepadList":{"ListMixin":["Gamepad?"],"ImmutableListMixin":["Gamepad?"],"List":["Gamepad?"],"JavaScriptIndexingBehavior":["Gamepad?"],"JavaScriptObject":[],"EfficientLengthIterable":["Gamepad?"],"Iterable":["Gamepad?"],"JSIndexable":["Gamepad?"],"ImmutableListMixin.E":"Gamepad?","ListMixin.E":"Gamepad?"},"_NamedNodeMap":{"ListMixin":["Node"],"ImmutableListMixin":["Node"],"List":["Node"],"JavaScriptIndexingBehavior":["Node"],"JavaScriptObject":[],"EfficientLengthIterable":["Node"],"Iterable":["Node"],"JSIndexable":["Node"],"ImmutableListMixin.E":"Node","ListMixin.E":"Node"},"_SpeechRecognitionResultList":{"ListMixin":["SpeechRecognitionResult"],"ImmutableListMixin":["SpeechRecognitionResult"],"List":["SpeechRecognitionResult"],"JavaScriptIndexingBehavior":["SpeechRecognitionResult"],"JavaScriptObject":[],"EfficientLengthIterable":["SpeechRecognitionResult"],"Iterable":["SpeechRecognitionResult"],"JSIndexable":["SpeechRecognitionResult"],"ImmutableListMixin.E":"SpeechRecognitionResult","ListMixin.E":"SpeechRecognitionResult"},"_StyleSheetList":{"ListMixin":["StyleSheet"],"ImmutableListMixin":["StyleSheet"],"List":["StyleSheet"],"JavaScriptIndexingBehavior":["StyleSheet"],"JavaScriptObject":[],"EfficientLengthIterable":["StyleSheet"],"Iterable":["StyleSheet"],"JSIndexable":["StyleSheet"],"ImmutableListMixin.E":"StyleSheet","ListMixin.E":"StyleSheet"},"_EventStream":{"Stream":["1"]},"_EventStreamSubscription":{"StreamSubscription":["1"]},"FixedSizeListIterator":{"Iterator":["1"]},"Length":{"JavaScriptObject":[]},"Number":{"JavaScriptObject":[]},"Transform":{"JavaScriptObject":[]},"LengthList":{"ListMixin":["Length"],"ImmutableListMixin":["Length"],"List":["Length"],"JavaScriptObject":[],"EfficientLengthIterable":["Length"],"Iterable":["Length"],"ImmutableListMixin.E":"Length","ListMixin.E":"Length"},"NumberList":{"ListMixin":["Number"],"ImmutableListMixin":["Number"],"List":["Number"],"JavaScriptObject":[],"EfficientLengthIterable":["Number"],"Iterable":["Number"],"ImmutableListMixin.E":"Number","ListMixin.E":"Number"},"PointList":{"JavaScriptObject":[]},"StringList":{"ListMixin":["String"],"ImmutableListMixin":["String"],"List":["String"],"JavaScriptObject":[],"EfficientLengthIterable":["String"],"Iterable":["String"],"ImmutableListMixin.E":"String","ListMixin.E":"String"},"TransformList":{"ListMixin":["Transform"],"ImmutableListMixin":["Transform"],"List":["Transform"],"JavaScriptObject":[],"EfficientLengthIterable":["Transform"],"Iterable":["Transform"],"ImmutableListMixin.E":"Transform","ListMixin.E":"Transform"},"AudioBuffer":{"JavaScriptObject":[]},"AudioParamMap":{"JavaScriptObject":[],"MapMixin":["String","@"],"Map":["String","@"],"MapMixin.K":"String","MapMixin.V":"@"},"AudioTrack":{"JavaScriptObject":[]},"AudioTrackList":{"EventTarget":[],"JavaScriptObject":[]},"BaseAudioContext":{"EventTarget":[],"JavaScriptObject":[]},"OfflineAudioContext":{"EventTarget":[],"JavaScriptObject":[]},"Uint8List":{"List":["int"],"EfficientLengthIterable":["int"],"Iterable":["int"]}}'));
  A._Universe_addErasedTypes(init.typeUniverse, JSON.parse('{"EfficientLengthIterable":1,"UnmodifiableListBase":1,"NativeTypedArray":1,"StreamTransformerBase":2,"ListBase":1,"MapBase":2,"_ListBase_Object_ListMixin":1,"Converter":2,"Promise":1}'));
  var string$ = {
    Error_: "Error handler must accept one Object or one Object and a StackTrace as arguments, and return a value of the returned future's type"
  };
  var type$ = (function rtii() {
    var findType = A.findType;
    return {
      Algorithm: findType("Algorithm"),
      AsyncError: findType("AsyncError"),
      Base64Codec: findType("Base64Codec"),
      Blob: findType("Blob"),
      ByteBuffer: findType("ByteBuffer"),
      ConstantMapView_Symbol_dynamic: findType("ConstantMapView<Symbol0,@>"),
      CryptoKey: findType("CryptoKey"),
      Cryptor: findType("Cryptor"),
      CssRule: findType("CssRule"),
      EfficientLengthIterable_dynamic: findType("EfficientLengthIterable<@>"),
      Error: findType("Error"),
      Event: findType("Event"),
      File: findType("File"),
      FileList: findType("FileList"),
      Function: findType("Function"),
      FutureOr_CryptoKey: findType("CryptoKey/"),
      Future_dynamic: findType("Future<@>"),
      Future_void_Function_RTCEncodedFrame_TransformStreamDefaultController: findType("Future<~>(RTCEncodedFrame,TransformStreamDefaultController)"),
      ImageData: findType("ImageData"),
      Invocation: findType("Invocation"),
      Iterable_dynamic: findType("Iterable<@>"),
      Iterable_int: findType("Iterable<int>"),
      JSArray_String: findType("JSArray<String>"),
      JSArray_dynamic: findType("JSArray<@>"),
      JSArray_int: findType("JSArray<int>"),
      JSIndexable_dynamic: findType("JSIndexable<@>"),
      JSNull: findType("JSNull"),
      JSObject: findType("JSObject"),
      JavaScriptFunction: findType("JavaScriptFunction"),
      JavaScriptIndexingBehavior_dynamic: findType("JavaScriptIndexingBehavior<@>"),
      JavaScriptObject: findType("JavaScriptObject"),
      JsLinkedHashMap_Symbol_dynamic: findType("JsLinkedHashMap<Symbol0,@>"),
      KeySet: findType("KeySet"),
      Length: findType("Length"),
      List_dynamic: findType("List<@>"),
      List_int: findType("List<int>"),
      Map_dynamic_dynamic: findType("Map<@,@>"),
      MessageEvent: findType("MessageEvent"),
      MessagePort: findType("MessagePort"),
      MimeType: findType("MimeType"),
      NativeByteBuffer: findType("NativeByteBuffer"),
      NativeTypedArrayOfInt: findType("NativeTypedArrayOfInt"),
      NativeTypedData: findType("NativeTypedData"),
      Node: findType("Node"),
      Null: findType("Null"),
      Number: findType("Number"),
      Object: findType("Object"),
      Plugin: findType("Plugin"),
      RTCEncodedFrame: findType("RTCEncodedFrame"),
      RTCTransformEvent: findType("RTCTransformEvent"),
      ReadableStream: findType("ReadableStream"),
      Record: findType("Record"),
      Rectangle_num: findType("Rectangle<num>"),
      SharedArrayBuffer: findType("SharedArrayBuffer"),
      SourceBuffer: findType("SourceBuffer"),
      SpeechGrammar: findType("SpeechGrammar"),
      SpeechRecognitionResult: findType("SpeechRecognitionResult"),
      StackTrace: findType("StackTrace"),
      String: findType("String"),
      StyleSheet: findType("StyleSheet"),
      Symbol: findType("Symbol0"),
      TextTrack: findType("TextTrack"),
      TextTrackCue: findType("TextTrackCue"),
      Touch: findType("Touch"),
      Transform: findType("Transform"),
      TransformStreamDefaultController: findType("TransformStreamDefaultController"),
      TypeError: findType("TypeError"),
      Uint8List: findType("Uint8List"),
      UnknownJavaScriptObject: findType("UnknownJavaScriptObject"),
      WritableStream: findType("WritableStream"),
      _AsyncCompleter_void: findType("_AsyncCompleter<~>"),
      _Future_dynamic: findType("_Future<@>"),
      _Future_int: findType("_Future<int>"),
      _Future_void: findType("_Future<~>"),
      _IdentityHashMap_dynamic_dynamic: findType("_IdentityHashMap<@,@>"),
      bool: findType("bool"),
      bool_Function_Object: findType("bool(Object)"),
      double: findType("double"),
      dynamic: findType("@"),
      dynamic_Function: findType("@()"),
      dynamic_Function_Object: findType("@(Object)"),
      dynamic_Function_Object_StackTrace: findType("@(Object,StackTrace)"),
      dynamic_Function_dynamic: findType("@(@)"),
      dynamic_Function_dynamic_dynamic: findType("@(@,@)"),
      int: findType("int"),
      legacy_Never: findType("0&*"),
      legacy_Object: findType("Object*"),
      nullable_EventTarget: findType("EventTarget?"),
      nullable_Future_Null: findType("Future<Null>?"),
      nullable_Gamepad: findType("Gamepad?"),
      nullable_KeySet: findType("KeySet?"),
      nullable_Object: findType("Object?"),
      nullable_String: findType("String?"),
      nullable__FutureListener_dynamic_dynamic: findType("_FutureListener<@,@>?"),
      nullable_dynamic_Function_Event: findType("@(Event)?"),
      nullable_void_Function: findType("~()?"),
      nullable_void_Function_MessageEvent: findType("~(MessageEvent)?"),
      num: findType("num"),
      void: findType("~"),
      void_Function: findType("~()"),
      void_Function_String_String: findType("~(String,String)"),
      void_Function_String_dynamic: findType("~(String,@)")
    };
  })();
  (function constants() {
    var makeConstList = hunkHelpers.makeConstList;
    B.DedicatedWorkerGlobalScope_methods = A.DedicatedWorkerGlobalScope.prototype;
    B.Interceptor_methods = J.Interceptor.prototype;
    B.JSArray_methods = J.JSArray.prototype;
    B.JSInt_methods = J.JSInt.prototype;
    B.JSNumber_methods = J.JSNumber.prototype;
    B.JSString_methods = J.JSString.prototype;
    B.JavaScriptFunction_methods = J.JavaScriptFunction.prototype;
    B.JavaScriptObject_methods = J.JavaScriptObject.prototype;
    B.NativeByteData_methods = A.NativeByteData.prototype;
    B.NativeUint8List_methods = A.NativeUint8List.prototype;
    B.PlainJavaScriptObject_methods = J.PlainJavaScriptObject.prototype;
    B.UnknownJavaScriptObject_methods = J.UnknownJavaScriptObject.prototype;
    B.C_Base64Decoder = new A.Base64Decoder();
    B.C_Base64Encoder = new A.Base64Encoder();
    B.C_JS_CONST = function getTagFallback(o) {
  var s = Object.prototype.toString.call(o);
  return s.substring(8, s.length - 1);
};
    B.C_JS_CONST0 = function() {
  var toStringFunction = Object.prototype.toString;
  function getTag(o) {
    var s = toStringFunction.call(o);
    return s.substring(8, s.length - 1);
  }
  function getUnknownTag(object, tag) {
    if (/^HTML[A-Z].*Element$/.test(tag)) {
      var name = toStringFunction.call(object);
      if (name == "[object Object]") return null;
      return "HTMLElement";
    }
  }
  function getUnknownTagGenericBrowser(object, tag) {
    if (self.HTMLElement && object instanceof HTMLElement) return "HTMLElement";
    return getUnknownTag(object, tag);
  }
  function prototypeForTag(tag) {
    if (typeof window == "undefined") return null;
    if (typeof window[tag] == "undefined") return null;
    var constructor = window[tag];
    if (typeof constructor != "function") return null;
    return constructor.prototype;
  }
  function discriminator(tag) { return null; }
  var isBrowser = typeof navigator == "object";
  return {
    getTag: getTag,
    getUnknownTag: isBrowser ? getUnknownTagGenericBrowser : getUnknownTag,
    prototypeForTag: prototypeForTag,
    discriminator: discriminator };
};
    B.C_JS_CONST6 = function(getTagFallback) {
  return function(hooks) {
    if (typeof navigator != "object") return hooks;
    var ua = navigator.userAgent;
    if (ua.indexOf("DumpRenderTree") >= 0) return hooks;
    if (ua.indexOf("Chrome") >= 0) {
      function confirm(p) {
        return typeof window == "object" && window[p] && window[p].name == p;
      }
      if (confirm("Window") && confirm("HTMLElement")) return hooks;
    }
    hooks.getTag = getTagFallback;
  };
};
    B.C_JS_CONST1 = function(hooks) {
  if (typeof dartExperimentalFixupGetTag != "function") return hooks;
  hooks.getTag = dartExperimentalFixupGetTag(hooks.getTag);
};
    B.C_JS_CONST2 = function(hooks) {
  var getTag = hooks.getTag;
  var prototypeForTag = hooks.prototypeForTag;
  function getTagFixed(o) {
    var tag = getTag(o);
    if (tag == "Document") {
      if (!!o.xmlVersion) return "!Document";
      return "!HTMLDocument";
    }
    return tag;
  }
  function prototypeForTagFixed(tag) {
    if (tag == "Document") return null;
    return prototypeForTag(tag);
  }
  hooks.getTag = getTagFixed;
  hooks.prototypeForTag = prototypeForTagFixed;
};
    B.C_JS_CONST5 = function(hooks) {
  var userAgent = typeof navigator == "object" ? navigator.userAgent : "";
  if (userAgent.indexOf("Firefox") == -1) return hooks;
  var getTag = hooks.getTag;
  var quickMap = {
    "BeforeUnloadEvent": "Event",
    "DataTransfer": "Clipboard",
    "GeoGeolocation": "Geolocation",
    "Location": "!Location",
    "WorkerMessageEvent": "MessageEvent",
    "XMLDocument": "!Document"};
  function getTagFirefox(o) {
    var tag = getTag(o);
    return quickMap[tag] || tag;
  }
  hooks.getTag = getTagFirefox;
};
    B.C_JS_CONST4 = function(hooks) {
  var userAgent = typeof navigator == "object" ? navigator.userAgent : "";
  if (userAgent.indexOf("Trident/") == -1) return hooks;
  var getTag = hooks.getTag;
  var quickMap = {
    "BeforeUnloadEvent": "Event",
    "DataTransfer": "Clipboard",
    "HTMLDDElement": "HTMLElement",
    "HTMLDTElement": "HTMLElement",
    "HTMLPhraseElement": "HTMLElement",
    "Position": "Geoposition"
  };
  function getTagIE(o) {
    var tag = getTag(o);
    var newTag = quickMap[tag];
    if (newTag) return newTag;
    if (tag == "Object") {
      if (window.DataView && (o instanceof window.DataView)) return "DataView";
    }
    return tag;
  }
  function prototypeForTagIE(tag) {
    var constructor = window[tag];
    if (constructor == null) return null;
    return constructor.prototype;
  }
  hooks.getTag = getTagIE;
  hooks.prototypeForTag = prototypeForTagIE;
};
    B.C_JS_CONST3 = function(hooks) { return hooks; }
;
    B.C_OutOfMemoryError = new A.OutOfMemoryError();
    B.C_SentinelValue = new A.SentinelValue();
    B.C__Required = new A._Required();
    B.C__RootZone = new A._RootZone();
    B.C__StringStackTrace = new A._StringStackTrace();
    B.CryptorError_0 = new A.CryptorError("kNew");
    B.CryptorError_1 = new A.CryptorError("kOk");
    B.CryptorError_2 = new A.CryptorError("kDecryptError");
    B.CryptorError_3 = new A.CryptorError("kEncryptError");
    B.CryptorError_5 = new A.CryptorError("kMissingKey");
    B.CryptorError_6 = new A.CryptorError("kKeyRatcheted");
    B.CryptorError_7 = new A.CryptorError("kInternalError");
    B.CryptorError_8 = new A.CryptorError("kDisposed");
    B.List_empty = A._setArrayType(makeConstList([]), type$.JSArray_dynamic);
    B.List_empty0 = A._setArrayType(makeConstList([]), A.findType("JSArray<Symbol0>"));
    B.Map_empty = new A.ConstantStringMap(0, {}, B.List_empty0, A.findType("ConstantStringMap<Symbol0,@>"));
    B.Symbol_call = new A.Symbol("call");
    B.Type_JSObject_8k0 = A.typeLiteral("JSObject");
    B.Type_Object_xQ6 = A.typeLiteral("Object");
  })();
  (function staticFields() {
    $._JS_INTEROP_INTERCEPTOR_TAG = null;
    $.Primitives__identityHashCodeProperty = null;
    $.BoundClosure__receiverFieldNameCache = null;
    $.BoundClosure__interceptorFieldNameCache = null;
    $.getTagFunction = null;
    $.alternateTagFunction = null;
    $.prototypeForTagFunction = null;
    $.dispatchRecordsForInstanceTags = null;
    $.interceptorsForUncacheableTags = null;
    $.initNativeDispatchFlag = null;
    $._nextCallback = null;
    $._lastCallback = null;
    $._lastPriorityCallback = null;
    $._isInCallbackLoop = false;
    $.Zone__current = B.C__RootZone;
    $._toStringVisiting = A._setArrayType([], A.findType("JSArray<Object>"));
    $.participantCryptors = A._setArrayType([], A.findType("JSArray<Cryptor>"));
  })();
  (function lazyInitializers() {
    var _lazyFinal = hunkHelpers.lazyFinal,
      _lazy = hunkHelpers.lazy;
    _lazyFinal($, "DART_CLOSURE_PROPERTY_NAME", "$get$DART_CLOSURE_PROPERTY_NAME", () => A.getIsolateAffinityTag("_$dart_dartClosure"));
    _lazyFinal($, "_CopyingBytesBuilder__emptyList", "$get$_CopyingBytesBuilder__emptyList", () => A.NativeUint8List_NativeUint8List(0));
    _lazyFinal($, "TypeErrorDecoder_noSuchMethodPattern", "$get$TypeErrorDecoder_noSuchMethodPattern", () => A.TypeErrorDecoder_extractPattern(A.TypeErrorDecoder_provokeCallErrorOn({
      toString: function() {
        return "$receiver$";
      }
    })));
    _lazyFinal($, "TypeErrorDecoder_notClosurePattern", "$get$TypeErrorDecoder_notClosurePattern", () => A.TypeErrorDecoder_extractPattern(A.TypeErrorDecoder_provokeCallErrorOn({$method$: null,
      toString: function() {
        return "$receiver$";
      }
    })));
    _lazyFinal($, "TypeErrorDecoder_nullCallPattern", "$get$TypeErrorDecoder_nullCallPattern", () => A.TypeErrorDecoder_extractPattern(A.TypeErrorDecoder_provokeCallErrorOn(null)));
    _lazyFinal($, "TypeErrorDecoder_nullLiteralCallPattern", "$get$TypeErrorDecoder_nullLiteralCallPattern", () => A.TypeErrorDecoder_extractPattern(function() {
      var $argumentsExpr$ = "$arguments$";
      try {
        null.$method$($argumentsExpr$);
      } catch (e) {
        return e.message;
      }
    }()));
    _lazyFinal($, "TypeErrorDecoder_undefinedCallPattern", "$get$TypeErrorDecoder_undefinedCallPattern", () => A.TypeErrorDecoder_extractPattern(A.TypeErrorDecoder_provokeCallErrorOn(void 0)));
    _lazyFinal($, "TypeErrorDecoder_undefinedLiteralCallPattern", "$get$TypeErrorDecoder_undefinedLiteralCallPattern", () => A.TypeErrorDecoder_extractPattern(function() {
      var $argumentsExpr$ = "$arguments$";
      try {
        (void 0).$method$($argumentsExpr$);
      } catch (e) {
        return e.message;
      }
    }()));
    _lazyFinal($, "TypeErrorDecoder_nullPropertyPattern", "$get$TypeErrorDecoder_nullPropertyPattern", () => A.TypeErrorDecoder_extractPattern(A.TypeErrorDecoder_provokePropertyErrorOn(null)));
    _lazyFinal($, "TypeErrorDecoder_nullLiteralPropertyPattern", "$get$TypeErrorDecoder_nullLiteralPropertyPattern", () => A.TypeErrorDecoder_extractPattern(function() {
      try {
        null.$method$;
      } catch (e) {
        return e.message;
      }
    }()));
    _lazyFinal($, "TypeErrorDecoder_undefinedPropertyPattern", "$get$TypeErrorDecoder_undefinedPropertyPattern", () => A.TypeErrorDecoder_extractPattern(A.TypeErrorDecoder_provokePropertyErrorOn(void 0)));
    _lazyFinal($, "TypeErrorDecoder_undefinedLiteralPropertyPattern", "$get$TypeErrorDecoder_undefinedLiteralPropertyPattern", () => A.TypeErrorDecoder_extractPattern(function() {
      try {
        (void 0).$method$;
      } catch (e) {
        return e.message;
      }
    }()));
    _lazyFinal($, "_AsyncRun__scheduleImmediateClosure", "$get$_AsyncRun__scheduleImmediateClosure", () => A._AsyncRun__initializeScheduleImmediate());
    _lazyFinal($, "_Base64Decoder__inverseAlphabet", "$get$_Base64Decoder__inverseAlphabet", () => new Int8Array(A._ensureNativeList(A._setArrayType([-2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -1, -2, -2, -2, -2, -2, 62, -2, 62, -2, 63, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, -2, -2, -2, -1, -2, -2, -2, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, -2, -2, -2, -2, 63, -2, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, -2, -2, -2, -2, -2], type$.JSArray_int))));
    _lazy($, "_Base64Decoder__emptyBuffer", "$get$_Base64Decoder__emptyBuffer", () => A.NativeUint8List_NativeUint8List(0));
    _lazyFinal($, "_hashSeed", "$get$_hashSeed", () => A.objectHashCode(B.Type_Object_xQ6));
    _lazyFinal($, "Random__secureRandom", "$get$Random__secureRandom", () => {
      var t1 = new A._JSSecureRandom(A.NativeByteData_NativeByteData(8));
      t1._JSSecureRandom$0();
      return t1;
    });
    _lazy($, "keyProviderOptions", "$get$keyProviderOptions", () => A.KeyOptions$(A.NativeUint8List_NativeUint8List$fromList(new A.CodeUnits("ratchetSalt")), 16, true));
  })();
  (function nativeSupport() {
    !function() {
      var intern = function(s) {
        var o = {};
        o[s] = 1;
        return Object.keys(hunkHelpers.convertToFastObject(o))[0];
      };
      init.getIsolateTag = function(name) {
        return intern("___dart_" + name + init.isolateTag);
      };
      var tableProperty = "___dart_isolate_tags_";
      var usedProperties = Object[tableProperty] || (Object[tableProperty] = Object.create(null));
      var rootProperty = "_ZxYxX";
      for (var i = 0;; i++) {
        var property = intern(rootProperty + "_" + i + "_");
        if (!(property in usedProperties)) {
          usedProperties[property] = 1;
          init.isolateTag = property;
          break;
        }
      }
      init.dispatchPropertyName = init.getIsolateTag("dispatch_record");
    }();
    hunkHelpers.setOrUpdateInterceptorsByTag({WebGL: J.Interceptor, AnimationEffectReadOnly: J.JavaScriptObject, AnimationEffectTiming: J.JavaScriptObject, AnimationEffectTimingReadOnly: J.JavaScriptObject, AnimationTimeline: J.JavaScriptObject, AnimationWorkletGlobalScope: J.JavaScriptObject, AuthenticatorAssertionResponse: J.JavaScriptObject, AuthenticatorAttestationResponse: J.JavaScriptObject, AuthenticatorResponse: J.JavaScriptObject, BackgroundFetchFetch: J.JavaScriptObject, BackgroundFetchManager: J.JavaScriptObject, BackgroundFetchSettledFetch: J.JavaScriptObject, BarProp: J.JavaScriptObject, BarcodeDetector: J.JavaScriptObject, BluetoothRemoteGATTDescriptor: J.JavaScriptObject, Body: J.JavaScriptObject, BudgetState: J.JavaScriptObject, CacheStorage: J.JavaScriptObject, CanvasGradient: J.JavaScriptObject, CanvasPattern: J.JavaScriptObject, CanvasRenderingContext2D: J.JavaScriptObject, Client: J.JavaScriptObject, Clients: J.JavaScriptObject, CookieStore: J.JavaScriptObject, Coordinates: J.JavaScriptObject, Credential: J.JavaScriptObject, CredentialUserData: J.JavaScriptObject, CredentialsContainer: J.JavaScriptObject, Crypto: J.JavaScriptObject, CSS: J.JavaScriptObject, CSSVariableReferenceValue: J.JavaScriptObject, CustomElementRegistry: J.JavaScriptObject, DataTransfer: J.JavaScriptObject, DeprecatedStorageInfo: J.JavaScriptObject, DeprecatedStorageQuota: J.JavaScriptObject, DeprecationReport: J.JavaScriptObject, DetectedBarcode: J.JavaScriptObject, DetectedFace: J.JavaScriptObject, DetectedText: J.JavaScriptObject, DeviceAcceleration: J.JavaScriptObject, DeviceRotationRate: J.JavaScriptObject, DirectoryEntry: J.JavaScriptObject, webkitFileSystemDirectoryEntry: J.JavaScriptObject, FileSystemDirectoryEntry: J.JavaScriptObject, DirectoryReader: J.JavaScriptObject, WebKitDirectoryReader: J.JavaScriptObject, webkitFileSystemDirectoryReader: J.JavaScriptObject, FileSystemDirectoryReader: J.JavaScriptObject, DocumentOrShadowRoot: J.JavaScriptObject, DocumentTimeline: J.JavaScriptObject, DOMError: J.JavaScriptObject, DOMImplementation: J.JavaScriptObject, Iterator: J.JavaScriptObject, DOMMatrix: J.JavaScriptObject, DOMMatrixReadOnly: J.JavaScriptObject, DOMParser: J.JavaScriptObject, DOMPoint: J.JavaScriptObject, DOMPointReadOnly: J.JavaScriptObject, DOMQuad: J.JavaScriptObject, DOMStringMap: J.JavaScriptObject, Entry: J.JavaScriptObject, webkitFileSystemEntry: J.JavaScriptObject, FileSystemEntry: J.JavaScriptObject, External: J.JavaScriptObject, FaceDetector: J.JavaScriptObject, FederatedCredential: J.JavaScriptObject, FileEntry: J.JavaScriptObject, webkitFileSystemFileEntry: J.JavaScriptObject, FileSystemFileEntry: J.JavaScriptObject, DOMFileSystem: J.JavaScriptObject, WebKitFileSystem: J.JavaScriptObject, webkitFileSystem: J.JavaScriptObject, FileSystem: J.JavaScriptObject, FontFace: J.JavaScriptObject, FontFaceSource: J.JavaScriptObject, FormData: J.JavaScriptObject, GamepadButton: J.JavaScriptObject, GamepadPose: J.JavaScriptObject, Geolocation: J.JavaScriptObject, Position: J.JavaScriptObject, GeolocationPosition: J.JavaScriptObject, Headers: J.JavaScriptObject, HTMLHyperlinkElementUtils: J.JavaScriptObject, IdleDeadline: J.JavaScriptObject, ImageBitmap: J.JavaScriptObject, ImageBitmapRenderingContext: J.JavaScriptObject, ImageCapture: J.JavaScriptObject, InputDeviceCapabilities: J.JavaScriptObject, IntersectionObserver: J.JavaScriptObject, IntersectionObserverEntry: J.JavaScriptObject, InterventionReport: J.JavaScriptObject, KeyframeEffect: J.JavaScriptObject, KeyframeEffectReadOnly: J.JavaScriptObject, MediaCapabilities: J.JavaScriptObject, MediaCapabilitiesInfo: J.JavaScriptObject, MediaError: J.JavaScriptObject, MediaKeyStatusMap: J.JavaScriptObject, MediaKeySystemAccess: J.JavaScriptObject, MediaKeys: J.JavaScriptObject, MediaKeysPolicy: J.JavaScriptObject, MediaMetadata: J.JavaScriptObject, MediaSession: J.JavaScriptObject, MediaSettingsRange: J.JavaScriptObject, MemoryInfo: J.JavaScriptObject, MessageChannel: J.JavaScriptObject, Metadata: J.JavaScriptObject, MutationObserver: J.JavaScriptObject, WebKitMutationObserver: J.JavaScriptObject, MutationRecord: J.JavaScriptObject, NavigationPreloadManager: J.JavaScriptObject, Navigator: J.JavaScriptObject, NavigatorAutomationInformation: J.JavaScriptObject, NavigatorConcurrentHardware: J.JavaScriptObject, NavigatorCookies: J.JavaScriptObject, NavigatorUserMediaError: J.JavaScriptObject, NodeFilter: J.JavaScriptObject, NodeIterator: J.JavaScriptObject, NonDocumentTypeChildNode: J.JavaScriptObject, NonElementParentNode: J.JavaScriptObject, NoncedElement: J.JavaScriptObject, OffscreenCanvasRenderingContext2D: J.JavaScriptObject, OverconstrainedError: J.JavaScriptObject, PaintRenderingContext2D: J.JavaScriptObject, PaintSize: J.JavaScriptObject, PaintWorkletGlobalScope: J.JavaScriptObject, PasswordCredential: J.JavaScriptObject, Path2D: J.JavaScriptObject, PaymentAddress: J.JavaScriptObject, PaymentInstruments: J.JavaScriptObject, PaymentManager: J.JavaScriptObject, PaymentResponse: J.JavaScriptObject, PerformanceEntry: J.JavaScriptObject, PerformanceLongTaskTiming: J.JavaScriptObject, PerformanceMark: J.JavaScriptObject, PerformanceMeasure: J.JavaScriptObject, PerformanceNavigation: J.JavaScriptObject, PerformanceNavigationTiming: J.JavaScriptObject, PerformanceObserver: J.JavaScriptObject, PerformanceObserverEntryList: J.JavaScriptObject, PerformancePaintTiming: J.JavaScriptObject, PerformanceResourceTiming: J.JavaScriptObject, PerformanceServerTiming: J.JavaScriptObject, PerformanceTiming: J.JavaScriptObject, Permissions: J.JavaScriptObject, PhotoCapabilities: J.JavaScriptObject, PositionError: J.JavaScriptObject, GeolocationPositionError: J.JavaScriptObject, Presentation: J.JavaScriptObject, PresentationReceiver: J.JavaScriptObject, PublicKeyCredential: J.JavaScriptObject, PushManager: J.JavaScriptObject, PushMessageData: J.JavaScriptObject, PushSubscription: J.JavaScriptObject, PushSubscriptionOptions: J.JavaScriptObject, Range: J.JavaScriptObject, RelatedApplication: J.JavaScriptObject, ReportBody: J.JavaScriptObject, ReportingObserver: J.JavaScriptObject, ResizeObserver: J.JavaScriptObject, ResizeObserverEntry: J.JavaScriptObject, RTCCertificate: J.JavaScriptObject, RTCIceCandidate: J.JavaScriptObject, mozRTCIceCandidate: J.JavaScriptObject, RTCLegacyStatsReport: J.JavaScriptObject, RTCRtpContributingSource: J.JavaScriptObject, RTCRtpReceiver: J.JavaScriptObject, RTCRtpSender: J.JavaScriptObject, RTCSessionDescription: J.JavaScriptObject, mozRTCSessionDescription: J.JavaScriptObject, RTCStatsResponse: J.JavaScriptObject, Screen: J.JavaScriptObject, ScrollState: J.JavaScriptObject, ScrollTimeline: J.JavaScriptObject, Selection: J.JavaScriptObject, SpeechRecognitionAlternative: J.JavaScriptObject, SpeechSynthesisVoice: J.JavaScriptObject, StaticRange: J.JavaScriptObject, StorageManager: J.JavaScriptObject, StyleMedia: J.JavaScriptObject, StylePropertyMap: J.JavaScriptObject, StylePropertyMapReadonly: J.JavaScriptObject, SyncManager: J.JavaScriptObject, TaskAttributionTiming: J.JavaScriptObject, TextDetector: J.JavaScriptObject, TextMetrics: J.JavaScriptObject, TrackDefault: J.JavaScriptObject, TreeWalker: J.JavaScriptObject, TrustedHTML: J.JavaScriptObject, TrustedScriptURL: J.JavaScriptObject, TrustedURL: J.JavaScriptObject, UnderlyingSourceBase: J.JavaScriptObject, URLSearchParams: J.JavaScriptObject, VRCoordinateSystem: J.JavaScriptObject, VRDisplayCapabilities: J.JavaScriptObject, VREyeParameters: J.JavaScriptObject, VRFrameData: J.JavaScriptObject, VRFrameOfReference: J.JavaScriptObject, VRPose: J.JavaScriptObject, VRStageBounds: J.JavaScriptObject, VRStageBoundsPoint: J.JavaScriptObject, VRStageParameters: J.JavaScriptObject, ValidityState: J.JavaScriptObject, VideoPlaybackQuality: J.JavaScriptObject, VTTRegion: J.JavaScriptObject, WindowClient: J.JavaScriptObject, WorkletAnimation: J.JavaScriptObject, WorkletGlobalScope: J.JavaScriptObject, XPathEvaluator: J.JavaScriptObject, XPathExpression: J.JavaScriptObject, XPathNSResolver: J.JavaScriptObject, XPathResult: J.JavaScriptObject, XMLSerializer: J.JavaScriptObject, XSLTProcessor: J.JavaScriptObject, Bluetooth: J.JavaScriptObject, BluetoothCharacteristicProperties: J.JavaScriptObject, BluetoothRemoteGATTServer: J.JavaScriptObject, BluetoothRemoteGATTService: J.JavaScriptObject, BluetoothUUID: J.JavaScriptObject, BudgetService: J.JavaScriptObject, Cache: J.JavaScriptObject, DOMFileSystemSync: J.JavaScriptObject, DirectoryEntrySync: J.JavaScriptObject, DirectoryReaderSync: J.JavaScriptObject, EntrySync: J.JavaScriptObject, FileEntrySync: J.JavaScriptObject, FileReaderSync: J.JavaScriptObject, FileWriterSync: J.JavaScriptObject, HTMLAllCollection: J.JavaScriptObject, Mojo: J.JavaScriptObject, MojoHandle: J.JavaScriptObject, MojoWatcher: J.JavaScriptObject, NFC: J.JavaScriptObject, PagePopupController: J.JavaScriptObject, Report: J.JavaScriptObject, Request: J.JavaScriptObject, Response: J.JavaScriptObject, SubtleCrypto: J.JavaScriptObject, USBAlternateInterface: J.JavaScriptObject, USBConfiguration: J.JavaScriptObject, USBDevice: J.JavaScriptObject, USBEndpoint: J.JavaScriptObject, USBInTransferResult: J.JavaScriptObject, USBInterface: J.JavaScriptObject, USBIsochronousInTransferPacket: J.JavaScriptObject, USBIsochronousInTransferResult: J.JavaScriptObject, USBIsochronousOutTransferPacket: J.JavaScriptObject, USBIsochronousOutTransferResult: J.JavaScriptObject, USBOutTransferResult: J.JavaScriptObject, WorkerLocation: J.JavaScriptObject, WorkerNavigator: J.JavaScriptObject, Worklet: J.JavaScriptObject, IDBCursor: J.JavaScriptObject, IDBCursorWithValue: J.JavaScriptObject, IDBFactory: J.JavaScriptObject, IDBIndex: J.JavaScriptObject, IDBKeyRange: J.JavaScriptObject, IDBObjectStore: J.JavaScriptObject, IDBObservation: J.JavaScriptObject, IDBObserver: J.JavaScriptObject, IDBObserverChanges: J.JavaScriptObject, SVGAngle: J.JavaScriptObject, SVGAnimatedAngle: J.JavaScriptObject, SVGAnimatedBoolean: J.JavaScriptObject, SVGAnimatedEnumeration: J.JavaScriptObject, SVGAnimatedInteger: J.JavaScriptObject, SVGAnimatedLength: J.JavaScriptObject, SVGAnimatedLengthList: J.JavaScriptObject, SVGAnimatedNumber: J.JavaScriptObject, SVGAnimatedNumberList: J.JavaScriptObject, SVGAnimatedPreserveAspectRatio: J.JavaScriptObject, SVGAnimatedRect: J.JavaScriptObject, SVGAnimatedString: J.JavaScriptObject, SVGAnimatedTransformList: J.JavaScriptObject, SVGMatrix: J.JavaScriptObject, SVGPoint: J.JavaScriptObject, SVGPreserveAspectRatio: J.JavaScriptObject, SVGRect: J.JavaScriptObject, SVGUnitTypes: J.JavaScriptObject, AudioListener: J.JavaScriptObject, AudioParam: J.JavaScriptObject, AudioWorkletGlobalScope: J.JavaScriptObject, AudioWorkletProcessor: J.JavaScriptObject, PeriodicWave: J.JavaScriptObject, WebGLActiveInfo: J.JavaScriptObject, ANGLEInstancedArrays: J.JavaScriptObject, ANGLE_instanced_arrays: J.JavaScriptObject, WebGLBuffer: J.JavaScriptObject, WebGLCanvas: J.JavaScriptObject, WebGLColorBufferFloat: J.JavaScriptObject, WebGLCompressedTextureASTC: J.JavaScriptObject, WebGLCompressedTextureATC: J.JavaScriptObject, WEBGL_compressed_texture_atc: J.JavaScriptObject, WebGLCompressedTextureETC1: J.JavaScriptObject, WEBGL_compressed_texture_etc1: J.JavaScriptObject, WebGLCompressedTextureETC: J.JavaScriptObject, WebGLCompressedTexturePVRTC: J.JavaScriptObject, WEBGL_compressed_texture_pvrtc: J.JavaScriptObject, WebGLCompressedTextureS3TC: J.JavaScriptObject, WEBGL_compressed_texture_s3tc: J.JavaScriptObject, WebGLCompressedTextureS3TCsRGB: J.JavaScriptObject, WebGLDebugRendererInfo: J.JavaScriptObject, WEBGL_debug_renderer_info: J.JavaScriptObject, WebGLDebugShaders: J.JavaScriptObject, WEBGL_debug_shaders: J.JavaScriptObject, WebGLDepthTexture: J.JavaScriptObject, WEBGL_depth_texture: J.JavaScriptObject, WebGLDrawBuffers: J.JavaScriptObject, WEBGL_draw_buffers: J.JavaScriptObject, EXTsRGB: J.JavaScriptObject, EXT_sRGB: J.JavaScriptObject, EXTBlendMinMax: J.JavaScriptObject, EXT_blend_minmax: J.JavaScriptObject, EXTColorBufferFloat: J.JavaScriptObject, EXTColorBufferHalfFloat: J.JavaScriptObject, EXTDisjointTimerQuery: J.JavaScriptObject, EXTDisjointTimerQueryWebGL2: J.JavaScriptObject, EXTFragDepth: J.JavaScriptObject, EXT_frag_depth: J.JavaScriptObject, EXTShaderTextureLOD: J.JavaScriptObject, EXT_shader_texture_lod: J.JavaScriptObject, EXTTextureFilterAnisotropic: J.JavaScriptObject, EXT_texture_filter_anisotropic: J.JavaScriptObject, WebGLFramebuffer: J.JavaScriptObject, WebGLGetBufferSubDataAsync: J.JavaScriptObject, WebGLLoseContext: J.JavaScriptObject, WebGLExtensionLoseContext: J.JavaScriptObject, WEBGL_lose_context: J.JavaScriptObject, OESElementIndexUint: J.JavaScriptObject, OES_element_index_uint: J.JavaScriptObject, OESStandardDerivatives: J.JavaScriptObject, OES_standard_derivatives: J.JavaScriptObject, OESTextureFloat: J.JavaScriptObject, OES_texture_float: J.JavaScriptObject, OESTextureFloatLinear: J.JavaScriptObject, OES_texture_float_linear: J.JavaScriptObject, OESTextureHalfFloat: J.JavaScriptObject, OES_texture_half_float: J.JavaScriptObject, OESTextureHalfFloatLinear: J.JavaScriptObject, OES_texture_half_float_linear: J.JavaScriptObject, OESVertexArrayObject: J.JavaScriptObject, OES_vertex_array_object: J.JavaScriptObject, WebGLProgram: J.JavaScriptObject, WebGLQuery: J.JavaScriptObject, WebGLRenderbuffer: J.JavaScriptObject, WebGLRenderingContext: J.JavaScriptObject, WebGL2RenderingContext: J.JavaScriptObject, WebGLSampler: J.JavaScriptObject, WebGLShader: J.JavaScriptObject, WebGLShaderPrecisionFormat: J.JavaScriptObject, WebGLSync: J.JavaScriptObject, WebGLTexture: J.JavaScriptObject, WebGLTimerQueryEXT: J.JavaScriptObject, WebGLTransformFeedback: J.JavaScriptObject, WebGLUniformLocation: J.JavaScriptObject, WebGLVertexArrayObject: J.JavaScriptObject, WebGLVertexArrayObjectOES: J.JavaScriptObject, WebGL2RenderingContextBase: J.JavaScriptObject, ArrayBuffer: A.NativeByteBuffer, ArrayBufferView: A.NativeTypedData, DataView: A.NativeByteData, Float32Array: A.NativeTypedArrayOfDouble, Float64Array: A.NativeTypedArrayOfDouble, Int16Array: A.NativeInt16List, Int32Array: A.NativeInt32List, Int8Array: A.NativeInt8List, Uint16Array: A.NativeUint16List, Uint32Array: A.NativeUint32List, Uint8ClampedArray: A.NativeUint8ClampedList, CanvasPixelArray: A.NativeUint8ClampedList, Uint8Array: A.NativeUint8List, HTMLAudioElement: A.HtmlElement, HTMLBRElement: A.HtmlElement, HTMLBaseElement: A.HtmlElement, HTMLBodyElement: A.HtmlElement, HTMLButtonElement: A.HtmlElement, HTMLCanvasElement: A.HtmlElement, HTMLContentElement: A.HtmlElement, HTMLDListElement: A.HtmlElement, HTMLDataElement: A.HtmlElement, HTMLDataListElement: A.HtmlElement, HTMLDetailsElement: A.HtmlElement, HTMLDialogElement: A.HtmlElement, HTMLDivElement: A.HtmlElement, HTMLEmbedElement: A.HtmlElement, HTMLFieldSetElement: A.HtmlElement, HTMLHRElement: A.HtmlElement, HTMLHeadElement: A.HtmlElement, HTMLHeadingElement: A.HtmlElement, HTMLHtmlElement: A.HtmlElement, HTMLIFrameElement: A.HtmlElement, HTMLImageElement: A.HtmlElement, HTMLInputElement: A.HtmlElement, HTMLLIElement: A.HtmlElement, HTMLLabelElement: A.HtmlElement, HTMLLegendElement: A.HtmlElement, HTMLLinkElement: A.HtmlElement, HTMLMapElement: A.HtmlElement, HTMLMediaElement: A.HtmlElement, HTMLMenuElement: A.HtmlElement, HTMLMetaElement: A.HtmlElement, HTMLMeterElement: A.HtmlElement, HTMLModElement: A.HtmlElement, HTMLOListElement: A.HtmlElement, HTMLObjectElement: A.HtmlElement, HTMLOptGroupElement: A.HtmlElement, HTMLOptionElement: A.HtmlElement, HTMLOutputElement: A.HtmlElement, HTMLParagraphElement: A.HtmlElement, HTMLParamElement: A.HtmlElement, HTMLPictureElement: A.HtmlElement, HTMLPreElement: A.HtmlElement, HTMLProgressElement: A.HtmlElement, HTMLQuoteElement: A.HtmlElement, HTMLScriptElement: A.HtmlElement, HTMLShadowElement: A.HtmlElement, HTMLSlotElement: A.HtmlElement, HTMLSourceElement: A.HtmlElement, HTMLSpanElement: A.HtmlElement, HTMLStyleElement: A.HtmlElement, HTMLTableCaptionElement: A.HtmlElement, HTMLTableCellElement: A.HtmlElement, HTMLTableDataCellElement: A.HtmlElement, HTMLTableHeaderCellElement: A.HtmlElement, HTMLTableColElement: A.HtmlElement, HTMLTableElement: A.HtmlElement, HTMLTableRowElement: A.HtmlElement, HTMLTableSectionElement: A.HtmlElement, HTMLTemplateElement: A.HtmlElement, HTMLTextAreaElement: A.HtmlElement, HTMLTimeElement: A.HtmlElement, HTMLTitleElement: A.HtmlElement, HTMLUListElement: A.HtmlElement, HTMLUnknownElement: A.HtmlElement, HTMLVideoElement: A.HtmlElement, HTMLDirectoryElement: A.HtmlElement, HTMLFontElement: A.HtmlElement, HTMLFrameElement: A.HtmlElement, HTMLFrameSetElement: A.HtmlElement, HTMLMarqueeElement: A.HtmlElement, HTMLElement: A.HtmlElement, AccessibleNodeList: A.AccessibleNodeList, HTMLAnchorElement: A.AnchorElement, HTMLAreaElement: A.AreaElement, Blob: A.Blob, CDATASection: A.CharacterData, CharacterData: A.CharacterData, Comment: A.CharacterData, ProcessingInstruction: A.CharacterData, Text: A.CharacterData, CryptoKey: A.CryptoKey, CSSPerspective: A.CssPerspective, CSSCharsetRule: A.CssRule, CSSConditionRule: A.CssRule, CSSFontFaceRule: A.CssRule, CSSGroupingRule: A.CssRule, CSSImportRule: A.CssRule, CSSKeyframeRule: A.CssRule, MozCSSKeyframeRule: A.CssRule, WebKitCSSKeyframeRule: A.CssRule, CSSKeyframesRule: A.CssRule, MozCSSKeyframesRule: A.CssRule, WebKitCSSKeyframesRule: A.CssRule, CSSMediaRule: A.CssRule, CSSNamespaceRule: A.CssRule, CSSPageRule: A.CssRule, CSSRule: A.CssRule, CSSStyleRule: A.CssRule, CSSSupportsRule: A.CssRule, CSSViewportRule: A.CssRule, CSSStyleDeclaration: A.CssStyleDeclaration, MSStyleCSSProperties: A.CssStyleDeclaration, CSS2Properties: A.CssStyleDeclaration, CSSImageValue: A.CssStyleValue, CSSKeywordValue: A.CssStyleValue, CSSNumericValue: A.CssStyleValue, CSSPositionValue: A.CssStyleValue, CSSResourceValue: A.CssStyleValue, CSSUnitValue: A.CssStyleValue, CSSURLImageValue: A.CssStyleValue, CSSStyleValue: A.CssStyleValue, CSSMatrixComponent: A.CssTransformComponent, CSSRotation: A.CssTransformComponent, CSSScale: A.CssTransformComponent, CSSSkew: A.CssTransformComponent, CSSTranslation: A.CssTransformComponent, CSSTransformComponent: A.CssTransformComponent, CSSTransformValue: A.CssTransformValue, CSSUnparsedValue: A.CssUnparsedValue, DataTransferItem: A.DataTransferItem, DataTransferItemList: A.DataTransferItemList, DedicatedWorkerGlobalScope: A.DedicatedWorkerGlobalScope, DOMException: A.DomException, ClientRectList: A.DomRectList, DOMRectList: A.DomRectList, DOMRectReadOnly: A.DomRectReadOnly, DOMStringList: A.DomStringList, DOMTokenList: A.DomTokenList, MathMLElement: A.Element, SVGAElement: A.Element, SVGAnimateElement: A.Element, SVGAnimateMotionElement: A.Element, SVGAnimateTransformElement: A.Element, SVGAnimationElement: A.Element, SVGCircleElement: A.Element, SVGClipPathElement: A.Element, SVGDefsElement: A.Element, SVGDescElement: A.Element, SVGDiscardElement: A.Element, SVGEllipseElement: A.Element, SVGFEBlendElement: A.Element, SVGFEColorMatrixElement: A.Element, SVGFEComponentTransferElement: A.Element, SVGFECompositeElement: A.Element, SVGFEConvolveMatrixElement: A.Element, SVGFEDiffuseLightingElement: A.Element, SVGFEDisplacementMapElement: A.Element, SVGFEDistantLightElement: A.Element, SVGFEFloodElement: A.Element, SVGFEFuncAElement: A.Element, SVGFEFuncBElement: A.Element, SVGFEFuncGElement: A.Element, SVGFEFuncRElement: A.Element, SVGFEGaussianBlurElement: A.Element, SVGFEImageElement: A.Element, SVGFEMergeElement: A.Element, SVGFEMergeNodeElement: A.Element, SVGFEMorphologyElement: A.Element, SVGFEOffsetElement: A.Element, SVGFEPointLightElement: A.Element, SVGFESpecularLightingElement: A.Element, SVGFESpotLightElement: A.Element, SVGFETileElement: A.Element, SVGFETurbulenceElement: A.Element, SVGFilterElement: A.Element, SVGForeignObjectElement: A.Element, SVGGElement: A.Element, SVGGeometryElement: A.Element, SVGGraphicsElement: A.Element, SVGImageElement: A.Element, SVGLineElement: A.Element, SVGLinearGradientElement: A.Element, SVGMarkerElement: A.Element, SVGMaskElement: A.Element, SVGMetadataElement: A.Element, SVGPathElement: A.Element, SVGPatternElement: A.Element, SVGPolygonElement: A.Element, SVGPolylineElement: A.Element, SVGRadialGradientElement: A.Element, SVGRectElement: A.Element, SVGScriptElement: A.Element, SVGSetElement: A.Element, SVGStopElement: A.Element, SVGStyleElement: A.Element, SVGElement: A.Element, SVGSVGElement: A.Element, SVGSwitchElement: A.Element, SVGSymbolElement: A.Element, SVGTSpanElement: A.Element, SVGTextContentElement: A.Element, SVGTextElement: A.Element, SVGTextPathElement: A.Element, SVGTextPositioningElement: A.Element, SVGTitleElement: A.Element, SVGUseElement: A.Element, SVGViewElement: A.Element, SVGGradientElement: A.Element, SVGComponentTransferFunctionElement: A.Element, SVGFEDropShadowElement: A.Element, SVGMPathElement: A.Element, Element: A.Element, AbortPaymentEvent: A.Event, AnimationEvent: A.Event, AnimationPlaybackEvent: A.Event, ApplicationCacheErrorEvent: A.Event, BackgroundFetchClickEvent: A.Event, BackgroundFetchEvent: A.Event, BackgroundFetchFailEvent: A.Event, BackgroundFetchedEvent: A.Event, BeforeInstallPromptEvent: A.Event, BeforeUnloadEvent: A.Event, BlobEvent: A.Event, CanMakePaymentEvent: A.Event, ClipboardEvent: A.Event, CloseEvent: A.Event, CompositionEvent: A.Event, CustomEvent: A.Event, DeviceMotionEvent: A.Event, DeviceOrientationEvent: A.Event, ErrorEvent: A.Event, ExtendableEvent: A.Event, ExtendableMessageEvent: A.Event, FetchEvent: A.Event, FocusEvent: A.Event, FontFaceSetLoadEvent: A.Event, ForeignFetchEvent: A.Event, GamepadEvent: A.Event, HashChangeEvent: A.Event, InstallEvent: A.Event, KeyboardEvent: A.Event, MediaEncryptedEvent: A.Event, MediaKeyMessageEvent: A.Event, MediaQueryListEvent: A.Event, MediaStreamEvent: A.Event, MediaStreamTrackEvent: A.Event, MIDIConnectionEvent: A.Event, MIDIMessageEvent: A.Event, MouseEvent: A.Event, DragEvent: A.Event, MutationEvent: A.Event, NotificationEvent: A.Event, PageTransitionEvent: A.Event, PaymentRequestEvent: A.Event, PaymentRequestUpdateEvent: A.Event, PointerEvent: A.Event, PopStateEvent: A.Event, PresentationConnectionAvailableEvent: A.Event, PresentationConnectionCloseEvent: A.Event, ProgressEvent: A.Event, PromiseRejectionEvent: A.Event, PushEvent: A.Event, RTCDataChannelEvent: A.Event, RTCDTMFToneChangeEvent: A.Event, RTCPeerConnectionIceEvent: A.Event, RTCTrackEvent: A.Event, SecurityPolicyViolationEvent: A.Event, SensorErrorEvent: A.Event, SpeechRecognitionError: A.Event, SpeechRecognitionEvent: A.Event, SpeechSynthesisEvent: A.Event, StorageEvent: A.Event, SyncEvent: A.Event, TextEvent: A.Event, TouchEvent: A.Event, TrackEvent: A.Event, TransitionEvent: A.Event, WebKitTransitionEvent: A.Event, UIEvent: A.Event, VRDeviceEvent: A.Event, VRDisplayEvent: A.Event, VRSessionEvent: A.Event, WheelEvent: A.Event, MojoInterfaceRequestEvent: A.Event, ResourceProgressEvent: A.Event, USBConnectionEvent: A.Event, IDBVersionChangeEvent: A.Event, AudioProcessingEvent: A.Event, OfflineAudioCompletionEvent: A.Event, WebGLContextEvent: A.Event, Event: A.Event, InputEvent: A.Event, SubmitEvent: A.Event, AbsoluteOrientationSensor: A.EventTarget, Accelerometer: A.EventTarget, AccessibleNode: A.EventTarget, AmbientLightSensor: A.EventTarget, Animation: A.EventTarget, ApplicationCache: A.EventTarget, DOMApplicationCache: A.EventTarget, OfflineResourceList: A.EventTarget, BackgroundFetchRegistration: A.EventTarget, BatteryManager: A.EventTarget, BroadcastChannel: A.EventTarget, EventSource: A.EventTarget, FileReader: A.EventTarget, FontFaceSet: A.EventTarget, Gyroscope: A.EventTarget, XMLHttpRequest: A.EventTarget, XMLHttpRequestEventTarget: A.EventTarget, XMLHttpRequestUpload: A.EventTarget, LinearAccelerationSensor: A.EventTarget, Magnetometer: A.EventTarget, MediaDevices: A.EventTarget, MediaKeySession: A.EventTarget, MediaQueryList: A.EventTarget, MediaRecorder: A.EventTarget, MediaSource: A.EventTarget, MediaStream: A.EventTarget, MIDIAccess: A.EventTarget, MIDIInput: A.EventTarget, MIDIOutput: A.EventTarget, MIDIPort: A.EventTarget, NetworkInformation: A.EventTarget, Notification: A.EventTarget, OffscreenCanvas: A.EventTarget, OrientationSensor: A.EventTarget, PaymentRequest: A.EventTarget, Performance: A.EventTarget, PermissionStatus: A.EventTarget, PresentationAvailability: A.EventTarget, PresentationConnection: A.EventTarget, PresentationConnectionList: A.EventTarget, PresentationRequest: A.EventTarget, RelativeOrientationSensor: A.EventTarget, RemotePlayback: A.EventTarget, RTCDataChannel: A.EventTarget, DataChannel: A.EventTarget, RTCDTMFSender: A.EventTarget, RTCPeerConnection: A.EventTarget, webkitRTCPeerConnection: A.EventTarget, mozRTCPeerConnection: A.EventTarget, ScreenOrientation: A.EventTarget, Sensor: A.EventTarget, ServiceWorker: A.EventTarget, ServiceWorkerContainer: A.EventTarget, ServiceWorkerRegistration: A.EventTarget, SharedWorker: A.EventTarget, SpeechRecognition: A.EventTarget, SpeechSynthesis: A.EventTarget, SpeechSynthesisUtterance: A.EventTarget, VR: A.EventTarget, VRDevice: A.EventTarget, VRDisplay: A.EventTarget, VRSession: A.EventTarget, VisualViewport: A.EventTarget, WebSocket: A.EventTarget, Window: A.EventTarget, DOMWindow: A.EventTarget, Worker: A.EventTarget, WorkerPerformance: A.EventTarget, BluetoothDevice: A.EventTarget, BluetoothRemoteGATTCharacteristic: A.EventTarget, Clipboard: A.EventTarget, MojoInterfaceInterceptor: A.EventTarget, USB: A.EventTarget, IDBDatabase: A.EventTarget, IDBOpenDBRequest: A.EventTarget, IDBVersionChangeRequest: A.EventTarget, IDBRequest: A.EventTarget, IDBTransaction: A.EventTarget, AnalyserNode: A.EventTarget, RealtimeAnalyserNode: A.EventTarget, AudioBufferSourceNode: A.EventTarget, AudioDestinationNode: A.EventTarget, AudioNode: A.EventTarget, AudioScheduledSourceNode: A.EventTarget, AudioWorkletNode: A.EventTarget, BiquadFilterNode: A.EventTarget, ChannelMergerNode: A.EventTarget, AudioChannelMerger: A.EventTarget, ChannelSplitterNode: A.EventTarget, AudioChannelSplitter: A.EventTarget, ConstantSourceNode: A.EventTarget, ConvolverNode: A.EventTarget, DelayNode: A.EventTarget, DynamicsCompressorNode: A.EventTarget, GainNode: A.EventTarget, AudioGainNode: A.EventTarget, IIRFilterNode: A.EventTarget, MediaElementAudioSourceNode: A.EventTarget, MediaStreamAudioDestinationNode: A.EventTarget, MediaStreamAudioSourceNode: A.EventTarget, OscillatorNode: A.EventTarget, Oscillator: A.EventTarget, PannerNode: A.EventTarget, AudioPannerNode: A.EventTarget, webkitAudioPannerNode: A.EventTarget, ScriptProcessorNode: A.EventTarget, JavaScriptAudioNode: A.EventTarget, StereoPannerNode: A.EventTarget, WaveShaperNode: A.EventTarget, EventTarget: A.EventTarget, File: A.File, FileList: A.FileList, FileWriter: A.FileWriter, HTMLFormElement: A.FormElement, Gamepad: A.Gamepad, History: A.History, HTMLCollection: A.HtmlCollection, HTMLFormControlsCollection: A.HtmlCollection, HTMLOptionsCollection: A.HtmlCollection, ImageData: A.ImageData, Location: A.Location, MediaDeviceInfo: A.MediaDeviceInfo, MediaList: A.MediaList, CanvasCaptureMediaStreamTrack: A.MediaStreamTrack, MediaStreamTrack: A.MediaStreamTrack, MessageEvent: A.MessageEvent, MessagePort: A.MessagePort, MIDIInputMap: A.MidiInputMap, MIDIOutputMap: A.MidiOutputMap, MimeType: A.MimeType, MimeTypeArray: A.MimeTypeArray, Document: A.Node, DocumentFragment: A.Node, HTMLDocument: A.Node, ShadowRoot: A.Node, XMLDocument: A.Node, Attr: A.Node, DocumentType: A.Node, Node: A.Node, NodeList: A.NodeList, RadioNodeList: A.NodeList, Plugin: A.Plugin, PluginArray: A.PluginArray, RTCStatsReport: A.RtcStatsReport, HTMLSelectElement: A.SelectElement, SharedArrayBuffer: A.SharedArrayBuffer, SourceBuffer: A.SourceBuffer, SourceBufferList: A.SourceBufferList, SpeechGrammar: A.SpeechGrammar, SpeechGrammarList: A.SpeechGrammarList, SpeechRecognitionResult: A.SpeechRecognitionResult, Storage: A.Storage, CSSStyleSheet: A.StyleSheet, StyleSheet: A.StyleSheet, TextTrack: A.TextTrack, TextTrackCue: A.TextTrackCue, VTTCue: A.TextTrackCue, TextTrackCueList: A.TextTrackCueList, TextTrackList: A.TextTrackList, TimeRanges: A.TimeRanges, Touch: A.Touch, TouchList: A.TouchList, TrackDefaultList: A.TrackDefaultList, HTMLTrackElement: A.TrackElement, URL: A.Url, VideoTrack: A.VideoTrack, VideoTrackList: A.VideoTrackList, ServiceWorkerGlobalScope: A.WorkerGlobalScope, SharedWorkerGlobalScope: A.WorkerGlobalScope, WorkerGlobalScope: A.WorkerGlobalScope, CSSRuleList: A._CssRuleList, ClientRect: A._DomRect, DOMRect: A._DomRect, GamepadList: A._GamepadList, NamedNodeMap: A._NamedNodeMap, MozNamedAttrMap: A._NamedNodeMap, SpeechRecognitionResultList: A._SpeechRecognitionResultList, StyleSheetList: A._StyleSheetList, SVGLength: A.Length, SVGLengthList: A.LengthList, SVGNumber: A.Number, SVGNumberList: A.NumberList, SVGPointList: A.PointList, SVGStringList: A.StringList, SVGTransform: A.Transform, SVGTransformList: A.TransformList, AudioBuffer: A.AudioBuffer, AudioParamMap: A.AudioParamMap, AudioTrack: A.AudioTrack, AudioTrackList: A.AudioTrackList, AudioContext: A.BaseAudioContext, webkitAudioContext: A.BaseAudioContext, BaseAudioContext: A.BaseAudioContext, OfflineAudioContext: A.OfflineAudioContext});
    hunkHelpers.setOrUpdateLeafTags({WebGL: true, AnimationEffectReadOnly: true, AnimationEffectTiming: true, AnimationEffectTimingReadOnly: true, AnimationTimeline: true, AnimationWorkletGlobalScope: true, AuthenticatorAssertionResponse: true, AuthenticatorAttestationResponse: true, AuthenticatorResponse: true, BackgroundFetchFetch: true, BackgroundFetchManager: true, BackgroundFetchSettledFetch: true, BarProp: true, BarcodeDetector: true, BluetoothRemoteGATTDescriptor: true, Body: true, BudgetState: true, CacheStorage: true, CanvasGradient: true, CanvasPattern: true, CanvasRenderingContext2D: true, Client: true, Clients: true, CookieStore: true, Coordinates: true, Credential: true, CredentialUserData: true, CredentialsContainer: true, Crypto: true, CSS: true, CSSVariableReferenceValue: true, CustomElementRegistry: true, DataTransfer: true, DeprecatedStorageInfo: true, DeprecatedStorageQuota: true, DeprecationReport: true, DetectedBarcode: true, DetectedFace: true, DetectedText: true, DeviceAcceleration: true, DeviceRotationRate: true, DirectoryEntry: true, webkitFileSystemDirectoryEntry: true, FileSystemDirectoryEntry: true, DirectoryReader: true, WebKitDirectoryReader: true, webkitFileSystemDirectoryReader: true, FileSystemDirectoryReader: true, DocumentOrShadowRoot: true, DocumentTimeline: true, DOMError: true, DOMImplementation: true, Iterator: true, DOMMatrix: true, DOMMatrixReadOnly: true, DOMParser: true, DOMPoint: true, DOMPointReadOnly: true, DOMQuad: true, DOMStringMap: true, Entry: true, webkitFileSystemEntry: true, FileSystemEntry: true, External: true, FaceDetector: true, FederatedCredential: true, FileEntry: true, webkitFileSystemFileEntry: true, FileSystemFileEntry: true, DOMFileSystem: true, WebKitFileSystem: true, webkitFileSystem: true, FileSystem: true, FontFace: true, FontFaceSource: true, FormData: true, GamepadButton: true, GamepadPose: true, Geolocation: true, Position: true, GeolocationPosition: true, Headers: true, HTMLHyperlinkElementUtils: true, IdleDeadline: true, ImageBitmap: true, ImageBitmapRenderingContext: true, ImageCapture: true, InputDeviceCapabilities: true, IntersectionObserver: true, IntersectionObserverEntry: true, InterventionReport: true, KeyframeEffect: true, KeyframeEffectReadOnly: true, MediaCapabilities: true, MediaCapabilitiesInfo: true, MediaError: true, MediaKeyStatusMap: true, MediaKeySystemAccess: true, MediaKeys: true, MediaKeysPolicy: true, MediaMetadata: true, MediaSession: true, MediaSettingsRange: true, MemoryInfo: true, MessageChannel: true, Metadata: true, MutationObserver: true, WebKitMutationObserver: true, MutationRecord: true, NavigationPreloadManager: true, Navigator: true, NavigatorAutomationInformation: true, NavigatorConcurrentHardware: true, NavigatorCookies: true, NavigatorUserMediaError: true, NodeFilter: true, NodeIterator: true, NonDocumentTypeChildNode: true, NonElementParentNode: true, NoncedElement: true, OffscreenCanvasRenderingContext2D: true, OverconstrainedError: true, PaintRenderingContext2D: true, PaintSize: true, PaintWorkletGlobalScope: true, PasswordCredential: true, Path2D: true, PaymentAddress: true, PaymentInstruments: true, PaymentManager: true, PaymentResponse: true, PerformanceEntry: true, PerformanceLongTaskTiming: true, PerformanceMark: true, PerformanceMeasure: true, PerformanceNavigation: true, PerformanceNavigationTiming: true, PerformanceObserver: true, PerformanceObserverEntryList: true, PerformancePaintTiming: true, PerformanceResourceTiming: true, PerformanceServerTiming: true, PerformanceTiming: true, Permissions: true, PhotoCapabilities: true, PositionError: true, GeolocationPositionError: true, Presentation: true, PresentationReceiver: true, PublicKeyCredential: true, PushManager: true, PushMessageData: true, PushSubscription: true, PushSubscriptionOptions: true, Range: true, RelatedApplication: true, ReportBody: true, ReportingObserver: true, ResizeObserver: true, ResizeObserverEntry: true, RTCCertificate: true, RTCIceCandidate: true, mozRTCIceCandidate: true, RTCLegacyStatsReport: true, RTCRtpContributingSource: true, RTCRtpReceiver: true, RTCRtpSender: true, RTCSessionDescription: true, mozRTCSessionDescription: true, RTCStatsResponse: true, Screen: true, ScrollState: true, ScrollTimeline: true, Selection: true, SpeechRecognitionAlternative: true, SpeechSynthesisVoice: true, StaticRange: true, StorageManager: true, StyleMedia: true, StylePropertyMap: true, StylePropertyMapReadonly: true, SyncManager: true, TaskAttributionTiming: true, TextDetector: true, TextMetrics: true, TrackDefault: true, TreeWalker: true, TrustedHTML: true, TrustedScriptURL: true, TrustedURL: true, UnderlyingSourceBase: true, URLSearchParams: true, VRCoordinateSystem: true, VRDisplayCapabilities: true, VREyeParameters: true, VRFrameData: true, VRFrameOfReference: true, VRPose: true, VRStageBounds: true, VRStageBoundsPoint: true, VRStageParameters: true, ValidityState: true, VideoPlaybackQuality: true, VTTRegion: true, WindowClient: true, WorkletAnimation: true, WorkletGlobalScope: true, XPathEvaluator: true, XPathExpression: true, XPathNSResolver: true, XPathResult: true, XMLSerializer: true, XSLTProcessor: true, Bluetooth: true, BluetoothCharacteristicProperties: true, BluetoothRemoteGATTServer: true, BluetoothRemoteGATTService: true, BluetoothUUID: true, BudgetService: true, Cache: true, DOMFileSystemSync: true, DirectoryEntrySync: true, DirectoryReaderSync: true, EntrySync: true, FileEntrySync: true, FileReaderSync: true, FileWriterSync: true, HTMLAllCollection: true, Mojo: true, MojoHandle: true, MojoWatcher: true, NFC: true, PagePopupController: true, Report: true, Request: true, Response: true, SubtleCrypto: true, USBAlternateInterface: true, USBConfiguration: true, USBDevice: true, USBEndpoint: true, USBInTransferResult: true, USBInterface: true, USBIsochronousInTransferPacket: true, USBIsochronousInTransferResult: true, USBIsochronousOutTransferPacket: true, USBIsochronousOutTransferResult: true, USBOutTransferResult: true, WorkerLocation: true, WorkerNavigator: true, Worklet: true, IDBCursor: true, IDBCursorWithValue: true, IDBFactory: true, IDBIndex: true, IDBKeyRange: true, IDBObjectStore: true, IDBObservation: true, IDBObserver: true, IDBObserverChanges: true, SVGAngle: true, SVGAnimatedAngle: true, SVGAnimatedBoolean: true, SVGAnimatedEnumeration: true, SVGAnimatedInteger: true, SVGAnimatedLength: true, SVGAnimatedLengthList: true, SVGAnimatedNumber: true, SVGAnimatedNumberList: true, SVGAnimatedPreserveAspectRatio: true, SVGAnimatedRect: true, SVGAnimatedString: true, SVGAnimatedTransformList: true, SVGMatrix: true, SVGPoint: true, SVGPreserveAspectRatio: true, SVGRect: true, SVGUnitTypes: true, AudioListener: true, AudioParam: true, AudioWorkletGlobalScope: true, AudioWorkletProcessor: true, PeriodicWave: true, WebGLActiveInfo: true, ANGLEInstancedArrays: true, ANGLE_instanced_arrays: true, WebGLBuffer: true, WebGLCanvas: true, WebGLColorBufferFloat: true, WebGLCompressedTextureASTC: true, WebGLCompressedTextureATC: true, WEBGL_compressed_texture_atc: true, WebGLCompressedTextureETC1: true, WEBGL_compressed_texture_etc1: true, WebGLCompressedTextureETC: true, WebGLCompressedTexturePVRTC: true, WEBGL_compressed_texture_pvrtc: true, WebGLCompressedTextureS3TC: true, WEBGL_compressed_texture_s3tc: true, WebGLCompressedTextureS3TCsRGB: true, WebGLDebugRendererInfo: true, WEBGL_debug_renderer_info: true, WebGLDebugShaders: true, WEBGL_debug_shaders: true, WebGLDepthTexture: true, WEBGL_depth_texture: true, WebGLDrawBuffers: true, WEBGL_draw_buffers: true, EXTsRGB: true, EXT_sRGB: true, EXTBlendMinMax: true, EXT_blend_minmax: true, EXTColorBufferFloat: true, EXTColorBufferHalfFloat: true, EXTDisjointTimerQuery: true, EXTDisjointTimerQueryWebGL2: true, EXTFragDepth: true, EXT_frag_depth: true, EXTShaderTextureLOD: true, EXT_shader_texture_lod: true, EXTTextureFilterAnisotropic: true, EXT_texture_filter_anisotropic: true, WebGLFramebuffer: true, WebGLGetBufferSubDataAsync: true, WebGLLoseContext: true, WebGLExtensionLoseContext: true, WEBGL_lose_context: true, OESElementIndexUint: true, OES_element_index_uint: true, OESStandardDerivatives: true, OES_standard_derivatives: true, OESTextureFloat: true, OES_texture_float: true, OESTextureFloatLinear: true, OES_texture_float_linear: true, OESTextureHalfFloat: true, OES_texture_half_float: true, OESTextureHalfFloatLinear: true, OES_texture_half_float_linear: true, OESVertexArrayObject: true, OES_vertex_array_object: true, WebGLProgram: true, WebGLQuery: true, WebGLRenderbuffer: true, WebGLRenderingContext: true, WebGL2RenderingContext: true, WebGLSampler: true, WebGLShader: true, WebGLShaderPrecisionFormat: true, WebGLSync: true, WebGLTexture: true, WebGLTimerQueryEXT: true, WebGLTransformFeedback: true, WebGLUniformLocation: true, WebGLVertexArrayObject: true, WebGLVertexArrayObjectOES: true, WebGL2RenderingContextBase: true, ArrayBuffer: true, ArrayBufferView: false, DataView: true, Float32Array: true, Float64Array: true, Int16Array: true, Int32Array: true, Int8Array: true, Uint16Array: true, Uint32Array: true, Uint8ClampedArray: true, CanvasPixelArray: true, Uint8Array: false, HTMLAudioElement: true, HTMLBRElement: true, HTMLBaseElement: true, HTMLBodyElement: true, HTMLButtonElement: true, HTMLCanvasElement: true, HTMLContentElement: true, HTMLDListElement: true, HTMLDataElement: true, HTMLDataListElement: true, HTMLDetailsElement: true, HTMLDialogElement: true, HTMLDivElement: true, HTMLEmbedElement: true, HTMLFieldSetElement: true, HTMLHRElement: true, HTMLHeadElement: true, HTMLHeadingElement: true, HTMLHtmlElement: true, HTMLIFrameElement: true, HTMLImageElement: true, HTMLInputElement: true, HTMLLIElement: true, HTMLLabelElement: true, HTMLLegendElement: true, HTMLLinkElement: true, HTMLMapElement: true, HTMLMediaElement: true, HTMLMenuElement: true, HTMLMetaElement: true, HTMLMeterElement: true, HTMLModElement: true, HTMLOListElement: true, HTMLObjectElement: true, HTMLOptGroupElement: true, HTMLOptionElement: true, HTMLOutputElement: true, HTMLParagraphElement: true, HTMLParamElement: true, HTMLPictureElement: true, HTMLPreElement: true, HTMLProgressElement: true, HTMLQuoteElement: true, HTMLScriptElement: true, HTMLShadowElement: true, HTMLSlotElement: true, HTMLSourceElement: true, HTMLSpanElement: true, HTMLStyleElement: true, HTMLTableCaptionElement: true, HTMLTableCellElement: true, HTMLTableDataCellElement: true, HTMLTableHeaderCellElement: true, HTMLTableColElement: true, HTMLTableElement: true, HTMLTableRowElement: true, HTMLTableSectionElement: true, HTMLTemplateElement: true, HTMLTextAreaElement: true, HTMLTimeElement: true, HTMLTitleElement: true, HTMLUListElement: true, HTMLUnknownElement: true, HTMLVideoElement: true, HTMLDirectoryElement: true, HTMLFontElement: true, HTMLFrameElement: true, HTMLFrameSetElement: true, HTMLMarqueeElement: true, HTMLElement: false, AccessibleNodeList: true, HTMLAnchorElement: true, HTMLAreaElement: true, Blob: false, CDATASection: true, CharacterData: true, Comment: true, ProcessingInstruction: true, Text: true, CryptoKey: true, CSSPerspective: true, CSSCharsetRule: true, CSSConditionRule: true, CSSFontFaceRule: true, CSSGroupingRule: true, CSSImportRule: true, CSSKeyframeRule: true, MozCSSKeyframeRule: true, WebKitCSSKeyframeRule: true, CSSKeyframesRule: true, MozCSSKeyframesRule: true, WebKitCSSKeyframesRule: true, CSSMediaRule: true, CSSNamespaceRule: true, CSSPageRule: true, CSSRule: true, CSSStyleRule: true, CSSSupportsRule: true, CSSViewportRule: true, CSSStyleDeclaration: true, MSStyleCSSProperties: true, CSS2Properties: true, CSSImageValue: true, CSSKeywordValue: true, CSSNumericValue: true, CSSPositionValue: true, CSSResourceValue: true, CSSUnitValue: true, CSSURLImageValue: true, CSSStyleValue: false, CSSMatrixComponent: true, CSSRotation: true, CSSScale: true, CSSSkew: true, CSSTranslation: true, CSSTransformComponent: false, CSSTransformValue: true, CSSUnparsedValue: true, DataTransferItem: true, DataTransferItemList: true, DedicatedWorkerGlobalScope: true, DOMException: true, ClientRectList: true, DOMRectList: true, DOMRectReadOnly: false, DOMStringList: true, DOMTokenList: true, MathMLElement: true, SVGAElement: true, SVGAnimateElement: true, SVGAnimateMotionElement: true, SVGAnimateTransformElement: true, SVGAnimationElement: true, SVGCircleElement: true, SVGClipPathElement: true, SVGDefsElement: true, SVGDescElement: true, SVGDiscardElement: true, SVGEllipseElement: true, SVGFEBlendElement: true, SVGFEColorMatrixElement: true, SVGFEComponentTransferElement: true, SVGFECompositeElement: true, SVGFEConvolveMatrixElement: true, SVGFEDiffuseLightingElement: true, SVGFEDisplacementMapElement: true, SVGFEDistantLightElement: true, SVGFEFloodElement: true, SVGFEFuncAElement: true, SVGFEFuncBElement: true, SVGFEFuncGElement: true, SVGFEFuncRElement: true, SVGFEGaussianBlurElement: true, SVGFEImageElement: true, SVGFEMergeElement: true, SVGFEMergeNodeElement: true, SVGFEMorphologyElement: true, SVGFEOffsetElement: true, SVGFEPointLightElement: true, SVGFESpecularLightingElement: true, SVGFESpotLightElement: true, SVGFETileElement: true, SVGFETurbulenceElement: true, SVGFilterElement: true, SVGForeignObjectElement: true, SVGGElement: true, SVGGeometryElement: true, SVGGraphicsElement: true, SVGImageElement: true, SVGLineElement: true, SVGLinearGradientElement: true, SVGMarkerElement: true, SVGMaskElement: true, SVGMetadataElement: true, SVGPathElement: true, SVGPatternElement: true, SVGPolygonElement: true, SVGPolylineElement: true, SVGRadialGradientElement: true, SVGRectElement: true, SVGScriptElement: true, SVGSetElement: true, SVGStopElement: true, SVGStyleElement: true, SVGElement: true, SVGSVGElement: true, SVGSwitchElement: true, SVGSymbolElement: true, SVGTSpanElement: true, SVGTextContentElement: true, SVGTextElement: true, SVGTextPathElement: true, SVGTextPositioningElement: true, SVGTitleElement: true, SVGUseElement: true, SVGViewElement: true, SVGGradientElement: true, SVGComponentTransferFunctionElement: true, SVGFEDropShadowElement: true, SVGMPathElement: true, Element: false, AbortPaymentEvent: true, AnimationEvent: true, AnimationPlaybackEvent: true, ApplicationCacheErrorEvent: true, BackgroundFetchClickEvent: true, BackgroundFetchEvent: true, BackgroundFetchFailEvent: true, BackgroundFetchedEvent: true, BeforeInstallPromptEvent: true, BeforeUnloadEvent: true, BlobEvent: true, CanMakePaymentEvent: true, ClipboardEvent: true, CloseEvent: true, CompositionEvent: true, CustomEvent: true, DeviceMotionEvent: true, DeviceOrientationEvent: true, ErrorEvent: true, ExtendableEvent: true, ExtendableMessageEvent: true, FetchEvent: true, FocusEvent: true, FontFaceSetLoadEvent: true, ForeignFetchEvent: true, GamepadEvent: true, HashChangeEvent: true, InstallEvent: true, KeyboardEvent: true, MediaEncryptedEvent: true, MediaKeyMessageEvent: true, MediaQueryListEvent: true, MediaStreamEvent: true, MediaStreamTrackEvent: true, MIDIConnectionEvent: true, MIDIMessageEvent: true, MouseEvent: true, DragEvent: true, MutationEvent: true, NotificationEvent: true, PageTransitionEvent: true, PaymentRequestEvent: true, PaymentRequestUpdateEvent: true, PointerEvent: true, PopStateEvent: true, PresentationConnectionAvailableEvent: true, PresentationConnectionCloseEvent: true, ProgressEvent: true, PromiseRejectionEvent: true, PushEvent: true, RTCDataChannelEvent: true, RTCDTMFToneChangeEvent: true, RTCPeerConnectionIceEvent: true, RTCTrackEvent: true, SecurityPolicyViolationEvent: true, SensorErrorEvent: true, SpeechRecognitionError: true, SpeechRecognitionEvent: true, SpeechSynthesisEvent: true, StorageEvent: true, SyncEvent: true, TextEvent: true, TouchEvent: true, TrackEvent: true, TransitionEvent: true, WebKitTransitionEvent: true, UIEvent: true, VRDeviceEvent: true, VRDisplayEvent: true, VRSessionEvent: true, WheelEvent: true, MojoInterfaceRequestEvent: true, ResourceProgressEvent: true, USBConnectionEvent: true, IDBVersionChangeEvent: true, AudioProcessingEvent: true, OfflineAudioCompletionEvent: true, WebGLContextEvent: true, Event: false, InputEvent: false, SubmitEvent: false, AbsoluteOrientationSensor: true, Accelerometer: true, AccessibleNode: true, AmbientLightSensor: true, Animation: true, ApplicationCache: true, DOMApplicationCache: true, OfflineResourceList: true, BackgroundFetchRegistration: true, BatteryManager: true, BroadcastChannel: true, EventSource: true, FileReader: true, FontFaceSet: true, Gyroscope: true, XMLHttpRequest: true, XMLHttpRequestEventTarget: true, XMLHttpRequestUpload: true, LinearAccelerationSensor: true, Magnetometer: true, MediaDevices: true, MediaKeySession: true, MediaQueryList: true, MediaRecorder: true, MediaSource: true, MediaStream: true, MIDIAccess: true, MIDIInput: true, MIDIOutput: true, MIDIPort: true, NetworkInformation: true, Notification: true, OffscreenCanvas: true, OrientationSensor: true, PaymentRequest: true, Performance: true, PermissionStatus: true, PresentationAvailability: true, PresentationConnection: true, PresentationConnectionList: true, PresentationRequest: true, RelativeOrientationSensor: true, RemotePlayback: true, RTCDataChannel: true, DataChannel: true, RTCDTMFSender: true, RTCPeerConnection: true, webkitRTCPeerConnection: true, mozRTCPeerConnection: true, ScreenOrientation: true, Sensor: true, ServiceWorker: true, ServiceWorkerContainer: true, ServiceWorkerRegistration: true, SharedWorker: true, SpeechRecognition: true, SpeechSynthesis: true, SpeechSynthesisUtterance: true, VR: true, VRDevice: true, VRDisplay: true, VRSession: true, VisualViewport: true, WebSocket: true, Window: true, DOMWindow: true, Worker: true, WorkerPerformance: true, BluetoothDevice: true, BluetoothRemoteGATTCharacteristic: true, Clipboard: true, MojoInterfaceInterceptor: true, USB: true, IDBDatabase: true, IDBOpenDBRequest: true, IDBVersionChangeRequest: true, IDBRequest: true, IDBTransaction: true, AnalyserNode: true, RealtimeAnalyserNode: true, AudioBufferSourceNode: true, AudioDestinationNode: true, AudioNode: true, AudioScheduledSourceNode: true, AudioWorkletNode: true, BiquadFilterNode: true, ChannelMergerNode: true, AudioChannelMerger: true, ChannelSplitterNode: true, AudioChannelSplitter: true, ConstantSourceNode: true, ConvolverNode: true, DelayNode: true, DynamicsCompressorNode: true, GainNode: true, AudioGainNode: true, IIRFilterNode: true, MediaElementAudioSourceNode: true, MediaStreamAudioDestinationNode: true, MediaStreamAudioSourceNode: true, OscillatorNode: true, Oscillator: true, PannerNode: true, AudioPannerNode: true, webkitAudioPannerNode: true, ScriptProcessorNode: true, JavaScriptAudioNode: true, StereoPannerNode: true, WaveShaperNode: true, EventTarget: false, File: true, FileList: true, FileWriter: true, HTMLFormElement: true, Gamepad: true, History: true, HTMLCollection: true, HTMLFormControlsCollection: true, HTMLOptionsCollection: true, ImageData: true, Location: true, MediaDeviceInfo: true, MediaList: true, CanvasCaptureMediaStreamTrack: true, MediaStreamTrack: true, MessageEvent: true, MessagePort: true, MIDIInputMap: true, MIDIOutputMap: true, MimeType: true, MimeTypeArray: true, Document: true, DocumentFragment: true, HTMLDocument: true, ShadowRoot: true, XMLDocument: true, Attr: true, DocumentType: true, Node: false, NodeList: true, RadioNodeList: true, Plugin: true, PluginArray: true, RTCStatsReport: true, HTMLSelectElement: true, SharedArrayBuffer: true, SourceBuffer: true, SourceBufferList: true, SpeechGrammar: true, SpeechGrammarList: true, SpeechRecognitionResult: true, Storage: true, CSSStyleSheet: true, StyleSheet: true, TextTrack: true, TextTrackCue: true, VTTCue: true, TextTrackCueList: true, TextTrackList: true, TimeRanges: true, Touch: true, TouchList: true, TrackDefaultList: true, HTMLTrackElement: true, URL: true, VideoTrack: true, VideoTrackList: true, ServiceWorkerGlobalScope: true, SharedWorkerGlobalScope: true, WorkerGlobalScope: false, CSSRuleList: true, ClientRect: true, DOMRect: true, GamepadList: true, NamedNodeMap: true, MozNamedAttrMap: true, SpeechRecognitionResultList: true, StyleSheetList: true, SVGLength: true, SVGLengthList: true, SVGNumber: true, SVGNumberList: true, SVGPointList: true, SVGStringList: true, SVGTransform: true, SVGTransformList: true, AudioBuffer: true, AudioParamMap: true, AudioTrack: true, AudioTrackList: true, AudioContext: true, webkitAudioContext: true, BaseAudioContext: false, OfflineAudioContext: true});
    A.NativeTypedArray.$nativeSuperclassTag = "ArrayBufferView";
    A._NativeTypedArrayOfDouble_NativeTypedArray_ListMixin.$nativeSuperclassTag = "ArrayBufferView";
    A._NativeTypedArrayOfDouble_NativeTypedArray_ListMixin_FixedLengthListMixin.$nativeSuperclassTag = "ArrayBufferView";
    A.NativeTypedArrayOfDouble.$nativeSuperclassTag = "ArrayBufferView";
    A._NativeTypedArrayOfInt_NativeTypedArray_ListMixin.$nativeSuperclassTag = "ArrayBufferView";
    A._NativeTypedArrayOfInt_NativeTypedArray_ListMixin_FixedLengthListMixin.$nativeSuperclassTag = "ArrayBufferView";
    A.NativeTypedArrayOfInt.$nativeSuperclassTag = "ArrayBufferView";
    A._SourceBufferList_EventTarget_ListMixin.$nativeSuperclassTag = "EventTarget";
    A._SourceBufferList_EventTarget_ListMixin_ImmutableListMixin.$nativeSuperclassTag = "EventTarget";
    A._TextTrackList_EventTarget_ListMixin.$nativeSuperclassTag = "EventTarget";
    A._TextTrackList_EventTarget_ListMixin_ImmutableListMixin.$nativeSuperclassTag = "EventTarget";
  })();
  Function.prototype.call$1 = function(a) {
    return this(a);
  };
  Function.prototype.call$2 = function(a, b) {
    return this(a, b);
  };
  Function.prototype.call$0 = function() {
    return this();
  };
  Function.prototype.call$3 = function(a, b, c) {
    return this(a, b, c);
  };
  Function.prototype.call$4 = function(a, b, c, d) {
    return this(a, b, c, d);
  };
  Function.prototype.call$1$1 = function(a) {
    return this(a);
  };
  convertAllToFastObject(holders);
  convertToFastObject($);
  (function(callback) {
    if (typeof document === "undefined") {
      callback(null);
      return;
    }
    if (typeof document.currentScript != "undefined") {
      callback(document.currentScript);
      return;
    }
    var scripts = document.scripts;
    function onLoad(event) {
      for (var i = 0; i < scripts.length; ++i)
        scripts[i].removeEventListener("load", onLoad, false);
      callback(event.target);
    }
    for (var i = 0; i < scripts.length; ++i)
      scripts[i].addEventListener("load", onLoad, false);
  })(function(currentScript) {
    init.currentScript = currentScript;
    var callMain = A.main;
    if (typeof dartMainRunner === "function")
      dartMainRunner(callMain, []);
    else
      callMain([]);
  });
})();

//# sourceMappingURL=e2ee.worker.dart.js.map

# Flutter WebRTC 日志監控器啟動腳本
# 設定控制台編碼為UTF-8
$OutputEncoding = [console]::InputEncoding = [console]::OutputEncoding = New-Object System.Text.UTF8Encoding

Write-Host "🚀 啟動 Flutter WebRTC 日志監控器..." -ForegroundColor Green
Write-Host ""

# 檢查Python是否安裝
try {
    $pythonVersion = python --version 2>&1
    Write-Host "✅ 找到 Python: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ 錯誤: 找不到 Python。請先安裝 Python 3.6+ 並添加到 PATH。" -ForegroundColor Red
    Write-Host "下載地址: https://www.python.org/downloads/" -ForegroundColor Yellow
    Read-Host "按任意鍵退出"
    exit 1
}

# 檢查並安裝依賴
Write-Host "📦 檢查依賴..." -ForegroundColor Yellow
try {
    python -c "import win10toast" 2>$null
    Write-Host "✅ 系統通知依賴已安裝" -ForegroundColor Green
} catch {
    Write-Host "📦 安裝系統通知依賴..." -ForegroundColor Yellow
    pip install win10toast
}

# 檢查Flutter是否可用
try {
    $flutterVersion = flutter --version 2>&1 | Select-String "Flutter"
    Write-Host "✅ 找到 Flutter: $flutterVersion" -ForegroundColor Green
} catch {
    Write-Host "⚠️ 警告: 找不到 Flutter 命令。將僅監控日志文件。" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🔍 開始監控 Flutter 應用日志..." -ForegroundColor Cyan
Write-Host "💡 按 Ctrl+C 停止監控" -ForegroundColor Gray
Write-Host ""

# 啟動Python監控腳本
try {
    python log_monitor.py
} catch {
    Write-Host "❌ 啟動監控腳本時發生錯誤: $_" -ForegroundColor Red
}

Read-Host "按任意鍵退出" 
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_webrtc_plus/pixelfree_platform_interface.dart';
import 'package:flutter_webrtc_plus/src/helper.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';

class PixeBeautyDialog extends StatefulWidget {
  const PixeBeautyDialog({
    Key? key,
  }) : super(key: key);

  @override
  State<PixeBeautyDialog> createState() => _PixeBeautyDialogState();
}

class _PixeBeautyDialogState extends State<PixeBeautyDialog> {
  int _currentPage = 0;
  late PageController _pageController;
  BeautyItem? _selectedItem;
  double _sliderValue = 0.0;
  late List<BeautyPage> _pages;
  final Map<int, BeautyItem> _pageSelectedItems = {};

  @override
  void initState() {
    super.initState();
    _pageController = PageController(initialPage: _currentPage);
    _initPages();
    _loadSavedValues();
    // 默认选中第一个项目
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_pages.isNotEmpty && _pages[0].items.isNotEmpty) {
        _selectItem(_pages[0].items[0]);
      }
    });
  }

  void _initPages() {
    _pages = [
      BeautyPage(
        title: '美膚',
        items: [
          BeautyItem.beauty(PFBeautyFiterType.faceWhitenStrength, 0.2, '美白', 'assets/icons/meibai.png'),
          BeautyItem.beauty(PFBeautyFiterType.faceRuddyStrength, 0.6, '紅潤', 'assets/icons/hongrun.png'),
          BeautyItem.beauty(PFBeautyFiterType.faceBlurStrength, 0.7, '磨皮', 'assets/icons/mopi.png'),
          BeautyItem.beauty(PFBeautyFiterType.faceEyeBrighten, 0.0, '亮眼', 'assets/icons/liangyan.png'),
          BeautyItem.beauty(PFBeautyFiterType.faceSharpenStrength, 0.0, '銳化', 'assets/icons/ruihua.png'),
          BeautyItem.beauty(PFBeautyFiterType.faceQualityStrength, 0.2, '增強畫質', 'assets/icons/huazhizengqiang.png'),
        ],
      ),
      BeautyPage(
        title: '美形',
        items: [
          BeautyItem.beauty(PFBeautyFiterType.eyeStrength, 0.2, '大眼', 'assets/icons/dayan.png'),
          BeautyItem.beauty(PFBeautyFiterType.faceThinning, 0.2, '瘦臉', 'assets/icons/shoulian.png'),
          BeautyItem.beauty(PFBeautyFiterType.faceNarrow, 0.2, '瘦顴骨', 'assets/icons/zhailian.png'),
          BeautyItem.beauty(PFBeautyFiterType.faceChin, 0.5, '下巴', 'assets/icons/xiaba.png'),
          BeautyItem.beauty(PFBeautyFiterType.faceV, 0.2, '瘦下頷', 'assets/icons/vlian.png'),
          BeautyItem.beauty(PFBeautyFiterType.faceSmall, 0.2, '小臉', 'assets/icons/xianlian.png'),
          BeautyItem.beauty(PFBeautyFiterType.faceNose, 0.2, '鼻子', 'assets/icons/bizhi.png'),
          BeautyItem.beauty(PFBeautyFiterType.faceForehead, 0.5, '額頭', 'assets/icons/etou.png'),
          BeautyItem.beauty(PFBeautyFiterType.faceMouth, 0.5, '嘴巴', 'assets/icons/zuiba.png'),
          BeautyItem.beauty(PFBeautyFiterType.facePhiltrum, 0.5, '人中', 'assets/icons/renzhong.png'),
          BeautyItem.beauty(PFBeautyFiterType.faceLongNose, 0.5, '長鼻', 'assets/icons/changbi.png'),
          BeautyItem.beauty(PFBeautyFiterType.faceEyeSpace, 0.5, '眼距', 'assets/icons/yanju.png'),
          BeautyItem.beauty(PFBeautyFiterType.faceSmile, 0.0, '微笑嘴角', 'assets/icons/weixiaozuijiao.png'),
          BeautyItem.beauty(PFBeautyFiterType.faceEyeRotate, 0.5, '旋轉眼睛', 'assets/icons/yanjingjiaodu.png'),
          BeautyItem.beauty(PFBeautyFiterType.faceCanthus, 0.0, '開眼角', 'assets/icons/kaiyanjiao.png'),
        ],
      ),
      BeautyPage(
        title: '濾鏡',
        items: [
          BeautyItem.filter('origin', 0.5, '原圖', 'assets/icons/yuantu.png'),
          BeautyItem.filter('chulian', 0.8, '初戀', 'assets/icons/chulian.png'),
          BeautyItem.filter('chuxin', 0.8, '初心', 'assets/icons/chuxin.png'),
          BeautyItem.filter('fennen', 0.8, '粉嫩', 'assets/icons/f_fennen1.png'),
          BeautyItem.filter('lengku', 0.8, '冷酷', 'assets/icons/lengku.png'),
          BeautyItem.filter('meiwei', 0.8, '美味', 'assets/icons/meiwei.png'),
          BeautyItem.filter('naicha', 0.8, '奶茶', 'assets/icons/naicha.png'),
          BeautyItem.filter('pailide', 0.8, '派麗德', 'assets/icons/pailide.png'),
          BeautyItem.filter('qingxin', 0.8, '清新', 'assets/icons/qingxin.png'),
          BeautyItem.filter('rixi', 0.8, '日系', 'assets/icons/rixi.png'),
          BeautyItem.filter('riza', 0.8, '日雜', 'assets/icons/riza.png'),
          BeautyItem.filter('weimei', 0.8, '唯美', 'assets/icons/weimei.png'),
        ],
      ),
      BeautyPage(
        title: '貼紙',
        items: [
          BeautyItem.sticker('origin', '原圖', 'assets/icons/yuantu.png'),
          BeautyItem.sticker('cat_fa_qia.bundle', 'cat_fa_qia', 'assets/icons/cat_fa_qia.png'),
          BeautyItem.sticker('cat_on_the_head.bundle', 'c_head', 'assets/icons/cat_on_the_head.png'),
          BeautyItem.sticker('cidinmaozi.bundle', 'cidinmaozi', 'assets/icons/cidinmaozi.png'),
          BeautyItem.sticker('cream_cake.bundle', 'c_cake', 'assets/icons/cream_cake.png'),
          BeautyItem.sticker('cut_hair.bundle', 'cut_hair', 'assets/icons/cut_hair.png'),
          BeautyItem.sticker('cute_cat.bundle', 'cute_cat', 'assets/icons/cute_cat.png'),
          BeautyItem.sticker('dahudiejie.bundle', 'dahudiejie', 'assets/icons/dahudiejie.png'),
          BeautyItem.sticker('damengyanjing.bundle', 'd_yjing', 'assets/icons/damengyanjing.png'),
          BeautyItem.sticker('dayuanerduo.bundle', 'd_duo', 'assets/icons/dayuanerduo.png'),
        ],
      ),
    ];
  }

  Future<void> _loadSavedValues() async {
    final prefs = await SharedPreferences.getInstance();
    
    // 加载美肤设置
    for (var item in _pages[0].items) {
      if (item.type == BeautyType.beauty) {
        final savedValue = prefs.getDouble(item.beautyType.toString());
        if (savedValue != null) {
          item.value = savedValue;
        }
      }
    }
    
    // 加载美形设置
    for (var item in _pages[1].items) {
      if (item.type == BeautyType.beauty) {
        final savedValue = prefs.getDouble(item.beautyType.toString());
        if (savedValue != null) {
          item.value = savedValue;
        }
      }
    }
    
    // 加载滤镜设置
    for (var item in _pages[2].items) {
      if (item.type == BeautyType.filter) {
        final savedValue = prefs.getDouble('filter_${item.filterName}');
        if (savedValue != null) {
          item.value = savedValue;
        }
      }
    }
    
    if (mounted) {
      setState(() {});
    }
  }

  @override
  void dispose() {
    _saveSettings();
    _pageController.dispose();
    super.dispose();
  }

  Future<void> _saveSettings() async {
    final prefs = await SharedPreferences.getInstance();
    
    // 保存美肤设置
    for (var item in _pages[0].items) {
      if (item.type == BeautyType.beauty) {
        await prefs.setDouble(item.beautyType.toString(), item.value);
      }
    }
    
    // 保存美形设置
    for (var item in _pages[1].items) {
      if (item.type == BeautyType.beauty) {
        await prefs.setDouble(item.beautyType.toString(), item.value);
      }
    }
    
    // 保存滤镜设置
    for (var item in _pages[2].items) {
      if (item.type == BeautyType.filter) {
        await prefs.setDouble('filter_${item.filterName}', item.value);
      }
    }
  }

  Future<String> _getStickerBundlePath(String bundleName) async {
    if (bundleName == 'origin') return 'origin';
    
    final appDir = await getApplicationDocumentsDirectory();
    final bundlePath = '${appDir.path}/$bundleName';
    
    // 检查文件是否已存在
    if (!File(bundlePath).existsSync()) {
      // 从assets复制到应用目录
      final data = await DefaultAssetBundle.of(context).load('assets/bundle/$bundleName');
      final bytes = data.buffer.asUint8List();
      await File(bundlePath).writeAsBytes(bytes);
    }
    print("sunmu----:$bundlePath");
    return bundlePath;
  }

  Future<void> _selectItem(BeautyItem item) async {
    setState(() {
      _selectedItem = item;
      _sliderValue = item.value is double ? item.value : 0.0;
      _pageSelectedItems[_currentPage] = item;
    });

    if (item.type == BeautyType.beauty) {
      Helper.pixelFreeSetBeautyFilterParam(item.beautyType!, _sliderValue);
    } else if (item.type == BeautyType.filter) {
      Helper.pixelFreeSetFilterParam(item.filterName!, _sliderValue);
      final prefs = await SharedPreferences.getInstance();
      if (item.filterName != null) {
        await prefs.setString('current_filter', item.filterName!);
      }
    } else if (item.type == BeautyType.sticker) {
      // 先启用美颜效果
      final bundlePath = await _getStickerBundlePath(item.filterName!);
      Helper.pixelFreeSetStickerBundle(bundlePath);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 300,
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.8),
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          Container(
            height: 76,
            child: _selectedItem != null && 
                   _selectedItem!.type != BeautyType.oneKey && 
                   _selectedItem!.type != BeautyType.sticker
                ? Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              _selectedItem!.title,
                              style: const TextStyle(color: Colors.white),
                            ),
                            Text(
                              '${(_sliderValue * 100).toInt()}%',
                              style: const TextStyle(color: Colors.white),
                            ),
                          ],
                        ),
                        Slider(
                          value: _sliderValue,
                          min: 0.0,
                          max: 1.0,
                          divisions: 100,
                          activeColor: Colors.blue,
                          inactiveColor: Colors.grey,
                          onChanged: (value) {
                            setState(() {
                              _sliderValue = value;
                            });
                            _selectedItem!.value = value;
                            if (_selectedItem!.type == BeautyType.beauty) {
                              Helper.pixelFreeSetBeautyFilterParam(
                                _selectedItem!.beautyType!,
                                value,
                              );
                            } else if (_selectedItem!.type == BeautyType.filter) {
                              Helper.pixelFreeSetFilterParam(
                                _selectedItem!.filterName!,
                                value,
                              );
                            }
                          },
                        ),
                      ],
                    ),
                  )
                : null,
          ),
          _buildTabBar(),
          Expanded(
            child: PageView(
              controller: _pageController,
              onPageChanged: (index) {
                setState(() {
                  _currentPage = index;
                  _selectedItem = _pageSelectedItems[index];
                  if (_selectedItem != null) {
                    _sliderValue = _selectedItem!.value is double ? _selectedItem!.value : 0.0;
                  } else {
                    _selectItem(_pages[index].items[0]);
                  }
                });
              },
              children: _pages.map((page) => _buildPage(page)).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      height: 50,
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: Colors.white.withOpacity(0.1),
            width: 1,
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: _pages.asMap().entries.map((entry) {
          final index = entry.key;
          final page = entry.value;
          return GestureDetector(
            onTap: () {
              _pageController.animateToPage(
                index,
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeInOut,
              );
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color: _currentPage == index ? Colors.blue : Colors.transparent,
                    width: 2,
                  ),
                ),
              ),
              child: Text(
                page.title,
                style: TextStyle(
                  color: _currentPage == index ? Colors.blue : Colors.white,
                  fontSize: 16,
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildPage(BeautyPage page) {
    return GridView.builder(
      padding: const EdgeInsets.all(10),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 5,
        childAspectRatio: 0.8,
        crossAxisSpacing: 10,
        mainAxisSpacing: 10,
      ),
      itemCount: page.items.length,
      itemBuilder: (context, index) {
        final item = page.items[index];
        if (_pageSelectedItems[_currentPage] == null && index == 0) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _selectItem(item);
          });
        }
        return _buildBeautyItem(item);
      },
    );
  }

  Widget _buildBeautyItem(BeautyItem item) {
    final isSelected = _selectedItem == item;
    return GestureDetector(
      onTap: () async {
        await _selectItem(item);
      },
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: isSelected ? Colors.blue.withOpacity(0.3) : Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(20),
              border: isSelected ? Border.all(color: Colors.blue, width: 2) : null,
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(20),
              child: Image.asset(
                item.iconPath,
                width: 30,
                height: 30,
                fit: BoxFit.contain,
                errorBuilder: (context, error, stackTrace) {
                  print('Error loading image: ${item.iconPath}');
                  print('Error: $error');
                  return Icon(
                    Icons.image_not_supported,
                    color: Colors.white.withOpacity(0.5),
                    size: 30,
                  );
                },
              ),
            ),
          ),
          const SizedBox(height: 5),
          Text(
            item.title,
            style: TextStyle(
              color: isSelected ? Colors.blue : Colors.white,
              fontSize: 12,
              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ],
      ),
    );
  }
}

class BeautyPage {
  final String title;
  final List<BeautyItem> items;

  BeautyPage({
    required this.title,
    required this.items,
  });
}

enum BeautyType {
  oneKey,
  beauty,
  filter,
  sticker,
}

class BeautyItem {
  final BeautyType type;
  dynamic value;
  final String title;
  final String iconPath;
  final PFBeautyFiterType? beautyType;
  final String? filterName;

  BeautyItem({
    required this.type,
    required this.value,
    required this.title,
    required this.iconPath,
    this.beautyType,
    this.filterName,
  });

  factory BeautyItem.oneKey(int value, String title, String iconPath) {
    return BeautyItem(
      type: BeautyType.oneKey,
      value: value,
      title: title,
      iconPath: iconPath,
    );
  }

  factory BeautyItem.beauty(PFBeautyFiterType beautyType, double value, String title, String iconPath) {
    return BeautyItem(
      type: BeautyType.beauty,
      value: value,
      title: title,
      iconPath: iconPath,
      beautyType: beautyType,
    );
  }

  factory BeautyItem.filter(String filterName, double value, String title, String iconPath) {
    return BeautyItem(
      type: BeautyType.filter,
      value: value,
      title: title,
      iconPath: iconPath,
      filterName: filterName,
    );
  }

  factory BeautyItem.sticker(String filterName, String title, String iconPath) {
    return BeautyItem(
      type: BeautyType.sticker,
      value: 1.0,
      title: title,
      iconPath: iconPath,
      filterName: filterName,
    );
  }
} 
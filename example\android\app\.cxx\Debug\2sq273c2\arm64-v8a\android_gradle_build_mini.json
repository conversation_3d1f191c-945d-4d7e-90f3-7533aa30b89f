{"buildFiles": ["/Users/<USER>/Desktop/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Desktop/mumuFU/push_github/flutter-webrtc-plus/example/android/app/.cxx/Debug/2sq273c2/arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Desktop/mumuFU/push_github/flutter-webrtc-plus/example/android/app/.cxx/Debug/2sq273c2/arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}
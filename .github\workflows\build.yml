name: Build

on:
  push:
    branches:
      - "*/*"
  pull_request:
    branches:
      - "*/*"

jobs:
  dart-format-and-analyze-check:
    name: Dart Format Check
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v1
      - uses: actions/setup-java@v3
        with:
          distribution: "temurin"
          java-version: "17"
          cache: "gradle"
      - uses: subosito/flutter-action@v2
        with:
          flutter-version: "3.24.4"
      - name: Install project dependencies
        run: flutter pub get
      - name: Dart Format Check
        run: dart format lib/ test/ --set-exit-if-changed
      # - name: Import Sorter Check
      #   run: dart run import_sorter:main --no-comments --exit-if-changed
      - name: Dart Analyze Check
        run: flutter analyze
      - name: Dart Test Check
        run: flutter test

  build-for-android:
    name: Build for Flutter Android
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-java@v1
        with:
          java-version: '17.x'
      - uses: actions/checkout@v2
      - uses: subosito/flutter-action@v2
        with:
          flutter-version: "3.24.4"
      - name: Install project dependencies
        run: flutter pub get
      - name: Build for Android
        working-directory: ./example
        run: flutter build apk

  build-for-ios:
    name: Build for Flutter iOS
    runs-on: macos-latest

    steps:
      - uses: actions/checkout@v2
      - uses: subosito/flutter-action@v2
        with:
          flutter-version: "3.24.4"
      - name: Install project dependencies
        run: flutter pub get
      - name: Build for iOS
        working-directory: ./example
        run: flutter build ios --release --no-codesign

  build-for-windows:
    name: Build for flutter Windows
    runs-on: windows-latest

    steps:
      - uses: actions/checkout@v2
      - uses: subosito/flutter-action@v2
        with:
          flutter-version: "3.24.4"
      - name: Install project dependencies
        run: flutter pub get
      - name: Build for Windows
        working-directory: ./example
        run: flutter build windows --release

  build-for-macos:
    name: Build for Flutter macOS (arm64)
    runs-on: macos-14
    steps:
      - uses: actions/checkout@v4
      - name: Select Xcode 15.4
        run: sudo xcode-select --switch /Applications/Xcode_15.4.app/Contents/Developer
      - uses: subosito/flutter-action@v2
        with:
          flutter-version: "3.24.4"
      - name: Install project dependencies
        run: flutter pub get
      - name: Build for macOS
        working-directory: ./example
        run: flutter build macos --release

  # build-for-linux:
  #   name: Build for Flutter Linux
  #   runs-on: ubuntu-latest

  #   steps:
  #     - uses: actions/checkout@v2
  #     - uses: actions/setup-java@v1
  #       with:
  #         java-version: '12.x'
  #     - uses: actions/checkout@v2
  #     - uses: subosito/flutter-action@v2
  #       with:
  #         flutter-version: "3.24.4"
  #     - name: Install project dependencies
  #       run: flutter pub get
  #     - name: Run apt update
  #       run: sudo apt-get update
  #     - name: Install ninja-build libgtk-3-dev
  #       run: sudo apt-get install -y ninja-build libgtk-3-dev
  #     - name: Build for Linux
  #       working-directory: ./example
  #       run: flutter build linux

  # build-for-elinux:
  #   name: Build for Flutter Embedded Linux
  #   runs-on: ubuntu-latest

  #   steps:
  #     - uses: actions/checkout@v2
  #     - uses: actions/setup-java@v1
  #       with:
  #         java-version: '12.x'
  #     - uses: actions/checkout@v2
  #     - uses: subosito/flutter-action@v2
  #       with:
  #         flutter-version: "3.24.4"
  #     - name: Run apt update
  #       run: sudo apt-get update
  #     - name: Install ninja-build libgtk-3-dev
  #       run: sudo apt-get install -y ninja-build libgtk-3-dev
  #     - name: Install elinux
  #       run:  git clone https://github.com/sony/flutter-elinux.git ~/flutter-elinux
  #     - name: Build for elinux
  #       working-directory: ./example
  #       run: /home/<USER>/flutter-elinux/bin/flutter-elinux pub get && /home/<USER>/flutter-elinux/bin/flutter-elinux build elinux

  build-for-web:
    name: Build for Flutter Web
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-java@v1
        with:
          java-version: '12.x'
      - uses: actions/checkout@v2
      - uses: subosito/flutter-action@v2
        with:
          flutter-version: "3.24.4"
      - name: Install project dependencies
        run: flutter pub get
      - name: build for Web
        working-directory: ./example
        run: flutter build web
package com.cloudwebrtc.webrtc

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Matrix
import android.util.Log
import com.hapi.pixelfree.PFBeautyFiterType
import com.hapi.pixelfree.PFDetectFormat
import com.hapi.pixelfree.PFIamgeInput
import com.hapi.pixelfree.PFRotationMode
import com.hapi.pixelfree.PFSrcType
import com.hapi.pixelfree.PixelFree
import com.pixpark.gpupixel.GPUPixelSource.ProcessedFrameDataCallback
import java.io.File
import java.nio.ByteBuffer
import java.nio.ByteOrder


fun interface ProcessedPixelfreeFrameDataCallback {
    fun onResult(var1: Bitmap)
}

class RTCPixelfreeBeautyFilter(context: Context) {
    private val tag = "FlutterRTCBeautyFilters"
    private var mPixelFree: PixelFree? = null
    private var resultCallback: ProcessedPixelfreeFrameDataCallback? = null
    private var useEffect = true

    companion object {
        private var isInitialized: Boolean = false

        fun initialize() {
            isInitialized = true
        }
    }

    init {
        try {
            mPixelFree = PixelFree()
            mPixelFree!!.create()
            val authData = mPixelFree!!.readBundleFile(context, "pixelfreeAuth.lic")
            mPixelFree!!.auth(context, authData, authData.size)
            val face_fiter =
                mPixelFree!!.readBundleFile(context, "filter_model.bundle")
            mPixelFree!!.createBeautyItemFormBundle(
                face_fiter,
                face_fiter.size,
                PFSrcType.PFSrcTypeFilter
            )
        } catch (error: Exception) {
            Log.e(tag, error.message.toString())
        }
    }

    fun setCallback(callback: ProcessedPixelfreeFrameDataCallback) {
        this.resultCallback = callback

    }

    private fun convertPixelsToBitmap(pixels: ByteArray, width: Int, height: Int): Bitmap {
        val bmp = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        bmp.copyPixelsFromBuffer(ByteBuffer.wrap(pixels))
        return bmp
    }

    fun processBitmap(originalBitmap: Bitmap, rotation: Int) {

        val bitmapRotation = if (rotation == 90) 90f else -90f

        val bitmap = rotateBitmap(originalBitmap, bitmapRotation)

        if (mPixelFree!!.isCreate() && useEffect) {
            val w: Int = bitmap.width
            val h: Int = bitmap.height
            val pixels = convertBitmapToRGBA(bitmap)

            val pxInput = PFIamgeInput().apply {
                wigth = w
                height = h
                p_data0 = pixels;
                p_data1 = null
                p_data2 = null
                stride_0 = w *4
                textureID = 0
                format = PFDetectFormat.PFFORMAT_IMAGE_RGBA
                rotationMode = PFRotationMode.PFRotationMode0
            }
            mPixelFree!!.processWithBuffer(pxInput)

            mPixelFree!!.textureIdToBitmap(pxInput.textureID, pxInput.wigth, pxInput.height) { bitmap ->
                if (bitmap != null) {
                    this.resultCallback?.onResult(bitmap)
                }
            }

        }else {
            this.resultCallback?.onResult(bitmap)
        }
    }

    private fun getPixelsFromBitmap(bitmap: Bitmap): ByteBuffer {
        val width = bitmap.width
        val height = bitmap.height

        // Allocate a ByteBuffer to hold the pixel data
        val buffer = ByteBuffer.allocateDirect(width * height * 4)
        buffer.order(ByteOrder.nativeOrder())

        // Copy pixel data from the Bitmap into the ByteBuffer
        bitmap.copyPixelsToBuffer(buffer)
        buffer.position(0)

        return buffer
    }

    fun pixelFreeSetFiterParam(filterName: String, value: Float) {
        mPixelFree?.pixelFreeSetFiterParam(filterName,value)
            ?: Log.e("PixelFree", "SDK not initialized")
    }

    fun pixelFreeSetBeautyFiterParam(type: PFBeautyFiterType, value: Int) {
        mPixelFree?.pixelFreeSetBeautyFiterParam(type, value)
            ?: Log.e("PixelFree", "SDK not initialized")
    }

    fun pixelFreeSetBeautyFiterParam(type: PFBeautyFiterType, value: Float) {
        mPixelFree?.pixelFreeSetBeautyFiterParam(type, value)
            ?: Log.e("PixelFree", "SDK not initialized")
    }

    fun pixelFreeSetBeautyUseEffect(isUse:Boolean) {
        useEffect = isUse;
    }

    fun pixelFreeGetBeautyUseEffect():Boolean {
         return useEffect;
    }

    fun pixelFreeSetStickerBundle(path: String) {
        if (path == "origin") {
            val byteArray = ByteArray(0)
            mPixelFree?.createBeautyItemFormBundle(
                byteArray,
                0,
                PFSrcType.PFSrcTypeStickerFile
            )
        } else {
            // 前置是镜像横屏的数据
            mPixelFree?.pixelFreeSetBeautyExtend(PFBeautyFiterType.PFBeautyFiterExtend,"mirrorX_1");
            mPixelFree?.pixelFreeSetBeautyExtend(PFBeautyFiterType.PFBeautyFiterExtend,"rotation_90");
            try {
                val file = File(path)
                if (file.exists()) {
                    val bytes = file.readBytes()
                    mPixelFree?.createBeautyItemFormBundle(
                        bytes,
                        bytes.size,
                        PFSrcType.PFSrcTypeStickerFile
                    )
                } else {
                    Log.e(tag, "貼紙包文件不存在: $path")
                }
            } catch (e: Exception) {
                Log.e(tag, "讀取貼紙包文件失敗: ${e.message}")
            }
        }
    }

    fun pixelFreeRealses() {
         mPixelFree?.release();
    }

    private fun rotateBitmap(bitmap: Bitmap, degrees: Float): Bitmap {
        val matrix = Matrix()
        matrix.postRotate(degrees)
        return Bitmap.createBitmap(bitmap, 0, 0, bitmap.width, bitmap.height, matrix, true)
    }

    private fun convertBitmapToRGBA(bitmap: Bitmap): ByteArray {
        val width = bitmap.width
        val height = bitmap.height
        val rgbaDataa = IntArray(width * height)

        bitmap.getPixels(rgbaDataa, 0, width, 0, 0, width, height)
        return intArrayToByteArray(rgbaDataa)
    }

    private fun intArrayToByteArray(rgbaData: IntArray): ByteArray {
        // 创建一个 ByteArray，大小为 RGBA 数据的四倍（每个像素四个字节）
        val byteArray = ByteArray(rgbaData.size * 4)

        for (i in rgbaData.indices) {
            // 获取 RGBA 颜色
            val color = rgbaData[i]

            // 解析 R、G、B、A 通道
            val a = (color shr 24 and 0xFF).toByte() // Alpha
            val r = (color shr 16 and 0xFF).toByte() // Red
            val g = (color shr 8 and 0xFF).toByte()  // Green
            val b = (color and 0xFF).toByte()        // Blue

            // 将 RGBA 值存入 ByteArray
            byteArray[i * 4] = r
            byteArray[i * 4 + 1] = g
            byteArray[i * 4 + 2] = b
            byteArray[i * 4 + 3] = a
        }

        return byteArray
    }
}
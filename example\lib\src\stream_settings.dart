class StreamSettings {
  final String resolution;
  final int fps;
  final int bitrate;
  final int keyframeInterval;
  final bool enableBFrame;
  final String profile;
  final String codec;
  final String encodingMode;
  final String streamUrl;
  final bool fromWebViewJS;

  StreamSettings({
    required this.resolution,
    required this.fps,
    required this.bitrate,
    required this.keyframeInterval,
    required this.enableBFrame,
    required this.profile,
    required this.codec,
    required this.encodingMode,
    required this.streamUrl,
    this.fromWebViewJS = false,
  });
} 
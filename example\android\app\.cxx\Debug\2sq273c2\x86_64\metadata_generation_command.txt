                        -H/Users/<USER>/Desktop/flutter/packages/flutter_tools/gradle/src/main/groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=24
-<PERSON>AN<PERSON>OID_PLATFORM=android-24
-DANDROID_ABI=x86_64
-DCMAKE_ANDROID_ARCH_ABI=x86_64
-DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/25.2.9519653
-DCMAKE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/25.2.9519653
-DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/25.2.9519653/build/cmake/android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/Desktop/mumuFU/push_github/flutter-webrtc-plus/example/build/app/intermediates/cxx/Debug/2sq273c2/obj/x86_64
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/Desktop/mumuFU/push_github/flutter-webrtc-plus/example/build/app/intermediates/cxx/Debug/2sq273c2/obj/x86_64
-DCMAKE_BUILD_TYPE=Debug
-B/Users/<USER>/Desktop/mumuFU/push_github/flutter-webrtc-plus/example/android/app/.cxx/Debug/2sq273c2/x86_64
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2
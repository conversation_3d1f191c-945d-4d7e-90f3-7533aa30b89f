@echo off
chcp 65001 >nul
echo 🚀 啟動 Flutter WebRTC 日志監控器...
echo.

REM 檢查是否安裝了Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 錯誤: 找不到 Python。請先安裝 Python 3.6+ 並添加到 PATH。
    echo 下載地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

REM 檢查是否需要安裝依賴
python -c "import win10toast" >nul 2>&1
if %errorlevel% neq 0 (
    echo 📦 安裝系統通知依賴...
    pip install win10toast
)

REM 啟動監控腳本
echo 🔍 開始監控 Flutter 應用日志...
echo.
python log_monitor.py

pause 
file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/collection-1.17.0/lib/collection.dart
file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/collection-1.17.0/lib/src/algorithms.dart
file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/collection-1.17.0/lib/src/boollist.dart
file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/collection-1.17.0/lib/src/canonicalized_map.dart
file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/collection-1.17.0/lib/src/combined_wrappers/combined_iterable.dart
file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/collection-1.17.0/lib/src/combined_wrappers/combined_iterator.dart
file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/collection-1.17.0/lib/src/combined_wrappers/combined_list.dart
file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/collection-1.17.0/lib/src/combined_wrappers/combined_map.dart
file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/collection-1.17.0/lib/src/comparators.dart
file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/collection-1.17.0/lib/src/empty_unmodifiable_set.dart
file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/collection-1.17.0/lib/src/equality.dart
file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/collection-1.17.0/lib/src/equality_map.dart
file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/collection-1.17.0/lib/src/equality_set.dart
file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/collection-1.17.0/lib/src/functions.dart
file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/collection-1.17.0/lib/src/iterable_extensions.dart
file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/collection-1.17.0/lib/src/iterable_zip.dart
file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/collection-1.17.0/lib/src/list_extensions.dart
file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/collection-1.17.0/lib/src/priority_queue.dart
file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/collection-1.17.0/lib/src/queue_list.dart
file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/collection-1.17.0/lib/src/union_set.dart
file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/collection-1.17.0/lib/src/union_set_controller.dart
file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/collection-1.17.0/lib/src/unmodifiable_wrappers.dart
file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/collection-1.17.0/lib/src/utils.dart
file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/collection-1.17.0/lib/src/wrappers.dart
file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/js-0.6.5/lib/js.dart
file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/meta-1.8.0/lib/meta.dart
file:///Users/<USER>/.pub-cache/hosted/pub.flutter-io.cn/meta-1.8.0/lib/meta_meta.dart
file:///Users/<USER>/Desktop/projects/flutter-webrtc/.dart_tool/package_config.json
file:///Users/<USER>/Desktop/projects/flutter-webrtc/lib/src/web/rtc_transform_stream.dart
file:///Users/<USER>/Desktop/projects/flutter-webrtc/web/crypto.dart
file:///Users/<USER>/Desktop/projects/flutter-webrtc/web/e2ee.cryptor.dart
file:///Users/<USER>/Desktop/projects/flutter-webrtc/web/e2ee.utils.dart
file:///Users/<USER>/Desktop/projects/flutter-webrtc/web/e2ee.worker.dart
file:///Users/<USER>/bin/flutter/bin/cache/dart-sdk/lib/_internal/dart2js_platform.dill
file:///Users/<USER>/bin/flutter/bin/cache/dart-sdk/lib/libraries.json
org-dartlang-sdk:///lib/_http/crypto.dart
org-dartlang-sdk:///lib/_http/embedder_config.dart
org-dartlang-sdk:///lib/_http/http.dart
org-dartlang-sdk:///lib/_http/http_date.dart
org-dartlang-sdk:///lib/_http/http_headers.dart
org-dartlang-sdk:///lib/_http/http_impl.dart
org-dartlang-sdk:///lib/_http/http_parser.dart
org-dartlang-sdk:///lib/_http/http_session.dart
org-dartlang-sdk:///lib/_http/http_testing.dart
org-dartlang-sdk:///lib/_http/overrides.dart
org-dartlang-sdk:///lib/_http/websocket.dart
org-dartlang-sdk:///lib/_http/websocket_impl.dart
org-dartlang-sdk:///lib/_internal/js_runtime/lib/annotations.dart
org-dartlang-sdk:///lib/_internal/js_runtime/lib/async_patch.dart
org-dartlang-sdk:///lib/_internal/js_runtime/lib/collection_patch.dart
org-dartlang-sdk:///lib/_internal/js_runtime/lib/constant_map.dart
org-dartlang-sdk:///lib/_internal/js_runtime/lib/convert_patch.dart
org-dartlang-sdk:///lib/_internal/js_runtime/lib/core_patch.dart
org-dartlang-sdk:///lib/_internal/js_runtime/lib/dart2js_runtime_metrics.dart
org-dartlang-sdk:///lib/_internal/js_runtime/lib/developer_patch.dart
org-dartlang-sdk:///lib/_internal/js_runtime/lib/foreign_helper.dart
org-dartlang-sdk:///lib/_internal/js_runtime/lib/instantiation.dart
org-dartlang-sdk:///lib/_internal/js_runtime/lib/interceptors.dart
org-dartlang-sdk:///lib/_internal/js_runtime/lib/internal_patch.dart
org-dartlang-sdk:///lib/_internal/js_runtime/lib/io_patch.dart
org-dartlang-sdk:///lib/_internal/js_runtime/lib/isolate_patch.dart
org-dartlang-sdk:///lib/_internal/js_runtime/lib/js_array.dart
org-dartlang-sdk:///lib/_internal/js_runtime/lib/js_helper.dart
org-dartlang-sdk:///lib/_internal/js_runtime/lib/js_names.dart
org-dartlang-sdk:///lib/_internal/js_runtime/lib/js_number.dart
org-dartlang-sdk:///lib/_internal/js_runtime/lib/js_patch.dart
org-dartlang-sdk:///lib/_internal/js_runtime/lib/js_primitives.dart
org-dartlang-sdk:///lib/_internal/js_runtime/lib/js_string.dart
org-dartlang-sdk:///lib/_internal/js_runtime/lib/late_helper.dart
org-dartlang-sdk:///lib/_internal/js_runtime/lib/linked_hash_map.dart
org-dartlang-sdk:///lib/_internal/js_runtime/lib/math_patch.dart
org-dartlang-sdk:///lib/_internal/js_runtime/lib/native_helper.dart
org-dartlang-sdk:///lib/_internal/js_runtime/lib/native_typed_data.dart
org-dartlang-sdk:///lib/_internal/js_runtime/lib/regexp_helper.dart
org-dartlang-sdk:///lib/_internal/js_runtime/lib/string_helper.dart
org-dartlang-sdk:///lib/_internal/js_runtime/lib/synced/async_await_error_codes.dart
org-dartlang-sdk:///lib/_internal/js_runtime/lib/synced/embedded_names.dart
org-dartlang-sdk:///lib/_internal/js_runtime/lib/synced/load_library_priority.dart
org-dartlang-sdk:///lib/_internal/js_runtime/lib/typed_data_patch.dart
org-dartlang-sdk:///lib/_internal/js_shared/lib/js_util_patch.dart
org-dartlang-sdk:///lib/_internal/js_shared/lib/rti.dart
org-dartlang-sdk:///lib/_internal/js_shared/lib/synced/embedded_names.dart
org-dartlang-sdk:///lib/_internal/js_shared/lib/synced/recipe_syntax.dart
org-dartlang-sdk:///lib/async/async.dart
org-dartlang-sdk:///lib/async/async_error.dart
org-dartlang-sdk:///lib/async/broadcast_stream_controller.dart
org-dartlang-sdk:///lib/async/deferred_load.dart
org-dartlang-sdk:///lib/async/future.dart
org-dartlang-sdk:///lib/async/future_impl.dart
org-dartlang-sdk:///lib/async/schedule_microtask.dart
org-dartlang-sdk:///lib/async/stream.dart
org-dartlang-sdk:///lib/async/stream_controller.dart
org-dartlang-sdk:///lib/async/stream_impl.dart
org-dartlang-sdk:///lib/async/stream_pipe.dart
org-dartlang-sdk:///lib/async/stream_transformers.dart
org-dartlang-sdk:///lib/async/timer.dart
org-dartlang-sdk:///lib/async/zone.dart
org-dartlang-sdk:///lib/collection/collection.dart
org-dartlang-sdk:///lib/collection/collections.dart
org-dartlang-sdk:///lib/collection/hash_map.dart
org-dartlang-sdk:///lib/collection/hash_set.dart
org-dartlang-sdk:///lib/collection/iterable.dart
org-dartlang-sdk:///lib/collection/iterator.dart
org-dartlang-sdk:///lib/collection/linked_hash_map.dart
org-dartlang-sdk:///lib/collection/linked_hash_set.dart
org-dartlang-sdk:///lib/collection/linked_list.dart
org-dartlang-sdk:///lib/collection/list.dart
org-dartlang-sdk:///lib/collection/maps.dart
org-dartlang-sdk:///lib/collection/queue.dart
org-dartlang-sdk:///lib/collection/set.dart
org-dartlang-sdk:///lib/collection/splay_tree.dart
org-dartlang-sdk:///lib/convert/ascii.dart
org-dartlang-sdk:///lib/convert/base64.dart
org-dartlang-sdk:///lib/convert/byte_conversion.dart
org-dartlang-sdk:///lib/convert/chunked_conversion.dart
org-dartlang-sdk:///lib/convert/codec.dart
org-dartlang-sdk:///lib/convert/convert.dart
org-dartlang-sdk:///lib/convert/converter.dart
org-dartlang-sdk:///lib/convert/encoding.dart
org-dartlang-sdk:///lib/convert/html_escape.dart
org-dartlang-sdk:///lib/convert/json.dart
org-dartlang-sdk:///lib/convert/latin1.dart
org-dartlang-sdk:///lib/convert/line_splitter.dart
org-dartlang-sdk:///lib/convert/string_conversion.dart
org-dartlang-sdk:///lib/convert/utf.dart
org-dartlang-sdk:///lib/core/annotations.dart
org-dartlang-sdk:///lib/core/bigint.dart
org-dartlang-sdk:///lib/core/bool.dart
org-dartlang-sdk:///lib/core/comparable.dart
org-dartlang-sdk:///lib/core/core.dart
org-dartlang-sdk:///lib/core/date_time.dart
org-dartlang-sdk:///lib/core/double.dart
org-dartlang-sdk:///lib/core/duration.dart
org-dartlang-sdk:///lib/core/enum.dart
org-dartlang-sdk:///lib/core/errors.dart
org-dartlang-sdk:///lib/core/exceptions.dart
org-dartlang-sdk:///lib/core/function.dart
org-dartlang-sdk:///lib/core/int.dart
org-dartlang-sdk:///lib/core/invocation.dart
org-dartlang-sdk:///lib/core/iterable.dart
org-dartlang-sdk:///lib/core/iterator.dart
org-dartlang-sdk:///lib/core/list.dart
org-dartlang-sdk:///lib/core/map.dart
org-dartlang-sdk:///lib/core/null.dart
org-dartlang-sdk:///lib/core/num.dart
org-dartlang-sdk:///lib/core/object.dart
org-dartlang-sdk:///lib/core/pattern.dart
org-dartlang-sdk:///lib/core/print.dart
org-dartlang-sdk:///lib/core/record.dart
org-dartlang-sdk:///lib/core/regexp.dart
org-dartlang-sdk:///lib/core/set.dart
org-dartlang-sdk:///lib/core/sink.dart
org-dartlang-sdk:///lib/core/stacktrace.dart
org-dartlang-sdk:///lib/core/stopwatch.dart
org-dartlang-sdk:///lib/core/string.dart
org-dartlang-sdk:///lib/core/string_buffer.dart
org-dartlang-sdk:///lib/core/string_sink.dart
org-dartlang-sdk:///lib/core/symbol.dart
org-dartlang-sdk:///lib/core/type.dart
org-dartlang-sdk:///lib/core/uri.dart
org-dartlang-sdk:///lib/core/weak.dart
org-dartlang-sdk:///lib/developer/developer.dart
org-dartlang-sdk:///lib/developer/extension.dart
org-dartlang-sdk:///lib/developer/profiler.dart
org-dartlang-sdk:///lib/developer/service.dart
org-dartlang-sdk:///lib/developer/timeline.dart
org-dartlang-sdk:///lib/html/dart2js/html_dart2js.dart
org-dartlang-sdk:///lib/html/html_common/conversions.dart
org-dartlang-sdk:///lib/html/html_common/conversions_dart2js.dart
org-dartlang-sdk:///lib/html/html_common/css_class_set.dart
org-dartlang-sdk:///lib/html/html_common/device.dart
org-dartlang-sdk:///lib/html/html_common/filtered_element_list.dart
org-dartlang-sdk:///lib/html/html_common/html_common_dart2js.dart
org-dartlang-sdk:///lib/html/html_common/lists.dart
org-dartlang-sdk:///lib/html/html_common/metadata.dart
org-dartlang-sdk:///lib/indexed_db/dart2js/indexed_db_dart2js.dart
org-dartlang-sdk:///lib/internal/async_cast.dart
org-dartlang-sdk:///lib/internal/bytes_builder.dart
org-dartlang-sdk:///lib/internal/cast.dart
org-dartlang-sdk:///lib/internal/errors.dart
org-dartlang-sdk:///lib/internal/internal.dart
org-dartlang-sdk:///lib/internal/iterable.dart
org-dartlang-sdk:///lib/internal/linked_list.dart
org-dartlang-sdk:///lib/internal/list.dart
org-dartlang-sdk:///lib/internal/patch.dart
org-dartlang-sdk:///lib/internal/print.dart
org-dartlang-sdk:///lib/internal/sort.dart
org-dartlang-sdk:///lib/internal/symbol.dart
org-dartlang-sdk:///lib/io/common.dart
org-dartlang-sdk:///lib/io/data_transformer.dart
org-dartlang-sdk:///lib/io/directory.dart
org-dartlang-sdk:///lib/io/directory_impl.dart
org-dartlang-sdk:///lib/io/embedder_config.dart
org-dartlang-sdk:///lib/io/eventhandler.dart
org-dartlang-sdk:///lib/io/file.dart
org-dartlang-sdk:///lib/io/file_impl.dart
org-dartlang-sdk:///lib/io/file_system_entity.dart
org-dartlang-sdk:///lib/io/io.dart
org-dartlang-sdk:///lib/io/io_resource_info.dart
org-dartlang-sdk:///lib/io/io_service.dart
org-dartlang-sdk:///lib/io/io_sink.dart
org-dartlang-sdk:///lib/io/link.dart
org-dartlang-sdk:///lib/io/namespace_impl.dart
org-dartlang-sdk:///lib/io/network_profiling.dart
org-dartlang-sdk:///lib/io/overrides.dart
org-dartlang-sdk:///lib/io/platform.dart
org-dartlang-sdk:///lib/io/platform_impl.dart
org-dartlang-sdk:///lib/io/process.dart
org-dartlang-sdk:///lib/io/secure_server_socket.dart
org-dartlang-sdk:///lib/io/secure_socket.dart
org-dartlang-sdk:///lib/io/security_context.dart
org-dartlang-sdk:///lib/io/service_object.dart
org-dartlang-sdk:///lib/io/socket.dart
org-dartlang-sdk:///lib/io/stdio.dart
org-dartlang-sdk:///lib/io/string_transformer.dart
org-dartlang-sdk:///lib/io/sync_socket.dart
org-dartlang-sdk:///lib/isolate/capability.dart
org-dartlang-sdk:///lib/isolate/isolate.dart
org-dartlang-sdk:///lib/js/_js.dart
org-dartlang-sdk:///lib/js/_js_annotations.dart
org-dartlang-sdk:///lib/js/_js_client.dart
org-dartlang-sdk:///lib/js/js.dart
org-dartlang-sdk:///lib/js_util/js_util.dart
org-dartlang-sdk:///lib/math/math.dart
org-dartlang-sdk:///lib/math/point.dart
org-dartlang-sdk:///lib/math/random.dart
org-dartlang-sdk:///lib/math/rectangle.dart
org-dartlang-sdk:///lib/svg/dart2js/svg_dart2js.dart
org-dartlang-sdk:///lib/typed_data/typed_data.dart
org-dartlang-sdk:///lib/typed_data/unmodifiable_typed_data.dart
org-dartlang-sdk:///lib/web_audio/dart2js/web_audio_dart2js.dart
org-dartlang-sdk:///lib/web_gl/dart2js/web_gl_dart2js.dart
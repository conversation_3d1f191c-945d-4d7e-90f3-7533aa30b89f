*.iml
.idea
.DS_Store
example/pubspec.lock
pubspec.lock
example/ios/Podfile.lock
GeneratedPluginRegistrant.java
example/android/.gradle
WorkspaceSettings.xcsettings
example/.flutter-plugins
example/android/local.properties
.dart_tool/package_config.json
android/.project
example/ios/Runner/GeneratedPluginRegistrant.m
example/ios/Runner/GeneratedPluginRegistrant.h
example/ios/Flutter/Generated.xcconfig
example/ios/Flutter/flutter_export_environment.sh

# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.buildlog/
.history
.svn/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# Flutter/Dart/Pub related
**/doc/api/
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.packages
.pub-cache/
.pub/
/build/
/android/.gradle/


android/.classpath
android/.settings/org.eclipse.buildship.core.prefs

# VSCode
.vscode/

!webrtc_android.iml
!webrtc.iml

# vs
*.pdb

# GPUPixel
macos/gpupixel-macos-1.2.5
macos/*.zip
macos/*.framework

ios/gpupixel-ios-1.2.5
ios/*.zip
ios/*.framework

third_party/libwebrtc/lib/elinux-arm64
third_party/libwebrtc/lib/elinux-x64
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Flutter WebRTC 日志監控腳本
當檢測到錯誤時會發出通知
"""

import subprocess
import re
import time
import sys
import threading
from datetime import datetime
from pathlib import Path
import json
import os

class LogMonitor:
    def __init__(self):
        self.error_patterns = [
            r'(?i)error[:\s]',
            r'(?i)exception[:\s]',
            r'(?i)failed[:\s]',
            r'(?i)crash',
            r'(?i)timeout',
            r'(?i)connection.*error',
            r'(?i)network.*error',
            r'(?i)dio.*錯誤',
            r'(?i)推流.*錯誤',
            r'(?i)連接失敗',
            r'(?i)伺服器.*不可用',
            r'flutter:.*error',
            r'flutter:.*exception',
            r'I/flutter.*error',
            r'E/flutter',
        ]
        
        self.warning_patterns = [
            r'(?i)warning[:\s]',
            r'(?i)warn[:\s]',
            r'(?i)⚠️',
            r'(?i)retry',
            r'(?i)重試',
            r'W/flutter',
        ]
        
        self.log_file_path = "flutter_logs.txt"
        self.error_count = 0
        self.warning_count = 0
        self.last_notification_time = 0
        self.notification_cooldown = 5  # 5秒冷卻時間
        
    def print_colored(self, message, color='white'):
        """彩色打印"""
        colors = {
            'red': '\033[91m',
            'yellow': '\033[93m',
            'green': '\033[92m',
            'blue': '\033[94m',
            'purple': '\033[95m',
            'cyan': '\033[96m',
            'white': '\033[97m',
            'reset': '\033[0m'
        }
        print(f"{colors.get(color, colors['white'])}{message}{colors['reset']}")
        
    def play_notification_sound(self):
        """播放通知音效（Windows）"""
        try:
            import winsound
            winsound.Beep(1000, 500)  # 1000Hz, 持續500ms
        except ImportError:
            # 如果無法播放音效，使用視覺通知
            print("\a")  # ASCII bell
            
    def send_notification(self, title, message, level='error'):
        """發送系統通知"""
        current_time = time.time()
        if current_time - self.last_notification_time < self.notification_cooldown:
            return
            
        self.last_notification_time = current_time
        
        # Windows 通知
        try:
            import win10toast
            toaster = win10toast.ToastNotifier()
            toaster.show_toast(title, message, duration=10)
        except ImportError:
            # 如果沒有安裝win10toast，使用控制台通知
            if level == 'error':
                self.print_colored(f"🚨 {title}: {message}", 'red')
                self.play_notification_sound()
            else:
                self.print_colored(f"⚠️ {title}: {message}", 'yellow')
                
    def check_for_errors(self, line):
        """檢查日志行是否包含錯誤"""
        line_lower = line.lower()
        
        # 檢查錯誤模式
        for pattern in self.error_patterns:
            if re.search(pattern, line, re.IGNORECASE):
                self.error_count += 1
                timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                error_msg = f"[{timestamp}] 檢測到錯誤 #{self.error_count}"
                self.print_colored(f"\n{'='*50}", 'red')
                self.print_colored(error_msg, 'red')
                self.print_colored(f"錯誤內容: {line.strip()}", 'red')
                self.print_colored('='*50, 'red')
                
                self.send_notification(
                    "Flutter 應用錯誤",
                    f"檢測到第 {self.error_count} 個錯誤: {line.strip()[:100]}...",
                    'error'
                )
                return True
                
        # 檢查警告模式
        for pattern in self.warning_patterns:
            if re.search(pattern, line, re.IGNORECASE):
                self.warning_count += 1
                timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                warning_msg = f"[{timestamp}] 檢測到警告 #{self.warning_count}"
                self.print_colored(f"\n{'-'*50}", 'yellow')
                self.print_colored(warning_msg, 'yellow')
                self.print_colored(f"警告內容: {line.strip()}", 'yellow')
                self.print_colored('-'*50, 'yellow')
                return True
                
        return False
        
    def monitor_flutter_logs(self):
        """監控Flutter應用日志"""
        self.print_colored("🔍 開始監控 Flutter 應用日志...", 'green')
        self.print_colored("❌ 檢測錯誤模式: error, exception, failed, crash, timeout", 'blue')
        self.print_colored("⚠️ 檢測警告模式: warning, retry, 重試", 'blue')
        self.print_colored("💡 按 Ctrl+C 停止監控\n", 'cyan')
        
        try:
            # 啟動 flutter logs 命令
            process = subprocess.Popen(
                ['flutter', 'logs'],
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            with open(self.log_file_path, 'a', encoding='utf-8') as log_file:
                while True:
                    line = process.stdout.readline()
                    if not line:
                        break
                        
                    # 記錄到文件
                    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    log_file.write(f"[{timestamp}] {line}")
                    log_file.flush()
                    
                    # 檢查錯誤和警告
                    if not self.check_for_errors(line):
                        # 正常日志輸出（灰色顯示）
                        print(f"\033[90m{line.rstrip()}\033[0m")
                        
        except FileNotFoundError:
            self.print_colored("❌ 錯誤: 找不到 flutter 命令。請確保已安裝 Flutter SDK 並添加到 PATH。", 'red')
            self.print_colored("💡 或者手動啟動 Flutter 應用，腳本將監控 flutter_logs.txt 文件", 'yellow')
            self.monitor_log_file()
        except KeyboardInterrupt:
            self.print_colored("\n⏹️ 停止監控", 'green')
        except Exception as e:
            self.print_colored(f"❌ 監控過程中發生錯誤: {e}", 'red')
            
    def monitor_log_file(self):
        """監控日志文件"""
        self.print_colored(f"📁 監控日志文件: {self.log_file_path}", 'blue')
        
        try:
            # 如果文件不存在，創建一個空文件
            if not os.path.exists(self.log_file_path):
                open(self.log_file_path, 'w').close()
                
            with open(self.log_file_path, 'r', encoding='utf-8') as f:
                # 移動到文件末尾
                f.seek(0, 2)
                
                while True:
                    line = f.readline()
                    if line:
                        if not self.check_for_errors(line):
                            print(f"\033[90m{line.rstrip()}\033[0m")
                    else:
                        time.sleep(0.1)
                        
        except KeyboardInterrupt:
            self.print_colored("\n⏹️ 停止監控", 'green')
        except Exception as e:
            self.print_colored(f"❌ 監控文件時發生錯誤: {e}", 'red')
            
    def show_status(self):
        """顯示狀態信息"""
        def status_thread():
            while True:
                time.sleep(30)  # 每30秒顯示一次狀態
                timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                status = f"[{timestamp}] 狀態: 錯誤 {self.error_count} 個, 警告 {self.warning_count} 個"
                self.print_colored(status, 'cyan')
                
        thread = threading.Thread(target=status_thread, daemon=True)
        thread.start()
        
    def run(self):
        """運行監控器"""
        self.print_colored("🚀 Flutter WebRTC 日志監控器啟動", 'green')
        self.show_status()
        
        # 嘗試監控flutter logs，如果失敗則監控文件
        self.monitor_flutter_logs()

if __name__ == "__main__":
    monitor = LogMonitor()
    monitor.run() 
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import '../main.dart' show StreamSettingsPage, TestJumpPage;
import 'dart:convert';

class WebViewPage extends StatefulWidget {
  final String url;
  final String title;

  const WebViewPage({
    Key? key,
    required this.url,
    required this.title,
  }) : super(key: key);

  @override
  State<WebViewPage> createState() => _WebViewPageState();
}

class _WebViewPageState extends State<WebViewPage> {
  InAppWebViewController? _webViewController;
  bool _isLoading = true;
  double _progress = 0;
  String? _lastReceivedStreamUrl;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              _webViewController?.reload();
            },
          ),
          IconButton(
            icon: const Icon(Icons.info_outline),
            onPressed: () {
              _showInstructions();
            },
          ),
          if (_lastReceivedStreamUrl != null)
            IconButton(
              icon: const Icon(Icons.live_tv),
              onPressed: () {
                _showStreamUrlDialog();
              },
              tooltip: '查看最後接收的直播連結',
            ),
        ],
      ),
      body: Stack(
        children: [
          InAppWebView(
            initialUrlRequest: URLRequest(url: WebUri(widget.url)),
            initialSettings: InAppWebViewSettings(
              javaScriptEnabled: true,
              domStorageEnabled: true,
              allowsInlineMediaPlayback: true,
              mediaPlaybackRequiresUserGesture: false,
              mixedContentMode: MixedContentMode.MIXED_CONTENT_ALWAYS_ALLOW,
              clearCache: false,
              supportZoom: true,
              useOnDownloadStart: true,
            ),
            onWebViewCreated: (controller) {
              _webViewController = controller;
              
              // 註冊主要的JavaScript處理函數 - 匹配前端的'sendDataToDart'
              controller.addJavaScriptHandler(
                handlerName: 'sendDataToDart',
                callback: (args) {
                  if (args.isNotEmpty) {
                    final data = args[0].toString();
                    
                    try {
                      // 嘗試解析JSON數據
                      final jsonData = json.decode(data);
                      if (jsonData is Map<String, dynamic>) {
                        final streamUrl = jsonData['streamUrl'] as String?;
                        final autoNavigate = jsonData['autoNavigate'] as bool? ?? true;
                        
                        if (streamUrl != null && streamUrl.isNotEmpty) {
                          if (autoNavigate) {
                            // 自動跳轉到推流頁面
                            _autoNavigateToStreamSettings(streamUrl);
                          } else {
                            // 只顯示對話框
                            _handleStreamUrl(streamUrl);
                          }
                          return {'status': 'success', 'message': '直播連結已成功接收'};
                        }
                      }
                    } catch (e) {
                      // 如果不是JSON，當作普通字符串處理
                      _handleStreamUrl(data);
                      return {'status': 'success', 'message': '直播連結已成功接收'};
                    }
                  }
                  return {'status': 'error', 'message': '未接收到數據'};
                },
              );
              
              // 註冊備用的JavaScript處理函數
              controller.addJavaScriptHandler(
                handlerName: 'sendToFlutter',
                callback: (args) {
                  if (args.isNotEmpty) {
                    _showDataDialog(args[0].toString());
                  }
                },
              );

              // 註冊FlutterChannel處理函數
              controller.addJavaScriptHandler(
                handlerName: 'FlutterChannel',
                callback: (args) {
                  if (args.isNotEmpty) {
                    final data = args[0].toString();
                    _handleStreamUrl(data);
                  }
                },
              );
            },
            onLoadStart: (controller, url) {
              setState(() {
                _isLoading = true;
                _progress = 0;
              });
            },
            onLoadStop: (controller, url) async {
              setState(() {
                _isLoading = false;
              });
              
              // 注入JavaScript代碼以建立通信橋樑
              await controller.evaluateJavascript(source: '''
                // 設置多種通信方式的兼容性
                window.flutterApp = {
                  sendToFlutter: function(data) {
                    if (window.flutter_inappwebview && window.flutter_inappwebview.callHandler) {
                      return window.flutter_inappwebview.callHandler('sendToFlutter', data);
                    }
                  },
                  sendDataToDart: function(data) {
                    if (window.flutter_inappwebview && window.flutter_inappwebview.callHandler) {
                      return window.flutter_inappwebview.callHandler('sendDataToDart', data);
                    }
                  }
                };
                
                // 為了兼容性，也可以直接調用
                window.sendToFlutter = function(data) {
                  if (window.flutter_inappwebview && window.flutter_inappwebview.callHandler) {
                    return window.flutter_inappwebview.callHandler('sendToFlutter', data);
                  }
                };

                // 添加FlutterChannel兼容性
                if (!window.FlutterChannel) {
                  window.FlutterChannel = {
                    postMessage: function(data) {
                      if (window.flutter_inappwebview && window.flutter_inappwebview.callHandler) {
                        return window.flutter_inappwebview.callHandler('FlutterChannel', data);
                      }
                    }
                  };
                }
                
                console.log('Flutter communication bridge established');
                console.log('Available methods: sendDataToDart, sendToFlutter, FlutterChannel.postMessage');
              ''');
            },
            onProgressChanged: (controller, progress) {
              setState(() {
                _progress = progress / 100;
              });
            },
            onConsoleMessage: (controller, consoleMessage) {
              debugPrint('Console: ${consoleMessage.message}');
            },
            onLoadError: (controller, url, code, message) {
              debugPrint('WebView Error: $message');
            },
          ),
          if (_isLoading)
            Container(
              color: Colors.white.withOpacity(0.8),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const CircularProgressIndicator(),
                    const SizedBox(height: 16),
                    Text(
                      '載入中... ${(_progress * 100).toInt()}%',
                      style: const TextStyle(fontSize: 16),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  // 處理直播URL的專用函數
  void _handleStreamUrl(String streamUrl) {
    setState(() {
      _lastReceivedStreamUrl = streamUrl;
    });

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.live_tv, color: Colors.red),
              SizedBox(width: 8),
              Text('接收到直播連結'),
            ],
          ),
          content: Container(
            constraints: const BoxConstraints(
              maxHeight: 300,
              maxWidth: 500,
            ),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.blue[50],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.blue[200]!),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '直播連結：',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.blue[700],
                          ),
                        ),
                        const SizedBox(height: 8),
                        SelectableText(
                          streamUrl,
                          style: const TextStyle(
                            fontFamily: 'monospace',
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Icon(Icons.access_time, size: 16, color: Colors.grey[600]),
                      const SizedBox(width: 4),
                      Text(
                        '接收時間: ${DateTime.now().toString().substring(0, 19)}',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.green[50],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.green[200]!),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.auto_awesome, color: Colors.green[600], size: 20),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            '將自動跳轉到推流頁面並設定此連結',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.green[700],
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _copyToClipboard(streamUrl);
              },
              child: const Text('複製連結'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _navigateToStreamSettings(streamUrl);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
              child: const Text('前往推流'),
            ),
          ],
        );
      },
    );
  }

  // 跳轉到推流設置頁面
  void _navigateToStreamSettings(String streamUrl) {
    // 發送確認響應給網頁
    _sendResponseToWebView('success');
    
    // 使用 push 而不是 pushReplacement，這樣返回時會回到 WebViewPage
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => StreamSettingsPage(initialStreamUrl: streamUrl),
      ),
    );
    
    // 顯示成功提示
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('已跳轉到推流頁面，直播連結已自動填入'),
        backgroundColor: Colors.green,
        duration: Duration(seconds: 3),
      ),
    );
  }

  // 自動跳轉到推流設置頁面（不顯示對話框）
  void _autoNavigateToStreamSettings(String streamUrl) {
    debugPrint('🚀 開始自動跳轉流程, URL: $streamUrl');
    
    // 檢查widget是否仍然掛載
    if (!mounted) {
      debugPrint('❌ Widget已卸載，取消跳轉');
      return;
    }
    
    setState(() {
      _lastReceivedStreamUrl = streamUrl;
    });
    
    debugPrint('✅ State已更新，準備跳轉');
    
    // 延遲執行跳轉以確保當前操作完成
    Future.delayed(const Duration(milliseconds: 300), () {
      if (!mounted) {
        debugPrint('❌ 延遲檢查：Widget已卸載，取消跳轉');
        return;
      }
      
      debugPrint('🎯 執行跳轉到推流頁面');
      
      try {
        // 使用 push 而不是 pushReplacement，這樣返回時會回到 WebViewPage
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => StreamSettingsPage(initialStreamUrl: streamUrl),
          ),
        ).then((_) {
          debugPrint('✅ 跳轉完成');
        }).catchError((error) {
          debugPrint('❌ 跳轉失敗: $error');
        });
        
        // 發送確認響應給網頁（在跳轉後）
        Future.delayed(const Duration(milliseconds: 100), () {
          _sendResponseToWebView('success');
        });
        
      } catch (e) {
        debugPrint('❌ 跳轉過程中發生錯誤: $e');
        _sendResponseToWebView('error');
      }
    });
  }

  // 顯示最後接收的直播連結
  void _showStreamUrlDialog() {
    if (_lastReceivedStreamUrl == null) return;
    
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.history, color: Colors.blue),
              SizedBox(width: 8),
              Text('最後接收的直播連結'),
            ],
          ),
          content: SelectableText(
            _lastReceivedStreamUrl!,
            style: const TextStyle(
              fontFamily: 'monospace',
              fontSize: 14,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('關閉'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _copyToClipboard(_lastReceivedStreamUrl!);
              },
              child: const Text('複製'),
            ),
          ],
        );
      },
    );
  }

  // 通用數據顯示對話框
  void _showDataDialog(String data) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.message, color: Colors.green),
              SizedBox(width: 8),
              Text('來自網頁的數據'),
            ],
          ),
          content: Container(
            constraints: const BoxConstraints(
              maxHeight: 400,
              maxWidth: 500,
            ),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey[300]!),
                    ),
                    child: SelectableText(
                      data,
                      style: const TextStyle(
                        fontFamily: 'monospace',
                        fontSize: 14,
                      ),
                    ),
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Icon(Icons.access_time, size: 16, color: Colors.grey[600]),
                      const SizedBox(width: 4),
                      Text(
                        '接收時間: ${DateTime.now().toString().substring(0, 19)}',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('確定'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _sendTestDataToWebView();
              },
              child: const Text('測試回傳'),
            ),
          ],
        );
      },
    );
  }

  // 發送響應到WebView
  void _sendResponseToWebView(String status) async {
    if (!mounted) return;
    
    try {
      await _webViewController?.evaluateJavascript(source: '''
        console.log('Flutter confirmed receipt with status: $status');
        if (window.onFlutterResponse) {
          window.onFlutterResponse('$status');
        }
      ''');
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('已向網頁發送確認響應: $status'),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      debugPrint('Error sending response to WebView: $e');
    }
  }

  // 發送數據到WebView進行測試
  void _sendTestDataToWebView() async {
    await _webViewController?.evaluateJavascript(source: '''
      if (window.receiveFromFlutter) {
        window.receiveFromFlutter('來自Flutter的測試消息: ${DateTime.now().millisecondsSinceEpoch}');
      } else {
        console.log('Flutter回傳數據: 測試成功');
        alert('Flutter回傳數據: 測試成功');
      }
    ''');
  }

  void _copyToClipboard(String data) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('數據已準備複製'),
        backgroundColor: Colors.green,
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _showInstructions() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.code, color: Colors.blue),
              SizedBox(width: 8),
              Text('JavaScript 通信說明'),
            ],
          ),
          content: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  '您的前端代碼已完全兼容！可以使用以下方式：',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 12),
                _buildCodeBlock('''// 主要方式：sendDataToDart (推薦)
window.flutter_inappwebview.callHandler('sendDataToDart', streamUrl)
  .then(function(result) {
    console.log('Flutter response:', result);
  });

// 備用方式：FlutterChannel
window.FlutterChannel.postMessage(streamUrl);

// 通用方式：sendToFlutter
window.sendToFlutter(data);'''),
                const SizedBox(height: 16),
                const Text(
                  '您的sendDataToFlutter()函數可以直接使用：',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                _buildCodeBlock('''// 在網頁中調用您現有的函數
sendDataToFlutter();

// 或者直接發送數據
window.flutter_inappwebview.callHandler('sendDataToDart', 'your-stream-url');'''),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.green[50],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.green[200]!),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.check_circle, color: Colors.green[600], size: 20),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          '您的前端代碼已完全匹配Flutter端實現，可以直接使用！',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.green[700],
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('明白了'),
            ),
          ],
        );
      },
    );
  }

  Widget _buildCodeBlock(String code) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(8),
      ),
      child: SelectableText(
        code,
        style: const TextStyle(
          fontFamily: 'monospace',
          fontSize: 12,
        ),
      ),
    );
  }
} 
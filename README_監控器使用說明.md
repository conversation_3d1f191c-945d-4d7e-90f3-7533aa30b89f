# Flutter WebRTC 日志監控器使用說明

## 📋 功能概述

此監控器能夠實時監測您的Flutter WebRTC應用日志，當檢測到錯誤或警告時會立即通知您。

## 🔧 系統要求

- **作業系統**: Windows 10/11
- **Python**: 3.6+ 版本
- **Flutter SDK**: (可選) 用於直接監控`flutter logs`
- **PowerShell**: 7.x (您已安裝)

## 🚀 快速開始

### 方法1: 使用PowerShell腳本 (推薦)
```powershell
# 在PowerShell中執行
.\Start-LogMonitor.ps1
```

### 方法2: 使用批次文件
```cmd
# 雙擊執行或在命令提示字元中執行
start_log_monitor.bat
```

### 方法3: 直接執行Python腳本
```bash
# 確保已安裝依賴
pip install win10toast

# 執行監控腳本
python log_monitor.py
```

## 🎯 監控內容

### 錯誤檢測模式
- `error:` 或 `Error:`
- `exception:` 或 `Exception:`
- `failed:` 或 `Failed:`
- `crash` 相關
- `timeout` 相關
- `connection error` 連接錯誤
- `network error` 網絡錯誤
- `dio 錯誤` (Dio網絡庫錯誤)
- `推流錯誤` (推流相關錯誤)
- `連接失敗`
- `伺服器不可用`
- Flutter框架錯誤 (`flutter: error`, `E/flutter`)

### 警告檢測模式
- `warning:` 或 `Warning:`
- `warn:` 或 `Warn:`
- `⚠️` 警告符號
- `retry` 重試相關
- `重試` 中文重試
- Flutter警告 (`W/flutter`)

## 🔔 通知方式

1. **控制台彩色輸出**: 
   - 🚨 紅色顯示錯誤
   - ⚠️ 黃色顯示警告
   - 📊 藍色顯示狀態

2. **系統音效**: 檢測到錯誤時播放嗶聲

3. **Windows 系統通知**: 
   - 桌面彈出通知
   - 需要安裝 `win10toast` 依賴

4. **日志文件**: 所有日志自動保存到 `flutter_logs.txt`

## 📊 監控狀態

- 每30秒顯示一次統計信息
- 實時計數錯誤和警告數量
- 5秒通知冷卻時間（避免通知過於頻繁）

## 🎛️ 使用場景

### 場景1: 開發調試
```bash
# 1. 啟動監控器
python log_monitor.py

# 2. 在另一個終端啟動Flutter應用
flutter run
```

### 場景2: 生產環境監控
```bash
# 將Flutter應用日志輸出到文件，監控器會自動檢測
flutter run > flutter_logs.txt 2>&1 &
python log_monitor.py
```

### 場景3: 手動日志監控
```bash
# 手動將日志寫入 flutter_logs.txt
# 監控器會實時檢測新增的日志行
```

## ⚙️ 自定義配置

可以修改 `log_monitor.py` 中的以下參數：

```python
# 修改錯誤檢測模式
self.error_patterns = [
    # 添加您的自定義錯誤模式
    r'your_custom_error_pattern',
]

# 修改通知冷卻時間（秒）
self.notification_cooldown = 5

# 修改日志文件路徑
self.log_file_path = "your_custom_log_file.txt"
```

## 🛠️ 故障排除

### 問題1: 找不到Python
**解決方案**: 
1. 下載並安裝Python: https://www.python.org/downloads/
2. 安裝時勾選 "Add Python to PATH"
3. 重新啟動終端

### 問題2: 找不到Flutter命令
**解決方案**: 
- 監控器會自動切換到文件監控模式
- 手動啟動Flutter應用並將日志輸出到文件

### 問題3: 無法安裝win10toast
**解決方案**:
```bash
# 使用國內鏡像安裝
pip install win10toast -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

### 問題4: PowerShell執行策略限制
**解決方案**:
```powershell
# 臨時允許執行腳本
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

## 📝 日志文件說明

所有監控的日志都會保存在 `flutter_logs.txt` 文件中，格式為：
```
[2024-01-20 10:30:15] flutter: Your log message here
[2024-01-20 10:30:16] I/flutter: Debug information
```

## 🔄 停止監控

- 按 `Ctrl+C` 停止監控
- 或直接關閉終端窗口

## 📞 技術支援

如果遇到問題，請檢查：
1. Python版本是否為3.6+
2. 所有依賴是否正確安裝
3. Flutter SDK是否正確配置
4. PowerShell執行策略是否允許腳本執行

祝您使用愉快！ 🎉 
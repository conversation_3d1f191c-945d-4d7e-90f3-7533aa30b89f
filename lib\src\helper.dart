// Dart imports:
import 'dart:io';

// Flutter imports:
import 'package:flutter/foundation.dart';
import 'package:flutter_webrtc_plus/pixelfree_platform_interface.dart';

// Project imports:
import '../flutter_webrtc_plus.dart';
import 'native/audio_management.dart';

class Helper {
  static Future<List<MediaDeviceInfo>> enumerateDevices(String type) async {
    var devices = await navigator.mediaDevices.enumerateDevices();
    return devices.where((d) => d.kind == type).toList();
  }

  /// Return the available cameras
  ///
  /// Note: Make sure to call this gettet after
  /// navigator.mediaDevices.getUserMedia(), otherwise the devices will not be
  /// listed.
  static Future<List<MediaDeviceInfo>> get cameras =>
      enumerateDevices('videoinput');

  /// Return the available audiooutputs
  ///
  /// Note: Make sure to call this gettet after
  /// navigator.mediaDevices.getUserMedia(), otherwise the devices will not be
  /// listed.
  static Future<List<MediaDeviceInfo>> get audiooutputs =>
      enumerateDevices('audiooutput');

  /// For web implementation, make sure to pass the target deviceId
  static Future<bool> switchCamera(MediaStreamTrack track,
      [String? deviceId, MediaStream? stream]) async {
    if (track.kind != 'video') {
      throw 'The is not a video track => $track';
    }

    if (!kIsWeb) {
      return WebRTC.invokeMethod(
        'mediaStreamTrackSwitchCamera',
        <String, dynamic>{'trackId': track.id},
      ).then((value) => value ?? false);
    }

    if (deviceId == null) throw 'You need to specify the deviceId';
    if (stream == null) throw 'You need to specify the stream';

    var cams = await cameras;
    if (!cams.any((e) => e.deviceId == deviceId)) {
      throw 'The provided deviceId is not available, make sure to retreive the deviceId from Helper.cammeras()';
    }

    // stop only video tracks
    // so that we can recapture video track
    stream.getVideoTracks().forEach((track) {
      track.stop();
      stream.removeTrack(track);
    });

    var mediaConstraints = {
      'audio': false, // NO need to capture audio again
      'video': {'deviceId': deviceId}
    };

    var newStream = await openCamera(mediaConstraints);
    var newCamTrack = newStream.getVideoTracks()[0];

    await stream.addTrack(newCamTrack, addToNative: true);

    return Future.value(true);
  }

  static Future<void> setZoom(
      MediaStreamTrack videoTrack, double zoomLevel) async {
    if (WebRTC.platformIsAndroid || WebRTC.platformIsIOS) {
      await WebRTC.invokeMethod(
        'mediaStreamTrackSetZoom',
        <String, dynamic>{'trackId': videoTrack.id, 'zoomLevel': zoomLevel},
      );
    } else {
      throw Exception('setZoom only support for mobile devices!');
    }
  }

  /// Used to select a specific audio output device.
  ///
  /// Note: This method is only used for Flutter native,
  /// supported on iOS/Android/macOS/Windows.
  ///
  /// Android/macOS/Windows: Can be used to switch all output devices.
  /// iOS: you can only switch directly between the
  /// speaker and the preferred device
  /// web: flutter web can use RTCVideoRenderer.audioOutput instead
  static Future<void> selectAudioOutput(String deviceId) async {
    await navigator.mediaDevices
        .selectAudioOutput(AudioOutputOptions(deviceId: deviceId));
  }

  /// Set audio input device for Flutter native
  /// Note: The usual practice in flutter web is to use deviceId as the
  /// `getUserMedia` parameter to get a new audio track and replace it with the
  ///  audio track in the original rtpsender.
  static Future<void> selectAudioInput(String deviceId) =>
      NativeAudioManagement.selectAudioInput(deviceId);

  /// Enable or disable speakerphone
  /// for iOS/Android only
  static Future<void> setSpeakerphoneOn(bool enable) =>
      NativeAudioManagement.setSpeakerphoneOn(enable);

  /// Ensure audio session
  /// for iOS only
  static Future<void> ensureAudioSession() =>
      NativeAudioManagement.ensureAudioSession();

  /// Enable speakerphone, but use bluetooth if audio output device available
  /// for iOS/Android only
  static Future<void> setSpeakerphoneOnButPreferBluetooth() =>
      NativeAudioManagement.setSpeakerphoneOnButPreferBluetooth();

  /// To select a a specific camera, you need to set constraints
  /// eg.
  /// var constraints = {
  ///      'audio': true,
  ///      'video': {
  ///          'deviceId': Helper.cameras[0].deviceId,
  ///          }
  ///      };
  ///
  /// var stream = await Helper.openCamera(constraints);
  ///
  static Future<MediaStream> openCamera(Map<String, dynamic> mediaConstraints) {
    return navigator.mediaDevices.getUserMedia(mediaConstraints);
  }

  /// Set the volume for Flutter native
  static Future<void> setVolume(double volume, MediaStreamTrack track) =>
      NativeAudioManagement.setVolume(volume, track);

  /// Set the microphone mute/unmute for Flutter native
  static Future<void> setMicrophoneMute(bool mute, MediaStreamTrack track) =>
      NativeAudioManagement.setMicrophoneMute(mute, track);

  /// Set the audio configuration to for Android.
  /// Must be set before initiating a WebRTC session and cannot be changed
  /// mid session.
  static Future<void> setAndroidAudioConfiguration(
          AndroidAudioConfiguration androidAudioConfiguration) =>
      AndroidNativeAudioManagement.setAndroidAudioConfiguration(
          androidAudioConfiguration);

  /// After Android app finishes a session, on audio focus loss, clear the active communication device.
  static Future<void> clearAndroidCommunicationDevice() =>
      WebRTC.invokeMethod('clearAndroidCommunicationDevice');

  /// Set the audio configuration for iOS
  static Future<void> setAppleAudioConfiguration(
          AppleAudioConfiguration appleAudioConfiguration) =>
      AppleNativeAudioManagement.setAppleAudioConfiguration(
          appleAudioConfiguration);

  /// Set the audio configuration for iOS
  static Future<void> setAppleAudioIOMode(AppleAudioIOMode mode,
          {bool preferSpeakerOutput = false}) =>
      AppleNativeAudioManagement.setAppleAudioConfiguration(
          AppleNativeAudioManagement.getAppleAudioConfigurationForMode(mode,
              preferSpeakerOutput: preferSpeakerOutput));

  // Virtual Background & Blur using Google Mediapipe with GPU tasks, Waterbus
  static Future<bool> isGpuSupported() async {
    if (!WebRTC.platformIsAndroid) return false;

    final bool? isSupported = await WebRTC.invokeMethod("isGpuSupported");

    return isSupported ?? false;
  }

  // Enable Virtual Background with the provided background image and threshold confidence level.
  // The backgroundImage is expected to be in Uint8List format representing the image bytes.
  // The thresholdConfidence is an optional parameter with a default value of 0.7, which represents
  // the confidence level (ranging from 0 to 1) for selecting the foreground in the segmentation mask.
  static Future<void> enableVirtualBackground({
    required Uint8List backgroundImage,
    double thresholdConfidence = 0.7,
  }) async {
    if (!WebRTC.platformIsMobile && !WebRTC.platformIsMacOS) return;
    // Invoke the native method "enableVirtualBackground" through WebRTC plugin,
    // passing the backgroundImage and thresholdConfidence as parameters.
    WebRTC.invokeMethod("enableVirtualBackground", {
      "imageBytes": backgroundImage,
      "confidence": thresholdConfidence,
    });
  }

  // Disable Virtual Background feature.
  // This function invokes the native method "disableVirtualBackground" through WebRTC plugin.
  static Future<void> disableVirtualBackground() async {
    if (!WebRTC.platformIsMobile && !WebRTC.platformIsMacOS) return;

    WebRTC.invokeMethod("disableVirtualBackground");
  }

  // MARK: Adjust beauty value

  static Future<void> setThinFaceValue(double value) async {
    if (!platformSupportGPUPixel) return;

    WebRTC.invokeMethod("setThinValue", {"value": value});
  }

  static Future<void> setBigEyeValue(double value) async {
    if (!platformSupportGPUPixel) return;

    WebRTC.invokeMethod("setBigEyeValue", {"value": value});
  }

  static Future<void> setSmoothValue(double value) async {
    if (!platformSupportGPUPixel) return;

    WebRTC.invokeMethod("setSmoothValue", {"value": value});
  }

  static Future<void> setLipstickValue(double value) async {
    if (!platformSupportGPUPixel) return;

    WebRTC.invokeMethod("setLipstickValue", {"value": value});
  }

  static Future<void> setBlusherValue(double value) async {
    if (!platformSupportGPUPixel) return;

    WebRTC.invokeMethod("setBlusherValue", {"value": value});
  }

  static Future<void> setWhiteValue(double value) async {
    if (!platformSupportGPUPixel) return;

    WebRTC.invokeMethod("setWhiteValue", {"value": value});
  }

  // 设置美颜类型与程度
  static Future<void> pixelFreeSetBeautyFilterParam(
    PFBeautyFiterType type,
    double value,
  ) {
    return WebRTC.invokeMethod(
        'pixelFreeSetBeautyTypeParam', {'type': type.index,'value': value,});
  }

  // 设置滤镜类型与程度
  static Future<void> pixelFreeSetFilterParam(String filterName, double value) async {
    await WebRTC.invokeMethod(
        'pixelFreeSetFilterParam', {'filterName': filterName,'value': value,});
  }

  static Future<bool?> pixelFreeGetBeautyUseEffect() async {
    return await WebRTC.invokeMethod('pixelFreeGetBeautyUseEffect');
  }

  static Future<void> pixelFreeSetBeautyUseEffect(bool enabled) async {
    await WebRTC.invokeMethod('pixelFreeSetBeautyUseEffect', {
      'enabled': enabled,
    });
  }

  static Future<void> pixelFreeSetStickerBundle(String bundlePath) async {
    await WebRTC.invokeMethod('pixelFreeSetStickerBundle', {
      'bundlePath': bundlePath,
    });
  }
  

  static bool get platformSupportGPUPixel => !WebRTC.platformIsWeb;

  static bool get platformIsDarwin => Platform.isIOS || Platform.isMacOS;
}

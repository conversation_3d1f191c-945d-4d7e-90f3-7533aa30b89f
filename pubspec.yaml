name: flutter_webrtc_plus
description: Flutter WebRTC plugin for iOS/Android/Destkop/Web, based on GoogleWebRTC.
version: 0.12.1+7
homepage: https://github.com/waterbustech/flutter-webrtc-plus

environment:
  sdk: '>=3.3.0 <4.0.0'
  flutter: '>=1.22.0'

dependencies:
  collection: ^1.17.1
  dart_webrtc_plus: ^1.4.10+4
  flutter:
    sdk: flutter
  path_provider: ^2.1.2
  web: ^1.0.0
  webrtc_interface_plus: ^1.2.0+1
  shared_preferences: ^2.2.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  import_sorter: ^4.6.0
  lints: ^4.0.0
  pedantic: ^1.11.1
  test: any

flutter:
  plugin:
    platforms:
      android:
        package: com.cloudwebrtc.webrtc
        pluginClass: FlutterWebRTCPlugin
      ios:
        pluginClass: FlutterWebRTCPlugin
      macos:
        pluginClass: FlutterWebRTCPlugin
      windows:
        pluginClass: FlutterWebRTCPlugin
      linux:
        pluginClass: FlutterWebRTCPlugin
      elinux:
        pluginClass: FlutterWebRTCPlugin
        
  assets:
    - assets/icons/
    - assets/bundle/
//
//  RTCPixelfreeBeautyFilter.h
//  flutter_webrtc_plus
//
//  Created by sunmu on 2025/5/7.
//

#import <Foundation/Foundation.h>
#import <CoreVideo/CoreVideo.h>
#import "RTCBeautyFilter.h"

NS_ASSUME_NONNULL_BEGIN

@interface RTCPixelfreeBeautyFilter : NSObject

@property (nonatomic, weak) id<RTCBeautyFilterDelegate> delegate;

- (instancetype)initWithDelegate:(id<RTCBeautyFilterDelegate>)delegate;
- (void)releaseInstance;
- (void)processVideoFrame:(CVPixelBufferRef)imageBuffer;

- (void)pixelFreeSetBeautyFiterParam:(int)key value:(void *)value;

- (BOOL)pixelFreeGetBeautyUseEffect;

- (void)pixelFreeSetBeautyUseEffect:(BOOL)isUse;

- (void)pixelFreeSetStickerBundle:(NSString *)path;
@end


NS_ASSUME_NONNULL_END
